package com.jkga.wyyjs.service;

import com.jkga.wyyjs.model.CommonResult;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * TeamManagementService增加管理员测试类
 * 
 * @Author：clyde
 * @Date：2025/7/18
 */
@SpringBootTest
@ActiveProfiles("dev")
public class TeamManagementServiceAddManagerTest {

    @Autowired
    private TeamManagementService teamManagementService;

    /**
     * 测试增加物业管理员
     */
    @Test
    public void testAddPropertyManager() {
        String areaId = "test_area_001";
        String userId = "test_user_001";
        String departmentId = "test_dept_001";
        String position = "物业经理";
        List<String> roleIds = Arrays.asList("role_001", "role_002");
        String placeId = null;
        Integer type = 0; // 物业
        
        System.out.println("测试增加物业管理员");
        System.out.println("参数: areaId=" + areaId + ", userId=" + userId + ", departmentId=" + departmentId);
        System.out.println("position=" + position + ", roleIds=" + roleIds + ", type=" + type);
        
        try {
            CommonResult result = teamManagementService.addManager(areaId, userId, departmentId, position, roleIds, placeId, type);
            
            System.out.println("返回结果: " + result);
            System.out.println("状态码: " + result.getCode());
            System.out.println("消息: " + result.getMsg());
            
            if (result.getCode() == 200 && result.getData() != null) {
                @SuppressWarnings("unchecked")
                Map<String, Object> managerData = (Map<String, Object>) result.getData();
                
                System.out.println("管理员信息:");
                System.out.println("  ID: " + managerData.get("id"));
                System.out.println("  用户ID: " + managerData.get("userId"));
                System.out.println("  用户名: " + managerData.get("userName"));
                System.out.println("  手机号: " + managerData.get("phone"));
                System.out.println("  部门ID: " + managerData.get("departmentId"));
                System.out.println("  职位: " + managerData.get("position"));
                System.out.println("  小区ID: " + managerData.get("areaId"));
                System.out.println("  类型: " + managerData.get("type"));
            }
            
        } catch (Exception e) {
            System.err.println("测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试增加中介管理员
     */
    @Test
    public void testAddIntermediaryManager() {
        String areaId = null;
        String userId = "test_user_002";
        String departmentId = "test_dept_002";
        String position = "中介经理";
        List<String> roleIds = Arrays.asList("role_003");
        String placeId = "test_place_001";
        Integer type = 1; // 中介
        
        System.out.println("测试增加中介管理员");
        System.out.println("参数: placeId=" + placeId + ", userId=" + userId + ", departmentId=" + departmentId);
        System.out.println("position=" + position + ", roleIds=" + roleIds + ", type=" + type);
        
        try {
            CommonResult result = teamManagementService.addManager(areaId, userId, departmentId, position, roleIds, placeId, type);
            
            System.out.println("返回结果: " + result);
            System.out.println("状态码: " + result.getCode());
            System.out.println("消息: " + result.getMsg());
            
            if (result.getCode() == 200 && result.getData() != null) {
                @SuppressWarnings("unchecked")
                Map<String, Object> managerData = (Map<String, Object>) result.getData();
                
                System.out.println("管理员信息:");
                System.out.println("  ID: " + managerData.get("id"));
                System.out.println("  用户ID: " + managerData.get("userId"));
                System.out.println("  用户名: " + managerData.get("userName"));
                System.out.println("  手机号: " + managerData.get("phone"));
                System.out.println("  部门ID: " + managerData.get("departmentId"));
                System.out.println("  职位: " + managerData.get("position"));
                System.out.println("  场所ID: " + managerData.get("placeId"));
                System.out.println("  类型: " + managerData.get("type"));
            }
            
        } catch (Exception e) {
            System.err.println("测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试参数验证
     */
    @Test
    public void testAddManagerParameterValidation() {
        System.out.println("测试参数验证");
        
        // 测试type参数错误
        CommonResult result1 = teamManagementService.addManager("area001", "user001", null, null, Arrays.asList("role001"), null, 2);
        System.out.println("type参数错误测试: " + result1.getMsg());
        assert result1.getCode() == 400 : "type参数错误应返回400";
        
        // 测试物业类型areaId为空
        CommonResult result2 = teamManagementService.addManager(null, "user001", null, null, Arrays.asList("role001"), null, 0);
        System.out.println("物业类型areaId为空测试: " + result2.getMsg());
        assert result2.getCode() == 400 : "物业类型areaId为空应返回400";
        
        // 测试中介类型placeId为空
        CommonResult result3 = teamManagementService.addManager(null, "user001", null, null, Arrays.asList("role001"), null, 1);
        System.out.println("中介类型placeId为空测试: " + result3.getMsg());
        assert result3.getCode() == 400 : "中介类型placeId为空应返回400";
        
        // 测试userId为空
        CommonResult result4 = teamManagementService.addManager("area001", null, null, null, Arrays.asList("role001"), null, 0);
        System.out.println("userId为空测试: " + result4.getMsg());
        assert result4.getCode() == 400 : "userId为空应返回400";
        
        // 测试roleIds为空
        CommonResult result5 = teamManagementService.addManager("area001", "user001", null, null, null, null, 0);
        System.out.println("roleIds为空测试: " + result5.getMsg());
        assert result5.getCode() == 400 : "roleIds为空应返回400";
        
        System.out.println("参数验证测试通过！");
    }

    /**
     * 测试用户不存在的情况
     */
    @Test
    public void testAddManagerUserNotExists() {
        String areaId = "test_area_001";
        String userId = "non_existent_user";
        String departmentId = null;
        String position = null;
        List<String> roleIds = Arrays.asList("role_001");
        String placeId = null;
        Integer type = 0;
        
        System.out.println("测试用户不存在的情况");
        
        try {
            CommonResult result = teamManagementService.addManager(areaId, userId, departmentId, position, roleIds, placeId, type);
            
            System.out.println("返回结果: " + result);
            System.out.println("状态码: " + result.getCode());
            System.out.println("消息: " + result.getMsg());
            
            // 验证返回的是用户不存在错误
            if (result.getCode() == 405) {
                System.out.println("正确返回用户不存在错误");
            } else {
                System.out.println("预期返回405，实际返回: " + result.getCode());
            }
            
        } catch (Exception e) {
            System.err.println("测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试重复添加管理员
     */
    @Test
    public void testAddDuplicateManager() {
        String areaId = "test_area_001";
        String userId = "existing_manager_user";
        String departmentId = "test_dept_001";
        String position = "经理";
        List<String> roleIds = Arrays.asList("role_001");
        String placeId = null;
        Integer type = 0;
        
        System.out.println("测试重复添加管理员");
        
        try {
            // 第一次添加
            CommonResult result1 = teamManagementService.addManager(areaId, userId, departmentId, position, roleIds, placeId, type);
            System.out.println("第一次添加结果: " + result1.getMsg());
            
            // 第二次添加（应该失败）
            CommonResult result2 = teamManagementService.addManager(areaId, userId, departmentId, position, roleIds, placeId, type);
            System.out.println("第二次添加结果: " + result2.getMsg());
            
            if (result2.getCode() == 402) {
                System.out.println("正确返回数据已存在错误");
            } else {
                System.out.println("预期返回402，实际返回: " + result2.getCode());
            }
            
        } catch (Exception e) {
            System.err.println("测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
