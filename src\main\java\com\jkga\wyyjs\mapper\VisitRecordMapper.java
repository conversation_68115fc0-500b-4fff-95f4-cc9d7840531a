package com.jkga.wyyjs.mapper;

import com.jkga.wyyjs.model.entity.VisitRecordEntity;
import com.jkga.wyyjs.model.vo.VisitRecordVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author：clyde
 * @Date：2025/6/4 10:33
 */

@Mapper
public interface VisitRecordMapper {

    boolean insertVisitRecord(VisitRecordEntity visitRecordEntity);

    List<VisitRecordEntity> getRecordList(@Param("areaId") String areaId,
                                          @Param("type") Integer type,
                                          @Param("searchParam") String searchParam,
                                          @Param("currIndex") int currIndex,
                                          @Param("pageSize") int pageSize);

    int countRecord(@Param("areaId") String areaId,
                    @Param("type") Integer type,
                    @Param("searchParam") String searchParam);

}
