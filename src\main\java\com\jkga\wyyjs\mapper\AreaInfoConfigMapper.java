package com.jkga.wyyjs.mapper;

import com.jkga.wyyjs.model.entity.AreaInfoConfigEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * @Author：clyde
 * @Date：2025/2/11 10:59
 */
@Mapper
public interface AreaInfoConfigMapper {

    AreaInfoConfigEntity getAreaInfoConfigByType(@Param("areaId") String areaId, @Param("type") int type);

    boolean addAreaInfoConfig(AreaInfoConfigEntity areaInfoConfigEntity);

    boolean updAreaInfoConfig(AreaInfoConfigEntity areaInfoConfigEntity);

    List<AreaInfoConfigEntity> getAreaInfoConfig(@Param("areaId") String areaId);

}
