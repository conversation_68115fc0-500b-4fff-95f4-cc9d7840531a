package com.jkga.wyyjs.mapper;

import com.jkga.wyyjs.model.entity.PermissionEntity;
import com.jkga.wyyjs.model.entity.UserEntity;
import com.jkga.wyyjs.model.vo.UserAuthVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * ${description}
 *
 * <AUTHOR>
 */
@Mapper
public interface UserMapper {

    UserEntity selectUserById(@Param("id") String id);

    void insertLoginUser(UserEntity userEntity);

    UserEntity selectUserByOpenid(@Param("openid") String openid);

    void updateUserPhone(@Param("id") String id,
                         @Param("newPhone") String newPhone,
                         @Param("nowDateTimeStr") String nowDateTimeStr);

    void updateUserLoginTime(@Param("id") String id,
                             @Param("nowDateTimeStr") String nowDateTimeStr);

    void updateUserAuthInfo(UserAuthVO authVO);

    void updateUserFaceImg(@Param("userId") String userId,
                           @Param("url") String url);

    int countUserAuth(@Param("name") String name,
                      @Param("identity") String identity);

    void insertDeclareUser(UserEntity userEntity);

    UserEntity getDeclareUserByPhone(String phone);

    void updateLoginUserOpenid(@Param("id") String id,
                               @Param("openid") String openid);

    UserEntity getUserByPhone(String phone);

    int countUserIdentityIsAuth(@Param("userId") String userId,
                                @Param("identity") String identity);

    UserEntity getUserByIdentity(String identity);

    UserEntity getUserByIdentityOrPhone(@Param("identity") String identity,
                                        @Param("phone") String phone);

    boolean smrzUser(UserEntity userEntity);

    boolean syncAddStoremgmtUser(UserEntity userEntity);

    boolean syncUpdStoremgmtUser(UserEntity userEntity);

    boolean updateUserDossierId(@Param("id") String id,
                                @Param("dossierId") String dossierId);

    boolean addImportCarUser(UserEntity userEntity);

    boolean syncAddQcbUser(UserEntity userEntity);

    boolean syncUpdQcbUser(UserEntity userEntity);

    UserEntity getUserByDossierId(@Param("dossier_id") String dossier_id);

    List<String> getUserPermissions(@Param("userId") String userId, @Param("area_id") String area_id, @Param("place_id") String place_id);

    List<PermissionEntity> getUserPermissionDetails(@Param("userId") String userId, @Param("area_id") String area_id);

    /**
     * 删除用户（逻辑删除）
     */
    boolean deleteUser(@Param("id") String id);

    /**
     * 将用户的open_id和phone字段设置为null
     */
    boolean clearUserOpenIdAndPhone(@Param("id") String id);

}
