package com.jkga.wyyjs.mapper;

import com.jkga.wyyjs.model.entity.FileEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * ${description}
 *
 * <AUTHOR>
 * @date 2025/1/17 10:44
 */
@Mapper
public interface FileMapper {

    FileEntity selectExistsFile(@Param("checksum") String checksum,
                                @Param("tbId") String tbId,
                                @Param("tbType") String tbType);

    void insertFile(FileEntity fileEntity);
}
