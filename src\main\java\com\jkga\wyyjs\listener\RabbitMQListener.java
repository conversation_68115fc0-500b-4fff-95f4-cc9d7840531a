package com.jkga.wyyjs.listener;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.jkga.wyyjs.mapper.HouseMapper;
import com.jkga.wyyjs.mapper.UserMapper;
import com.jkga.wyyjs.model.entity.AlarmQcbEntity;
import com.jkga.wyyjs.model.entity.UserEntity;
import com.jkga.wyyjs.utils.HouseUtil;
import com.jkga.wyyjs.utils.SnowflakeUtil;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * @Author：clyde
 * @Date：2024/10/25 9:12
 */
@Component
@Slf4j
public class RabbitMQListener {

    @Autowired
    private UserMapper userMapper;
    @Autowired
    private HouseMapper houseMapper;
    @Autowired
    private HouseUtil houseUtil;


    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(value = "QUEUE_QCB_UNRELATED"),
            exchange = @Exchange(value = "EXCHANGE_QCB", type = "direct"),
            key = "ROUTING_KEY_QCB_UNRELATED"))
    public void handMemberMessage(Message message, Channel channel) throws IOException {
        String msg = new String(message.getBody());
        log.info("========接收到清查宝数据:" + msg);

        JSONObject data = JSONUtil.parseObj(msg);
        Integer type = data.getInt("type");
        String area_id = data.getStr("area_id");
        String dossier_id = data.getStr("dossier_id");
        String id_card = data.getStr("id_card");
        String user_name = data.getStr("user_name");
        String phone = data.getStr("phone");
        String car_no = data.getStr("car_no");
        String house_id = data.getStr("house_id");
        String face_image_url = data.getStr("face_image_url");
        String update_user = data.getStr("update_user");
        if (type.intValue() == 0) {
            UserEntity userEntity = new UserEntity();
            userEntity.setName(user_name);
            userEntity.setIdentity(id_card);
            userEntity.setIdentityType("身份证");
            userEntity.setPhone(phone);
            userEntity.setFaceImgUrl(face_image_url);
            userEntity.setDossierId(dossier_id);
            houseUtil.syncQcbUser(userEntity);

            UserEntity ex = userMapper.getUserByDossierId(dossier_id);
            if (ex != null) {
                houseUtil.addHouseMember(house_id, ex, "1");
            }
        } else if (type.intValue() == 2) {
            String[] car_no_arr = car_no.split(",");
            for (String car : car_no_arr) {
                AlarmQcbEntity ex = houseMapper.getAreaAlarmQcbByDIdAndCar(area_id, dossier_id, car);
                if (ex == null) {
                    AlarmQcbEntity addAlarmQcbEntity = new AlarmQcbEntity();
                    String alarmQcbId = SnowflakeUtil.generateId();
                    addAlarmQcbEntity.setId(alarmQcbId);
                    addAlarmQcbEntity.setAreaId(area_id);
                    addAlarmQcbEntity.setType(2);
                    addAlarmQcbEntity.setDossierId(dossier_id);
                    addAlarmQcbEntity.setIdCard(id_card);
                    addAlarmQcbEntity.setUserName(user_name);
                    addAlarmQcbEntity.setPhone(phone);
                    addAlarmQcbEntity.setCarNo(car);
                    addAlarmQcbEntity.setHouseId(house_id);
                    addAlarmQcbEntity.setUpdateUser(update_user);
                    addAlarmQcbEntity.setStatus(0);
                    addAlarmQcbEntity.setFaceImageUrl(face_image_url);

                    houseMapper.addAlarmQcb(addAlarmQcbEntity);
                }
            }
        }


        // 确认消息
//        channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
    }

//    @RabbitListener(bindings = @QueueBinding(
//            value = @Queue(value = "QUEUE_JQL_UNRELATED666"),
//            exchange = @Exchange(value = "EXCHANGE_QCB666", type = "direct"),
//            key = "ROUTING_KEY_JQL_UNRELATED666"))
//    public void testMessage(Message message, Channel channel) throws IOException {
//        String msg = new String(message.getBody());
//        log.info("接收了一条mq推送信息:" + msg);
//        // 确认消息
////        channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
//    }

//    @RabbitListener(queues = TopicRabbitConfig.HOUSE_QUEUE)
//    public void handTestMessage(Message message, Channel channel) throws IOException {
//        log.info("======");
////        String msg = new String(message.getBody());
////        JSONObject jsonObject= JSONObject.parseObject(msg);
////        log.info("======"+jsonObject.toJSONString());
////        houseMapper.addRabbitMQHouseMemberData("member",msg,1,null);
//
//        // 在处理完消息后手动进行确认
//        /*
//         * 参数1： 消息标签
//         * 参数2： 是否批量进行确认
//         * */
//        channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
//        // 在处理完消息后手动进行确认
//        /*
//         * 参数1： 消息标签
//         * 参数2： 是否requeue，true则重新入队列，否则丢弃或者进入死信队列
//         * */
////        channel.basicReject(message.getMessageProperties().getDeliveryTag(), true);
//    }


}
