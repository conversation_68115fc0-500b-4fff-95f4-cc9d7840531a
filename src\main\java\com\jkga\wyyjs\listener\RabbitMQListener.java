package com.jkga.wyyjs.listener;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.jkga.wyyjs.mapper.HouseMapper;
import com.jkga.wyyjs.mapper.UserMapper;
import com.jkga.wyyjs.model.entity.AlarmQcbEntity;
import com.jkga.wyyjs.model.entity.UserEntity;
import com.jkga.wyyjs.service.UserManagerService;
import com.jkga.wyyjs.utils.HouseUtil;
import com.jkga.wyyjs.utils.SnowflakeUtil;
import org.apache.commons.lang3.StringUtils;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * @Author：clyde
 * @Date：2024/10/25 9:12
 */
@Component
@Slf4j
public class RabbitMQListener {

    @Autowired
    private UserMapper userMapper;
    @Autowired
    private HouseMapper houseMapper;
    @Autowired
    private HouseUtil houseUtil;
    @Autowired
    private UserManagerService userManagerService;


    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(value = "QUEUE_QCB_UNRELATED"),
            exchange = @Exchange(value = "EXCHANGE_QCB", type = "direct"),
            key = "ROUTING_KEY_QCB_UNRELATED"))
    public void handMemberMessage(Message message, Channel channel) throws IOException {
        String msg = new String(message.getBody());
        log.info("========接收到清查宝数据:" + msg);

        JSONObject data = JSONUtil.parseObj(msg);
        Integer type = data.containsKey("type") ? data.getInt("type") : -1;
        String area_id = data.containsKey("area_id") ? data.getStr("area_id") : null;
        String dossier_id = data.containsKey("dossier_id") ? data.getStr("dossier_id") : null;
        String id_card = data.containsKey("id_card") ? data.getStr("id_card") : null;
        String user_name = data.containsKey("user_name") ? data.getStr("user_name") : null;
        String phone = data.containsKey("phone") ? data.getStr("phone") : null;
        String car_no = data.containsKey("car_no") ? data.getStr("car_no") : null;
        String house_id = data.containsKey("house_id") ? data.getStr("house_id") : null;
        String face_image_url = data.containsKey("face_image_url") ? data.getStr("face_image_url") : null;
        String update_user = data.containsKey("update_user") ? data.getStr("update_user") : null;
        if (type.intValue() == 0) {
            UserEntity userEntity = new UserEntity();
            userEntity.setName(user_name);
            userEntity.setIdentity(id_card);
            userEntity.setIdentityType("身份证");
            userEntity.setPhone(phone);
            userEntity.setFaceImgUrl(face_image_url);
            userEntity.setDossierId(dossier_id);
            houseUtil.syncQcbUser(userEntity);

            UserEntity ex = userMapper.getUserByDossierId(dossier_id);
            if (ex != null) {
                houseUtil.addHouseMember(house_id, ex, "1");
            }
        } else if (type.intValue() == 2) {
            if (StringUtils.isNotBlank(car_no)) {
                String[] car_no_arr = car_no.split(",");
                for (String car : car_no_arr) {
                    AlarmQcbEntity ex = houseMapper.getAreaAlarmQcbByDIdAndCar(area_id, dossier_id, car);
                    if (ex == null) {
                        AlarmQcbEntity addAlarmQcbEntity = new AlarmQcbEntity();
                        String alarmQcbId = SnowflakeUtil.generateId();
                        addAlarmQcbEntity.setId(alarmQcbId);
                        addAlarmQcbEntity.setAreaId(area_id);
                        addAlarmQcbEntity.setType(2);
                        addAlarmQcbEntity.setDossierId(dossier_id);
                        addAlarmQcbEntity.setIdCard(id_card);
                        addAlarmQcbEntity.setUserName(user_name);
                        addAlarmQcbEntity.setPhone(phone);
                        addAlarmQcbEntity.setCarNo(car);
                        addAlarmQcbEntity.setHouseId(house_id);
                        addAlarmQcbEntity.setUpdateUser(update_user);
                        addAlarmQcbEntity.setStatus(0);
                        addAlarmQcbEntity.setFaceImageUrl(face_image_url);

                        houseMapper.addAlarmQcb(addAlarmQcbEntity);
                    }
                }
            } else {
                AlarmQcbEntity ex = houseMapper.getAreaAlarmQcbByDIdAndCar(area_id, dossier_id, null);
                if (ex == null) {
                    AlarmQcbEntity addAlarmQcbEntity = new AlarmQcbEntity();
                    String alarmQcbId = SnowflakeUtil.generateId();
                    addAlarmQcbEntity.setId(alarmQcbId);
                    addAlarmQcbEntity.setAreaId(area_id);
                    addAlarmQcbEntity.setType(2);
                    addAlarmQcbEntity.setDossierId(dossier_id);
                    addAlarmQcbEntity.setIdCard(id_card);
                    addAlarmQcbEntity.setUserName(user_name);
                    addAlarmQcbEntity.setPhone(phone);
                    addAlarmQcbEntity.setHouseId(house_id);
                    addAlarmQcbEntity.setUpdateUser(update_user);
                    addAlarmQcbEntity.setStatus(0);
                    addAlarmQcbEntity.setFaceImageUrl(face_image_url);

                    houseMapper.addAlarmQcb(addAlarmQcbEntity);
                }
            }

        }
        // 确认消息
//        channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
    }

    /**
     * 监听用户管理消息队列
     * 处理物业管理员和中介管理员的添加和删除
     */
    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(value = "QUEUE_JQL_WYYJSADMIN"),
            exchange = @Exchange(value = "EXCHANGE_QCB", type = "direct"),
            key = "ROUTING_KEY_JQL_WYYJSADMIN"))
    public void handleUserManagerMessage(Message message, Channel channel) throws IOException {
        String msg = new String(message.getBody());
        log.info("========接收到用户管理数据:" + msg);

        try {
            // 第一步：JSON序列化校验
            JSONObject data;
            try {
                data = JSONUtil.parseObj(msg);
            } catch (Exception e) {
                log.error("JSON序列化失败，丢弃消息: {}", msg, e);
                // 直接确认消息，不重新入队
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                return;
            }

            // 第二步：数据完整性校验
            String validationResult = validateMessageData(data);
            if (validationResult != null) {
                log.error("数据校验失败，丢弃消息: {} - 错误: {}", msg, validationResult);
                // 直接确认消息，不重新入队
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                return;
            }

            // 第三步：调用服务处理业务逻辑
            userManagerService.processUserManagerMessage(data);

            // 确认消息
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
            log.info("用户管理数据处理成功");

        } catch (Exception e) {
            log.error("处理用户管理数据失败", e);
            // 业务逻辑处理失败，拒绝消息并重新入队
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, true);
        }
    }

    /**
     * 校验消息数据的完整性和格式
     *
     * @param data JSON数据对象
     * @return 校验失败时返回错误信息，校验成功返回null
     */
    private String validateMessageData(JSONObject data) {
        try {
            // 校验type字段
            Integer type = data.getInt("type");
            if (type == null) {
                return "type字段不能为空";
            }
            if (type != 0 && type != 1) {
                return "type字段值必须为0或1";
            }

            // 校验id_card字段
            String idCard = data.getStr("id_card");
            if (StringUtils.isBlank(idCard)) {
                return "id_card字段不能为空";
            }
            if (!isValidIdCard(idCard)) {
                return "id_card格式不正确";
            }

            // 校验user_name字段
            String userName = data.getStr("user_name");
            if (StringUtils.isBlank(userName)) {
                return "user_name字段不能为空";
            }

            // 校验phone字段
            String phone = data.getStr("phone");
            if (StringUtils.isBlank(phone)) {
                return "phone字段不能为空";
            }

            // 校验status字段
            String status = data.getStr("status");
            if (StringUtils.isBlank(status)) {
                return "status字段不能为空";
            }
            if (!"0".equals(status) && !"1".equals(status)) {
                return "status字段值必须为0或1";
            }

            // 校验update_user字段
            String updateUser = data.getStr("update_user");
            if (StringUtils.isBlank(updateUser)) {
                return "update_user字段不能为空";
            }

            // 校验area_id字段（现在对所有type都是必填）
            String areaId = data.getStr("area_id");
            if (StringUtils.isBlank(areaId)) {
                return "area_id字段不能为空";
            }

            // position字段可以为空，不做校验

            return null; // 校验通过
        } catch (Exception e) {
            return "数据格式错误: " + e.getMessage();
        }
    }

    /**
     * 身份证号码格式校验
     *
     * @param idCard 身份证号码
     * @return 格式是否正确
     */
    private boolean isValidIdCard(String idCard) {
        if (StringUtils.isBlank(idCard)) {
            return false;
        }

        // 18位身份证号码正则表达式
        String regex18 = "^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$";
        // 15位身份证号码正则表达式
        String regex15 = "^[1-9]\\d{5}\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}$";

        return idCard.matches(regex18) || idCard.matches(regex15);
    }

//    @RabbitListener(queues = TopicRabbitConfig.HOUSE_QUEUE)
//    public void handTestMessage(Message message, Channel channel) throws IOException {
//        log.info("======");
////        String msg = new String(message.getBody());
////        JSONObject jsonObject= JSONObject.parseObject(msg);
////        log.info("======"+jsonObject.toJSONString());
////        houseMapper.addRabbitMQHouseMemberData("member",msg,1,null);
//
//        // 在处理完消息后手动进行确认
//        /*
//         * 参数1： 消息标签
//         * 参数2： 是否批量进行确认
//         * */
//        channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
//        // 在处理完消息后手动进行确认
//        /*
//         * 参数1： 消息标签
//         * 参数2： 是否requeue，true则重新入队列，否则丢弃或者进入死信队列
//         * */
////        channel.basicReject(message.getMessageProperties().getDeliveryTag(), true);
//    }


}
