package com.jkga.wyyjs.mapper;

import com.jkga.wyyjs.model.entity.RoleEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author：clyde
 * @Date：2025/2/6 11:06
 */
@Mapper
public interface RoleMapper {

    RoleEntity getAreaRoleByName(@Param("areaId") String areaId, @Param("roleName") String roleName);

    boolean insertAreaRole(RoleEntity roleEntity);

    List<RoleEntity> getAreaRoleList(@Param("areaId") String areaId);

    RoleEntity getRoleById(@Param("roleId") String roleId);

}
