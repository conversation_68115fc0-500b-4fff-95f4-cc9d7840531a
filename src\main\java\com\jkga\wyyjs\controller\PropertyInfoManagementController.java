package com.jkga.wyyjs.controller;

import com.jkga.wyyjs.aspect.RecordLog;
import com.jkga.wyyjs.constant.UserFunctionEnum;
import com.jkga.wyyjs.model.CommonCode;
import com.jkga.wyyjs.model.CommonResult;
import com.jkga.wyyjs.model.vo.AreaInfoConfigVO;
import com.jkga.wyyjs.service.PropertyInfoManagementService;
import com.jkga.wyyjs.utils.PermissionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

/**
 * 小区信息管理
 *
 * @Author：clyde
 * @Date：2025/2/26 15:46
 */
@Slf4j
@CrossOrigin
@RestController
@RequestMapping("propertyInfoManagement")
public class PropertyInfoManagementController {

    @Autowired
    HttpServletRequest request;
    @Autowired
    private PropertyInfoManagementService propertyInfoManagementService;
    @Autowired
    private PermissionUtils permissionUtils;


    /**
     * 编辑小区档案信息
     *
     * @Author：clyde
     * @Date：2025/2/26 10:26
     */
    @RecordLog(optModel = "小区信息管理", optDesc = "编辑小区档案信息")
    @RequestMapping(value = "editAreaArchives", method = RequestMethod.POST)
    public CommonResult editAreaArchives(@RequestBody @Valid AreaInfoConfigVO areaInfoConfigVO) {

        //权限控制
        if (!permissionUtils.judeUserPermission(request.getHeader("uid"), request.getHeader("area_id"), UserFunctionEnum.WYXXWH, request.getRequestURI())) {

            return new CommonResult(CommonCode.NO_PERMISSION);
        }
        try {
            String creatorId = request.getHeader("uid");
            areaInfoConfigVO.setOperatorId(creatorId);
            return propertyInfoManagementService.editAreaArchives(areaInfoConfigVO);
        } catch (Exception e) {
            return new CommonResult(CommonCode.SYSTEM_ERROR);
        }
    }


    /**
     * 编辑小区物业档案
     *
     * @Author：clyde
     * @Date：2025/2/26 15:42
     */
    @RecordLog(optModel = "小区信息管理", optDesc = "编辑小区物业档案")
    @RequestMapping(value = "editPropertyArchives", method = RequestMethod.POST)
    public CommonResult editPropertyArchives(@RequestBody @Valid AreaInfoConfigVO areaInfoConfigVO) {
        //权限控制
        if (!permissionUtils.judeUserPermission(request.getHeader("uid"), request.getHeader("area_id"), UserFunctionEnum.WYXXWH, request.getRequestURI())) {
            return new CommonResult(CommonCode.NO_PERMISSION);
        }
        try {
            String creatorId = request.getHeader("uid");
            areaInfoConfigVO.setOperatorId(creatorId);
            return propertyInfoManagementService.editPropertyArchives(areaInfoConfigVO);
        } catch (Exception e) {
            return new CommonResult(CommonCode.SYSTEM_ERROR);
        }
    }


    /**
     * 编辑小区物业费收费
     *
     * @Author：clyde
     * @Date：2025/2/26 16:40
     */
    @RecordLog(optModel = "小区信息管理", optDesc = "编辑小区物业费收费")
    @RequestMapping(value = "editPropertyFee", method = RequestMethod.POST)
    public CommonResult editPropertyFee(@RequestBody @Valid AreaInfoConfigVO areaInfoConfigVO) {

        //权限控制
        if (!permissionUtils.judeUserPermission(request.getHeader("uid"), request.getHeader("area_id"), UserFunctionEnum.WYFGL, request.getRequestURI())) {

            return new CommonResult(CommonCode.NO_PERMISSION);
        }
        try {
            String creatorId = request.getHeader("uid");
            areaInfoConfigVO.setOperatorId(creatorId);
            return propertyInfoManagementService.editPropertyFee(areaInfoConfigVO);
        } catch (Exception e) {
            return new CommonResult(CommonCode.SYSTEM_ERROR);
        }
    }

    /**
     * 获取当前小区物业信息
     *
     * @Author：clyde
     * @Date：2025/2/27 13:55
     */
    @RequestMapping(value = "getAreaInfoConfig", method = RequestMethod.GET)
    public CommonResult getAreaInfoConfig(String areaId) {

        try {
            return propertyInfoManagementService.getAreaInfoConfig(areaId);
        } catch (Exception e) {
            e.printStackTrace();
            return new CommonResult(CommonCode.SYSTEM_ERROR);
        }
    }


}
