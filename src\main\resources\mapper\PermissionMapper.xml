<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jkga.wyyjs.mapper.PermissionMapper">

    <resultMap id="PermissionResultMap" type="com.jkga.wyyjs.model.entity.PermissionEntity">
        <id     property="id"     column="id"     />
        <result property="name"   column="name"   />
        <result property="description"  column="description"   />
        <result property="createTime"   column="create_time"   />
        <result property="deleteStatus"   column="delete_status"   />
        <result property="permissionType"   column="permission_type"   />
        <result property="code"   column="code"   />
    </resultMap>

    <select id="getPermissionList" resultMap="PermissionResultMap">
        SELECT * from "permission" where  "delete_status" = 0
    </select>



</mapper>