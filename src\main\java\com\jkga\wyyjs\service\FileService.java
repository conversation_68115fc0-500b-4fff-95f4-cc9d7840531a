package com.jkga.wyyjs.service;

import com.jkga.wyyjs.mapper.FileMapper;
import com.jkga.wyyjs.model.dto.FileSaveReturnDTO;
import com.jkga.wyyjs.model.entity.FileEntity;
import com.jkga.wyyjs.utils.SnowflakeUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import sun.misc.BASE64Decoder;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 文件服务
 *
 * <AUTHOR>
 * @date 2025/1/17 10:44
 */
@Service
@Slf4j
public class FileService {

    @Autowired
    private FileMapper fileMapper;

    @Value("${file.root-path}")
    private String FILE_ROOT_PATH;

    /**
     * 通用文件存储
     *
     * @param userId
     * @param files
     * @param tbId
     * @param tbType
     * @return
     * @throws IOException
     * @throws NoSuchAlgorithmException
     */
    public List<FileSaveReturnDTO> saveFiles(String userId, List<MultipartFile> files, String tbId, String tbType, String modulePath) throws IOException, NoSuchAlgorithmException {
        List<FileSaveReturnDTO> returnFileList = new ArrayList<>();

        for (MultipartFile file : files) {
            if (!file.isEmpty()) {
                String checksum = calculateChecksum(file);
                FileEntity existsFile = fileMapper.selectExistsFile(checksum, tbId, tbType);
                // 检查是否重复上传，并确保ownerType和ownerId匹配
                if (existsFile == null) {
                    FileEntity fileEntity = new FileEntity();
                    fileEntity.setId(SnowflakeUtil.generateId());
                    fileEntity.setFileName(file.getOriginalFilename());
                    fileEntity.setFilePath(saveToFileSystem(file, modulePath));
                    fileEntity.setFileType(file.getContentType());
                    fileEntity.setSize(file.getSize());
                    fileEntity.setChecksum(checksum);
                    fileEntity.setTbType(tbType);
                    fileEntity.setTbId(tbId);
                    fileEntity.setCreatorId(userId);
                    fileEntity.setIsDeleted((byte) 0);
                    fileMapper.insertFile(fileEntity);

                    FileSaveReturnDTO fileSaveReturnDTO = new FileSaveReturnDTO();
                    fileSaveReturnDTO.setId(fileEntity.getId());
                    fileSaveReturnDTO.setUrl(fileEntity.getFilePath());
                    returnFileList.add(fileSaveReturnDTO);
                } else {
                    FileSaveReturnDTO fileSaveReturnDTO = new FileSaveReturnDTO();
                    fileSaveReturnDTO.setId(existsFile.getId());
                    fileSaveReturnDTO.setUrl(existsFile.getFilePath());
                    returnFileList.add(fileSaveReturnDTO);
                }
            }
        }
        return returnFileList;
    }


    /**
     * base64文件存储
     *
     * @Author：clyde
     * @Date：2025/2/27 14:00
     */
    public List<FileSaveReturnDTO> saveBase64Files(String userId, List<String> base64Images, String tbId, String tbType, String modulePath) throws NoSuchAlgorithmException {
        List<FileSaveReturnDTO> returnFileList = new ArrayList<>();

        for (String base64Image : base64Images) {
            String checksum = calculateBase64Checksum(base64Image);
            FileEntity existsFile = fileMapper.selectExistsFile(checksum, tbId, tbType);
            // 检查是否重复上传，并确保ownerType和ownerId匹配
            if (existsFile == null) {
                FileEntity fileEntity = new FileEntity();
                fileEntity.setId(SnowflakeUtil.generateId());
                fileEntity.setFileName(null);
                fileEntity.setFilePath(decode(base64Image, modulePath));
                fileEntity.setFileType("image/jpeg");
                byte[] imageBytes = Base64.getDecoder().decode(base64Image);
                fileEntity.setSize(imageBytes.length);
                fileEntity.setChecksum(checksum);
                fileEntity.setTbType(tbType);
                fileEntity.setTbId(tbId);
                fileEntity.setCreatorId(userId);
                fileEntity.setIsDeleted((byte) 0);
                fileMapper.insertFile(fileEntity);

                FileSaveReturnDTO fileSaveReturnDTO = new FileSaveReturnDTO();
                fileSaveReturnDTO.setId(fileEntity.getId());
                fileSaveReturnDTO.setUrl(fileEntity.getFilePath());
                returnFileList.add(fileSaveReturnDTO);
            } else {
                FileSaveReturnDTO fileSaveReturnDTO = new FileSaveReturnDTO();
                fileSaveReturnDTO.setId(existsFile.getId());
                fileSaveReturnDTO.setUrl(existsFile.getFilePath());
                returnFileList.add(fileSaveReturnDTO);
            }
        }
        return returnFileList;
    }

    /**
     * 计算校验和
     *
     * @param file
     * @return
     * @throws NoSuchAlgorithmException
     * @throws IOException
     */
    public String calculateChecksum(MultipartFile file) throws NoSuchAlgorithmException, IOException {
        MessageDigest digest = MessageDigest.getInstance("SHA-256");
        byte[] buffer = new byte[8192];
        int bytesRead;
        try (InputStream inputStream = file.getInputStream()) {
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                digest.update(buffer, 0, bytesRead);
            }
        }
        byte[] hashBytes = digest.digest();
        return bytesToHex(hashBytes);
    }

    /**
     * 计算base64文件校验和
     *
     * @param base64Img
     * @return
     * @throws NoSuchAlgorithmException
     * @throws IOException
     */
    public String calculateBase64Checksum(String base64Img) throws NoSuchAlgorithmException {
        MessageDigest digest = MessageDigest.getInstance("SHA-256");
        byte[] imageBytes = Base64.getDecoder().decode(base64Img);
        byte[] hashBytes = digest.digest(imageBytes);
        return bytesToHex(hashBytes);
    }

    public String bytesToHex(byte[] hash) {
        StringBuilder hexString = new StringBuilder(2 * hash.length);
        for (byte b : hash) {
            String hex = Integer.toHexString(0xff & b);
            if (hex.length() == 1) {
                hexString.append('0');
            }
            hexString.append(hex);
        }
        return hexString.toString();
    }

    /**
     * 保存文件到服务器
     *
     * @param file
     * @param modulePath
     * @return
     * @throws IOException
     */
    private String saveToFileSystem(MultipartFile file, String modulePath) throws IOException {
        //相较于SimpleDateFormat更线程安全的日期格式化器
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        String dirPath = LocalDate.now().format(formatter);

        //确保目录存在
        Path filePath = Paths.get(FILE_ROOT_PATH, modulePath, dirPath);
        Files.createDirectories(filePath);


        //自定义文件名
        String uuid = UUID.randomUUID().toString().replaceAll("-", "");
        String fileName = uuid + getFileExtension(file.getOriginalFilename());

        // 创建目标文件
        Path targetFilePath = filePath.resolve(fileName);
        if (Files.exists(targetFilePath)) {
            log.warn("文件已经存在: {}", targetFilePath);
        }

        Files.copy(file.getInputStream(), targetFilePath, StandardCopyOption.REPLACE_EXISTING);

        return modulePath + File.separator + dirPath + File.separator + fileName;
    }

    /**
     * 获取文件扩展名
     *
     * @param fileName 文件名
     * @return 文件扩展名，如果没有扩展名则返回空字符串
     */
    private String getFileExtension(String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            return "";
        }
        int dotIndex = fileName.lastIndexOf('.');
        return dotIndex > 0 ? fileName.substring(dotIndex) : "";
    }


    /**
     * base64转文件并输出到指定目录
     *
     * @param base64Str
     * @param modulePath
     * @return
     */
    public String decode(String base64Str, String modulePath) {
        //相较于SimpleDateFormat更线程安全的日期格式化器
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        String dirPath = LocalDate.now().format(formatter);
        String filePath = FILE_ROOT_PATH + "/" + modulePath + "/" + dirPath;

        File file = null;
        //创建文件目录
        File dir = new File(filePath);
        if (!dir.exists() && !dir.isDirectory()) {
            dir.mkdirs();
        }
        BufferedOutputStream bos = null;
        java.io.FileOutputStream fos = null;

        byte[] b = null;
        BASE64Decoder decoder = new BASE64Decoder();
        try {
            b = decoder.decodeBuffer(replaceEnter(base64Str));
            //window
            //file=new File(filePath+"\\"+fileName);
            //linux
            //自定义文件名
            String fileName = UUID.randomUUID().toString().replaceAll("-", "") + ".jpg";
            file = new File(filePath + "/" + fileName);
            fos = new java.io.FileOutputStream(file);
            bos = new BufferedOutputStream(fos);
            bos.write(b);

            return modulePath + File.separator + dirPath + File.separator + fileName;
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (bos != null) {
                try {
                    bos.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (fos != null) {
                try {
                    fos.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return null;
    }

    public static String replaceEnter(String str) {
        String reg = "[\n-\r]";
        Pattern p = Pattern.compile(reg);
        Matcher m = p.matcher(str);
        return m.replaceAll("");
    }


}
