package com.jkga.wyyjs.task;

import com.jkga.wyyjs.model.configuration.WeixinConfiguration;
import com.jkga.wyyjs.model.weixin.AccessToken;
import com.jkga.wyyjs.utils.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;


@Slf4j
@Component
public class TokenTask {

    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private WeixinConfiguration wxConfiguration;
    @Autowired
    private RedisUtil redisUtil;

    /**
     * 定时获取access_token值存入redis
     */
//    @Scheduled(initialDelay = 100, fixedRate = 60 * 1000) ////每60秒（1分钟）执行一次
    @Scheduled(cron = "0/5 * * * * ?")   //每5秒执行一次
    public void getAccessToken() {
        log.info("========== 定时执行获取access_token ==========");

        //从redis获取access_token
        String redisAccessToken = String.valueOf(redisUtil.get("access_token"));
        log.info("redis access_token: " + redisAccessToken);
        long expireTime = redisUtil.getExpire("access_token");
        log.info("expireTime: " + expireTime);

        if (expireTime <= 60) {
            //为空则获取access_token并存入redis，设置过期时间为5分钟
            AccessToken accessToken = wxRequestAccessToken();
            redisUtil.set("access_token", accessToken.getAccess_token(), 5 * 60);

            log.info("set new access_token: " + accessToken.getAccess_token());
        }

    }


    /**
     * 请求微信接口获取access_token
     *
     * @return
     */
    private AccessToken wxRequestAccessToken() {
        Map<String, String> param = new HashMap<>();
        param.put("grant_type", "client_credential");
        param.put("appid", wxConfiguration.getAppId());
        param.put("secret", wxConfiguration.getAppSecret());

        return restTemplate.getForObject(wxConfiguration.getTokenUrl() +
                "?grant_type={grant_type}&appid={appid}&secret={secret}", AccessToken.class, param);
    }

}
