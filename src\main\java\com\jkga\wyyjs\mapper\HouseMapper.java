package com.jkga.wyyjs.mapper;

import com.jkga.wyyjs.model.dto.HouseMemberDTO;
import com.jkga.wyyjs.model.dto.UserHouseDTO;
import com.jkga.wyyjs.model.entity.*;
import com.jkga.wyyjs.model.vo.CarDetailVO;
import com.jkga.wyyjs.model.vo.CarVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * ${description}
 *
 * <AUTHOR>
 * @date 2025/2/10 10:52
 */
@Mapper
public interface HouseMapper {

    List<HouseMemberDTO> listHouseMember(@Param("currIndex") int currIndex,
                                         @Param("pageSize") int pageSize,
                                         @Param("status") Integer status,
                                         @Param("area_id") String area_id);

    int countHouseMember(@Param("status") Integer status,
                         @Param("area_id") String area_id);

    void insertHouseMember(HouseMemberEntity houseMemberEntity);

    void reviewHouseMember(@Param("adminId") String adminId,
                           @Param("houseMemberId") String houseMemberId,
                           @Param("status") int status);

    int countExistsHouseMember(@Param("houseId") String houseId,
                               @Param("userId") String userId);

    List<HouseEntity> getHouseListByAreaId(@Param("areaId") String areaId);

    List<UserHouseDTO> listUserHouse(@Param("userId") String userId);

    int countHouseByArea(@Param("areaId") String areaId);

    int countApplyHouseByArea(@Param("areaId") String areaId);

    int countHouseMemberByArea(@Param("areaId") String areaId);

    List<HouseMemberEntity> getHouseMemberListByArea(@Param("areaId") String areaId);

    List<HouseEntity> getBuildingByAreaId(@Param("areaId") String areaId);

    List<HouseEntity> getHouseByBuildingId(@Param("buildingId") String buildingId);

    List<HouseMemberDTO> getHouseMemberListByHouseId(@Param("houseId") String houseId);

    CarTableEntity getCarTableByNo(@Param("areaId") String areaId, @Param("car_no") String car_no);

    List<CarDetailTableEntity> getCarDetailTableByCarId(@Param("areaId") String areaId, @Param("car_id") Integer car_id);


    List<CarTableEntity> getToBindingCarListByAreaId(@Param("area_location_id") String area_location_id, @Param("car_no") String car_no, @Param("currIndex") int currIndex, @Param("pageSize") int pageSize);

    int getToBindingCarListByAreaIdCount(@Param("area_location_id") String area_location_id, @Param("car_no") String car_no);

    List<Map> getCarManagerListByAreaId(@Param("area_location_id") String area_location_id, @Param("param") String param, @Param("currIndex") int currIndex, @Param("pageSize") int pageSize);

    int getCarManagerListByAreaIdCount(@Param("area_location_id") String area_location_id, @Param("param") String param);

    int updateCarManagerById(CarManageEntity carManageEntity);

    int checkCarIsExist(@Param("area_location_id") String area_location_id, @Param("car_no") String car_no, @Param("id") String id);

    boolean addCarManage(CarManageEntity carManageEntity);

    boolean updCarTableBind(@Param("id") Integer id);

    CarTableEntity getCarTableById(@Param("id") Integer id);

    boolean updCarTableRemind(@Param("id") Integer id);

    int countHouseCarManage(@Param("house_id") String house_id, @Param("car_number") String car_number);

    int countAreaCarTableById(@Param("id") Integer id);

    boolean addCarTable(CarVO carVO);

    int countAreaCarDetailTableById(@Param("id") Integer id);

    boolean addCarDetailTable(CarDetailVO carDetailVO);

    HouseEntity getHouseById(@Param("id") String id);

    boolean updateCarTableTime(@Param("id") Integer id, @Param("time") int time);

    boolean updateCarDetailTableTime(@Param("id") Integer id, @Param("time") int time);

    List<CarManageEntity> getCarManagerListByAreaAndCar(@Param("areaId") String areaId, @Param("car_number") String car_number);

    AlarmQcbEntity getAreaAlarmQcbByDIdAndCar(@Param("areaId") String areaId, @Param("dossierId") String dossierId, @Param("carNo") String carNo);

    boolean addAlarmQcb(AlarmQcbEntity alarmQcbEntity);

    boolean updateAlarmQcb(AlarmQcbEntity alarmQcbEntity);

    AlarmQcbEntity getAreaAlarmQcbById(@Param("id") String id);

    HouseMemberEntity getHouseMemberById(@Param("id") String id);

    int getAlarmQcbListCountByAreaId(@Param("area_location_id") String area_location_id, @Param("status") Integer status, @Param("param") String param);

    List<AlarmQcbEntity> getAlarmQcbListByAreaId(@Param("area_location_id") String area_location_id, @Param("status") Integer status, @Param("param") String param, @Param("currIndex") int currIndex, @Param("pageSize") int pageSize);
}
