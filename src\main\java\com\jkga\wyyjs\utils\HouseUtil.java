package com.jkga.wyyjs.utils;

import com.jkga.wyyjs.mapper.HouseMapper;
import com.jkga.wyyjs.mapper.UserMapper;
import com.jkga.wyyjs.model.dto.UserTransformDTO;
import com.jkga.wyyjs.model.entity.HouseEntity;
import com.jkga.wyyjs.model.entity.HouseMemberEntity;
import com.jkga.wyyjs.model.entity.UserEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Author：clyde
 * @Date：2025/6/19 10:35
 */
@Service
@Slf4j
public class HouseUtil {

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private HouseMapper houseMapper;


    public void syncQcbUser(UserEntity userEntity) {
        if (StringUtils.isNotBlank(userEntity.getIdentity())) {
            UserEntity ex = userMapper.getUserByIdentity(userEntity.getIdentity());
            if (ex == null) {
                if (StringUtils.isNotBlank(userEntity.getPhone())) {
                    UserEntity phone_ex = userMapper.getUserByPhone(userEntity.getPhone());
                    if (phone_ex == null) {
                        UserEntity addUserEntity = new UserEntity();
                        BeanUtils.copyProperties(userEntity, addUserEntity);
                        String userId = SnowflakeUtil.generateId();
                        addUserEntity.setId(userId);
                        userMapper.syncAddQcbUser(addUserEntity);

                    } else {
                        if (StringUtils.isBlank(phone_ex.getIdentity())) {
                            UserEntity updateUserEntity = new UserEntity();
                            BeanUtils.copyProperties(userEntity, updateUserEntity);
                            updateUserEntity.setId(phone_ex.getId());
                            userMapper.syncUpdQcbUser(updateUserEntity);

                        } else {
                        }
                    }
                } else {
                    UserEntity addUserEntity = new UserEntity();
                    BeanUtils.copyProperties(userEntity, addUserEntity);
                    String userId = SnowflakeUtil.generateId();
                    addUserEntity.setId(userId);
                    userMapper.syncAddQcbUser(addUserEntity);
                }
            } else {
            }
        } else {
            UserEntity ex = userMapper.getUserByDossierId(userEntity.getDossierId());
            if (ex != null) {

            } else {
                UserEntity addUserEntity = new UserEntity();
                BeanUtils.copyProperties(userEntity, addUserEntity);
                String userId = SnowflakeUtil.generateId();
                addUserEntity.setId(userId);
                userMapper.syncAddQcbUser(addUserEntity);
            }
        }

    }

    public void addHouseMember(String houseId, UserEntity userEntity, String adminId) {
        HouseEntity houseEntity = houseMapper.getHouseById(houseId);
        if (houseEntity != null) {
            int ExistsHouseMemberNum = houseMapper.countExistsHouseMember(houseEntity.getId(), userEntity.getId());
            if (ExistsHouseMemberNum == 0) {
                //=====存house_member表=====
                HouseMemberEntity houseMemberEntity = new HouseMemberEntity();
                //雪花id
                String houseMemberId = SnowflakeUtil.generateId();
                houseMemberEntity.setId(houseMemberId);
                houseMemberEntity.setUserId(userEntity.getId());
                houseMemberEntity.setUserName(userEntity.getName());
                houseMemberEntity.setUrl(userEntity.getFaceImgUrl());
                HouseEntity p_house = houseMapper.getHouseById(houseEntity.getParentId());
                houseMemberEntity.setAreaId(houseEntity.getAreaId());
                houseMemberEntity.setHouseId(houseEntity.getId());

                if (p_house.getHouseType() == 1) {
                    HouseEntity b_house = houseMapper.getHouseById(p_house.getParentId());
                    houseMemberEntity.setBuildingName(b_house.getName() + p_house.getName() + houseEntity.getName());
                } else {
                    houseMemberEntity.setBuildingName(p_house.getName() + houseEntity.getName());
                }
                houseMemberEntity.setType(0);
                houseMemberEntity.setStatus(1);
                houseMemberEntity.setCreator(adminId);
                houseMemberEntity.setDeleteStatus(0);
                houseMapper.insertHouseMember(houseMemberEntity);
            }
        }

    }


}
