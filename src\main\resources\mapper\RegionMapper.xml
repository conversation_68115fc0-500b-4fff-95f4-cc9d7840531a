<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jkga.wyyjs.mapper.RegionMapper">

    <select id="listRegionByLevel" resultType="com.jkga.wyyjs.model.entity.RegionEntity">
        select * from "region" where "level" = #{level}
        <if test="parentId != null and parentId != ''">
            <choose>
                <when test="level == 1">
                  and "code" = #{parentId}
                </when>
                <when test="level == 2">
                  and "province_code" = #{parentId}
                </when>
                <when test="level == 3">
                  and "city_code" = #{parentId}
                </when>
                <when test="level == 4">
                  and "district_code" = #{parentId}
                </when>
                <when test="level == 5">
                  and "street_code" = #{parentId}
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
    </select>

</mapper>