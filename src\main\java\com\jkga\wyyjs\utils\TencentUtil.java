package com.jkga.wyyjs.utils;

import com.jkga.wyyjs.model.configuration.FaceConfiguration;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;
import com.tencentcloudapi.faceid.v20180301.FaceidClient;
import com.tencentcloudapi.faceid.v20180301.models.DetectAuthRequest;
import com.tencentcloudapi.faceid.v20180301.models.DetectAuthResponse;
import com.tencentcloudapi.faceid.v20180301.models.GetDetectInfoRequest;
import com.tencentcloudapi.faceid.v20180301.models.GetDetectInfoResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;



@Service
@Slf4j
public class TencentUtil {

    @Autowired
    private FaceConfiguration faceConfiguration;






    /**
     * 人脸核身获取bizToken（调用官方SDK）
     *
     * @param idCard
     * @param name
     * @return
     */
    public DetectAuthResponse detectAuth(String idCard, String name) throws TencentCloudSDKException {

            Credential cred = new Credential(faceConfiguration.getSecretId(), faceConfiguration.getSecretKey());

            HttpProfile httpProfile = new HttpProfile();
            httpProfile.setEndpoint("faceid.tencentcloudapi.com");

            ClientProfile clientProfile = new ClientProfile();
            clientProfile.setHttpProfile(httpProfile);

            FaceidClient client = new FaceidClient(cred, "ap-shanghai", clientProfile);

//            String params = "{\"RuleId\":\"0\"}";
//            DetectAuthRequest req = DetectAuthRequest.fromJsonString(params, DetectAuthRequest.class);

            DetectAuthRequest req = new DetectAuthRequest();
            req.setRuleId("1");
            if (StringUtils.isNotBlank(idCard)) {
                req.setIdCard(idCard);
            }
            if (StringUtils.isNotBlank(name)) {
                req.setName(name);
            }

            DetectAuthResponse resp = client.DetectAuth(req);

            log.info(DetectAuthRequest.toJsonString(resp));

            return resp;

    }


    /**
     * 获取核身信息（调用官方SDK）
     *
     * @return
     */
    public GetDetectInfoResponse getDetectInfo(String bizToken) {
        try {

            Credential cred = new Credential(faceConfiguration.getSecretId(), faceConfiguration.getSecretKey());

            HttpProfile httpProfile = new HttpProfile();
            httpProfile.setEndpoint("faceid.tencentcloudapi.com");

            ClientProfile clientProfile = new ClientProfile();
            clientProfile.setHttpProfile(httpProfile);

            FaceidClient client = new FaceidClient(cred, "ap-shanghai", clientProfile);

            String params = "{\"BizToken\":\"" + bizToken + "\",\"RuleId\":\"1\"}";
            GetDetectInfoRequest req = GetDetectInfoRequest.fromJsonString(params, GetDetectInfoRequest.class);

            GetDetectInfoResponse resp = client.GetDetectInfo(req);

//            log.info(GetDetectInfoRequest.toJsonString(resp));

            return resp;
        } catch (TencentCloudSDKException e) {
            log.error(e.toString());
        }
        return null;
    }



}
