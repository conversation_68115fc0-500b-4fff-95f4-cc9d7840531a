<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jkga.wyyjs.mapper.SysOperateLogMapper">


    <insert id="saveSysOperateLog" parameterType="com.jkga.wyyjs.model.entity.SysOperateLogEntity">
        insert into "sys_operate_log"("optModel", "optDesc", "method", "user_id", "method_type", "content_type", "request_data", "response_data", "ip", "intime", "request_uri", "response_time")
        VALUES (#{optModel}, #{optDesc}, #{method}, #{userId}, #{methodType}, #{contentType}, #{requestData}, #{responseData}, #{ip}, #{intime}, #{requestUri}, #{responseTime})
    </insert>



</mapper>