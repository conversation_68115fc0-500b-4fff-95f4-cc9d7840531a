package com.jkga.wyyjs.mapper;

import com.jkga.wyyjs.model.entity.AdminManagerEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 管理人员Mapper接口
 * 
 * @Author：clyde
 * @Date：2025/7/18
 */
@Mapper
public interface AdminManagerMapper {

    /**
     * 插入管理人员
     */
    boolean insertAdminManager(AdminManagerEntity adminManagerEntity);

    /**
     * 根据条件查询管理人员
     */
    AdminManagerEntity getAdminManagerByCondition(@Param("userId") String userId, 
                                                 @Param("areaId") String areaId, 
                                                 @Param("placeId") String placeId,
                                                 @Param("type") Integer type);

    /**
     * 根据ID删除管理人员（逻辑删除）
     */
    boolean delAdminManager(@Param("id") String id);

    /**
     * 根据条件删除管理人员（逻辑删除）
     */
    boolean delAdminManagerByCondition(@Param("userId") String userId, 
                                      @Param("areaId") String areaId, 
                                      @Param("placeId") String placeId,
                                      @Param("type") Integer type);

    /**
     * 获取团队人员列表
     */
    List<AdminManagerEntity> getTeamMembers(@Param("managerId") String managerId, 
                                           @Param("type") Integer type);

    /**
     * 更新管理人员信息
     */
    boolean updateAdminManager(AdminManagerEntity adminManagerEntity);

    /**
     * 根据部门ID设置部门为null
     */
    boolean clearDepartmentByDepartmentId(@Param("departmentId") String departmentId);

    /**
     * 根据用户ID获取中介管理权限
     */
    List<AdminManagerEntity> getIntermediaryManagersByUserId(@Param("userId") String userId);

}
