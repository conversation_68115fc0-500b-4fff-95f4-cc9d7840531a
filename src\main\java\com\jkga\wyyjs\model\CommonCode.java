package com.jkga.wyyjs.model;

/**
 * ${description}
 *
 * <AUTHOR>
 */
public enum CommonCode {

    OK(200, "成功"),
    FAIL(-1,"自定义消息"),
    SYSTEM_ERROR(500, "系统内部错误"),
    PARAM_ERROR(400, "参数错误"),
    TOKEN_UNAUTHORIZED(401, "token无效"),
    DATA_EXIST(402, "数据已存在"),
    NO_PERMISSION(403,"没有权限"),
    NO_USER(405,"用户不存在");

    private final int code;
    private final String message;

    private CommonCode(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public int getCode() {
        return this.code;
    }

    public String getMessage() {
        return this.message;
    }

}
