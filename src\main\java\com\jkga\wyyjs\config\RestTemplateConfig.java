package com.jkga.wyyjs.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;


/**
 * Http请求工具类配置
 *
 * <AUTHOR>
 */
@Configuration
public class RestTemplateConfig {

//    @Value("${jkga-api.dahua-idconfirm-ip}")
//    private String dahuaIdconfirmIp;
//    @Value("${jkga-api.dahua-idconfirm-port}")
//    private int dahuaIdconfirmPort;


    //    @Bean(name = "restTemplate")
    @Bean
    public RestTemplate restTemplate() {
        SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
        //连接超时10秒
        factory.setConnectTimeout(10 * 1000);
        //读取超时10秒
        factory.setReadTimeout(10 * 1000);
        return new RestTemplate(factory);
    }

//    @Bean(name = "myRestTemplate")
//    public RestTemplate createMyRestTemplate() {
//        SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
//        //连接超时10秒
//        factory.setConnectTimeout(10 * 1000);
//        //读取超时1分钟
//        factory.setReadTimeout(60 * 1000);
//        return new RestTemplate(factory);
//    }


//    @Bean(name = "myRestTemplate")
//    public RestTemplate getRestTemplate() {
//        String host = dahuaIdconfirmIp;
//        int port = dahuaIdconfirmPort;
//        String userName = "dahua";
//        String password = "dahua123456";
//        //摘要认证时使用
//        CredentialsProvider credsProvider = new BasicCredentialsProvider();
//        credsProvider.setCredentials(new AuthScope(host, port, AuthScope.ANY_REALM),
//                new UsernamePasswordCredentials(userName, password));
//        DefaultHttpClient newHttpClient = new DefaultHttpClient();
//        newHttpClient.setCredentialsProvider(credsProvider);
//
//        RestTemplate restTemplate = new RestTemplate(new PreEmptiveAuthHttpRequestFactory(newHttpClient));
//        //支持中文编码
//        restTemplate.getMessageConverters().set(1,
//                new StringHttpMessageConverter(StandardCharsets.UTF_8));
//
//        return restTemplate;
//    }

}
