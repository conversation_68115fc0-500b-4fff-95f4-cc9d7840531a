package com.jkga.wyyjs.service;

import cn.hutool.json.JSONObject;
import com.jkga.wyyjs.mapper.*;
import com.jkga.wyyjs.model.entity.*;
import com.jkga.wyyjs.utils.SnowflakeUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * 用户管理服务类
 * 处理用户创建、角色分配、权限管理等业务逻辑
 * 
 * @Author：clyde
 * @Date：2025/7/18
 */
@Service
@Slf4j
public class UserManagerService {

    @Autowired
    private UserMapper userMapper;
    
    @Autowired
    private RoleMapper roleMapper;
    
    @Autowired
    private RolePermissionMapper rolePermissionMapper;
    
    @Autowired
    private UserRoleMapper userRoleMapper;
    
    @Autowired
    private PermissionMapper permissionMapper;
    
    @Autowired
    private AreaManagerMapper areaManagerMapper;
    
    @Autowired
    private IntermediaryManagerMapper intermediaryManagerMapper;

    /**
     * 处理MQ消息的完整业务逻辑
     */
    @Transactional
    public void processUserManagerMessage(JSONObject data) {
        try {
            Integer type = data.getInt("type");
            String areaId = data.getStr("area_id");
            String idCard = data.getStr("id_card");
            String userName = data.getStr("user_name");
            String phone = data.getStr("phone");
            String position = data.getStr("position");
            String status = data.getStr("status");
            String updateUser = data.getStr("update_user");

            log.info("开始处理用户管理消息: type={}, areaId={}, idCard={}, userName={}, status={}", 
                    type, areaId, idCard, userName, status);

            // 第一步：处理用户信息
            String userId = processUserInfo(idCard, userName, phone);
            
            // 第二步：处理管理人员信息
            processManagerInfo(type, areaId, userId, userName, phone, position, status);
            
            // 第三步：处理角色信息
            String roleId = processRoleInfo(type, areaId);
            
            // 第四步：处理角色权限关联
            processRolePermissions(roleId, type);
            
            // 第五步：处理用户角色关联
            processUserRole(userId, roleId, areaId, status);
            
            log.info("用户管理消息处理完成: userId={}, roleId={}", userId, roleId);
            
        } catch (Exception e) {
            log.error("处理用户管理消息失败", e);
            throw e;
        }
    }

    /**
     * 第一步：处理用户信息
     */
    private String processUserInfo(String idCard, String userName, String phone) {
        // 根据身份证查询用户是否存在
        UserEntity existingUser = userMapper.getUserByIdentity(idCard);
        
        if (existingUser != null) {
            log.info("用户已存在，userId: {}", existingUser.getId());
            return existingUser.getId();
        } else {
            // 创建新用户
            UserEntity newUser = new UserEntity();
            String userId = SnowflakeUtil.generateId();
            newUser.setId(userId);
            newUser.setName(userName);
            newUser.setIdentityType("身份证");
            newUser.setIdentity(idCard);
            newUser.setDeleteStatus(0);
            
            userMapper.syncAddQcbUser(newUser);
            log.info("创建新用户成功，userId: {}", userId);
            return userId;
        }
    }

    /**
     * 第二步：处理管理人员信息
     */
    private void processManagerInfo(Integer type, String areaId, String userId, String userName, 
                                  String phone, String position, String status) {
        String currentTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        
        if (type == 0) {
            // 物业管理人员
            processAreaManager(areaId, userId, userName, phone, position, status, currentTime);
        } else if (type == 1) {
            // 中介管理人员
            processIntermediaryManager(userId, userName, phone, position, status, currentTime);
        }
    }

    /**
     * 处理物业管理人员
     */
    private void processAreaManager(String areaId, String userId, String userName, String phone, 
                                  String position, String status, String currentTime) {
        AreaManagerEntity existingManager = areaManagerMapper.getAreaManagerByUserId(areaId, userId);
        
        if ("1".equals(status)) {
            // 删除操作
            if (existingManager != null) {
                areaManagerMapper.delAreaManager(existingManager.getId());
                log.info("删除物业管理人员: areaId={}, userId={}", areaId, userId);
            }
        } else {
            // 新增操作
            if (existingManager == null) {
                AreaManagerEntity newManager = new AreaManagerEntity();
                newManager.setId(SnowflakeUtil.generateId());
                newManager.setAreaId(areaId);
                newManager.setUserId(userId);
                newManager.setUserName(userName);
                newManager.setPhone(phone);
                newManager.setPosition(position);
                newManager.setIntime(currentTime);
                newManager.setDeleteStatus(0);
                
                areaManagerMapper.insertAreaManager(newManager);
                log.info("新增物业管理人员: areaId={}, userId={}", areaId, userId);
            } else {
                log.info("物业管理人员已存在，跳过: areaId={}, userId={}", areaId, userId);
            }
        }
    }

    /**
     * 处理中介管理人员
     */
    private void processIntermediaryManager(String userId, String userName, String phone, 
                                          String position, String status, String currentTime) {
        IntermediaryManagerEntity existingManager = intermediaryManagerMapper.getIntermediaryManagerByUserId(userId);
        
        if ("1".equals(status)) {
            // 删除操作
            if (existingManager != null) {
                intermediaryManagerMapper.delIntermediaryManagerByUserId(userId);
                log.info("删除中介管理人员: userId={}", userId);
            }
        } else {
            // 新增操作
            if (existingManager == null) {
                IntermediaryManagerEntity newManager = new IntermediaryManagerEntity();
                newManager.setId(SnowflakeUtil.generateId());
                newManager.setUserId(userId);
                newManager.setUserName(userName);
                newManager.setPhone(phone);
                newManager.setPosition(position);
                newManager.setIntime(currentTime);
                newManager.setDeleteStatus(0);
                
                intermediaryManagerMapper.insertIntermediaryManager(newManager);
                log.info("新增中介管理人员: userId={}", userId);
            } else {
                log.info("中介管理人员已存在，跳过: userId={}", userId);
            }
        }
    }

    /**
     * 第三步：处理角色信息
     */
    private String processRoleInfo(Integer type, String areaId) {
        RoleEntity existingRole;
        
        if (type == 0) {
            // 物业管理员角色
            existingRole = roleMapper.getAreaRoleByName(areaId, "管理员");
        } else {
            // 中介管理员角色 - 查询role_type=1且role_name=管理员的角色
            existingRole = roleMapper.getIntermediaryRoleByName("管理员");
        }
        
        if (existingRole != null) {
            log.info("角色已存在: roleId={}", existingRole.getId());
            return existingRole.getId();
        } else {
            // 创建新角色
            RoleEntity newRole = new RoleEntity();
            String roleId = SnowflakeUtil.generateId();
            newRole.setId(roleId);
            newRole.setRoleName("管理员");
            newRole.setRoleDesc("管理员角色");
            newRole.setDeleteStatus(0);
            
            if (type == 0) {
                newRole.setAreaId(areaId);
                newRole.setRoleType(0);
            } else {
                newRole.setRoleType(1);
            }
            
            roleMapper.insertAreaRole(newRole);
            log.info("创建新角色成功: roleId={}, type={}", roleId, type);
            return roleId;
        }
    }

    /**
     * 第四步：处理角色权限关联
     */
    private void processRolePermissions(String roleId, Integer type) {
        // 获取现有的角色权限
        List<RolePermissionEntity> existingPermissions = rolePermissionMapper.getRolePermissionList(roleId);
        
        // 获取该类型的所有权限
        List<PermissionEntity> allPermissions = permissionMapper.getPermissionListByType(type);
        
        for (PermissionEntity permission : allPermissions) {
            // 检查权限是否已经关联
            boolean exists = existingPermissions.stream()
                    .anyMatch(rp -> permission.getId().equals(rp.getPermissionId()));
            
            if (!exists) {
                // 添加新的权限关联
                RolePermissionEntity newRolePermission = new RolePermissionEntity();
                newRolePermission.setId(SnowflakeUtil.generateId());
                newRolePermission.setRoleId(roleId);
                newRolePermission.setPermissionId(permission.getId());
                newRolePermission.setDeleteStatus(0);
                
                rolePermissionMapper.insertRolePermission(newRolePermission);
                log.info("添加角色权限关联: roleId={}, permissionId={}", roleId, permission.getId());
            }
        }
    }

    /**
     * 第五步：处理用户角色关联
     */
    private void processUserRole(String userId, String roleId, String areaId, String status) {
        // 对于中介管理员，areaId可能为null，需要特殊处理查询
        List<UserRoleEntity> existingUserRoles;
        if (areaId != null) {
            existingUserRoles = userRoleMapper.getAreaManagerUserRole(areaId, userId);
        } else {
            existingUserRoles = userRoleMapper.getUserRoleByUserId(userId);
        }
        
        if ("1".equals(status)) {
            // 删除操作
            for (UserRoleEntity userRole : existingUserRoles) {
                if (roleId.equals(userRole.getRoleId())) {
                    userRoleMapper.delUserRole(userRole.getId());
                    log.info("删除用户角色关联: userId={}, roleId={}", userId, roleId);
                }
            }
        } else {
            // 新增操作
            boolean exists = existingUserRoles.stream()
                    .anyMatch(ur -> roleId.equals(ur.getRoleId()));
            
            if (!exists) {
                UserRoleEntity newUserRole = new UserRoleEntity();
                newUserRole.setId(SnowflakeUtil.generateId());
                newUserRole.setUserId(userId);
                newUserRole.setRoleId(roleId);
                newUserRole.setAreaId(areaId);
                newUserRole.setDeleteStatus(0);
                
                userRoleMapper.insertUserRole(newUserRole);
                log.info("添加用户角色关联: userId={}, roleId={}, areaId={}", userId, roleId, areaId);
            } else {
                log.info("用户角色关联已存在，跳过: userId={}, roleId={}", userId, roleId);
            }
        }
    }
}
