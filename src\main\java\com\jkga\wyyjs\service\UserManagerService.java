package com.jkga.wyyjs.service;

import cn.hutool.json.JSONObject;
import com.jkga.wyyjs.mapper.*;
import com.jkga.wyyjs.model.entity.*;
import com.jkga.wyyjs.utils.SnowflakeUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * 用户管理服务类
 * 处理用户创建、角色分配、权限管理等业务逻辑
 * 
 * @Author：clyde
 * @Date：2025/7/18
 */
@Service
@Slf4j
public class UserManagerService {

    @Autowired
    private UserMapper userMapper;
    
    @Autowired
    private RoleMapper roleMapper;
    
    @Autowired
    private RolePermissionMapper rolePermissionMapper;
    
    @Autowired
    private UserRoleMapper userRoleMapper;
    
    @Autowired
    private PermissionMapper permissionMapper;
    
    @Autowired
    private AdminManagerMapper adminManagerMapper;

    /**
     * 处理MQ消息的完整业务逻辑
     */
    @Transactional
    public void processUserManagerMessage(JSONObject data) {
        try {
            Integer type = data.getInt("type");
            String areaId = data.getStr("area_id");
            String idCard = data.getStr("id_card");
            String userName = data.getStr("user_name");
            String phone = data.getStr("phone");
            String position = data.getStr("position");
            String status = data.getStr("status");
            String updateUser = data.getStr("update_user");
            String placeName = data.getStr("place_name"); // 新增place_name字段

            log.info("开始处理用户管理消息: type={}, areaId={}, idCard={}, userName={}, status={}",
                    type, areaId, idCard, userName, status);

            // 设置当前处理的type到ThreadLocal
            currentProcessingType.set(type);

            // 第一步：处理用户信息
            String userId = processUserInfo(idCard, userName, phone);

            // 第二步：处理管理人员信息
            processManagerInfo(type, areaId, userId, userName, phone, position, status, placeName);

            // 第三步：处理角色信息
            String roleId = processRoleInfo(type, areaId);

            // 第四步：处理角色权限关联
            processRolePermissions(roleId, type);

            // 第五步：处理用户角色关联
            processUserRole(userId, roleId, areaId, status);

            log.info("用户管理消息处理完成: userId={}, roleId={}", userId, roleId);

        } catch (Exception e) {
            log.error("处理用户管理消息失败", e);
            throw e;
        } finally {
            // 清理ThreadLocal
            currentProcessingType.remove();
        }
    }

    /**
     * 第一步：处理用户信息
     */
    private String processUserInfo(String idCard, String userName, String phone) {
        // 根据身份证查询用户是否存在
        UserEntity existingUser = userMapper.getUserByIdentity(idCard);
        
        if (existingUser != null) {
            log.info("用户已存在，userId: {}", existingUser.getId());
            return existingUser.getId();
        } else {
            // 创建新用户
            UserEntity newUser = new UserEntity();
            String userId = SnowflakeUtil.generateId();
            newUser.setId(userId);
            newUser.setName(userName);
            newUser.setIdentityType("身份证");
            newUser.setIdentity(idCard);
            newUser.setPhone(phone);
            newUser.setDeleteStatus(0);
            
            userMapper.syncAddQcbUser(newUser);
            log.info("创建新用户成功，userId: {}", userId);
            return userId;
        }
    }

    /**
     * 第二步：处理管理人员信息
     */
    private void processManagerInfo(Integer type, String areaId, String userId, String userName,
                                  String phone, String position, String status, String placeName) {
        String currentTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

        // 统一处理管理人员（物业和中介都使用admin_manager表）
        processAdminManager(type, areaId, userId, userName, phone, position, status, currentTime, placeName);
    }

    /**
     * 处理管理人员（统一处理物业和中介管理员）
     */
    private void processAdminManager(Integer type, String areaId, String userId, String userName, String phone,
                                   String position, String status, String currentTime, String placeName) {
        // 根据type确定查询条件
        String queryAreaId = (type == 0) ? areaId : null;
        String queryPlaceId = (type == 1) ? areaId : null;

        AdminManagerEntity existingManager = adminManagerMapper.getAdminManagerByCondition(
                userId, queryAreaId, queryPlaceId, type);

        if ("1".equals(status)) {
            // 删除操作
            if (existingManager != null) {
                adminManagerMapper.delAdminManagerByCondition(userId, queryAreaId, queryPlaceId, type);
                log.info("删除管理人员: type={}, managerId={}, userId={}", type, areaId, userId);
            }
        } else {
            // 新增操作
            if (existingManager == null) {
                AdminManagerEntity newManager = new AdminManagerEntity();
                newManager.setId(SnowflakeUtil.generateId());
                newManager.setUserId(userId);
                newManager.setUserName(userName);
                newManager.setPhone(phone);
                newManager.setPosition(position);
                newManager.setIntime(currentTime);
                newManager.setDeleteStatus(0);
                newManager.setType(type);

                if (type == 0) {
                    // 物业管理员
                    newManager.setAreaId(areaId);
                } else {
                    // 中介管理员
                    newManager.setPlaceId(areaId);
                    // 如果是中介类型且提供了place_name，则存储place_name
                    if (placeName != null && !placeName.trim().isEmpty()) {
                        newManager.setPlaceName(placeName);
                    }
                }

                adminManagerMapper.insertAdminManager(newManager);
                log.info("新增管理人员: type={}, managerId={}, userId={}", type, areaId, userId);
            } else {
                log.info("管理人员已存在，跳过: type={}, managerId={}, userId={}", type, areaId, userId);
            }
        }
    }



    /**
     * 第三步：处理角色信息
     */
    private String processRoleInfo(Integer type, String areaId) {
        // 根据type确定查询条件
        String queryAreaId = (type == 0) ? areaId : null;
        String queryPlaceId = (type == 1) ? areaId : null;

        RoleEntity existingRole = roleMapper.getRoleByCondition("管理员", queryAreaId, queryPlaceId, type);

        if (existingRole != null) {
            log.info("角色已存在: roleId={}", existingRole.getId());
            return existingRole.getId();
        } else {
            // 创建新角色
            RoleEntity newRole = new RoleEntity();
            String roleId = SnowflakeUtil.generateId();
            newRole.setId(roleId);
            newRole.setRoleName("管理员");
            newRole.setRoleDesc("管理员角色");
            newRole.setDeleteStatus(0);
            newRole.setRoleType(type);

            if (type == 0) {
                newRole.setAreaId(areaId);
            } else {
                newRole.setPlaceId(areaId);
            }

            roleMapper.insertAreaRole(newRole);
            log.info("创建新角色成功: roleId={}, type={}", roleId, type);
            return roleId;
        }
    }

    /**
     * 第四步：处理角色权限关联
     */
    private void processRolePermissions(String roleId, Integer type) {
        // 获取现有的角色权限
        List<RolePermissionEntity> existingPermissions = rolePermissionMapper.getRolePermissionList(roleId);
        
        // 获取该类型的所有权限
        List<PermissionEntity> allPermissions = permissionMapper.getPermissionListByType(type);
        
        for (PermissionEntity permission : allPermissions) {
            // 检查权限是否已经关联
            boolean exists = existingPermissions.stream()
                    .anyMatch(rp -> permission.getId().equals(rp.getPermissionId()));
            
            if (!exists) {
                // 添加新的权限关联
                RolePermissionEntity newRolePermission = new RolePermissionEntity();
                newRolePermission.setId(SnowflakeUtil.generateId());
                newRolePermission.setRoleId(roleId);
                newRolePermission.setPermissionId(permission.getId());
                newRolePermission.setDeleteStatus(0);
                
                rolePermissionMapper.insertRolePermission(newRolePermission);
                log.info("添加角色权限关联: roleId={}, permissionId={}", roleId, permission.getId());
            }
        }
    }

    /**
     * 第五步：处理用户角色关联
     */
    private void processUserRole(String userId, String roleId, String areaId, String status) {
        // 从processUserManagerMessage方法传入的type参数
        Integer type = getCurrentProcessingType();

        if ("1".equals(status)) {
            // 删除操作
            userRoleMapper.delUserRoleByCondition(userId, areaId, type);
            log.info("删除用户角色关联: userId={}, managerId={}, type={}", userId, areaId, type);
        } else {
            // 新增操作
            List<UserRoleEntity> existingUserRoles = userRoleMapper.getUserRoleByCondition(userId, areaId, type);

            if (existingUserRoles.isEmpty()) {
                UserRoleEntity newUserRole = new UserRoleEntity();
                newUserRole.setId(SnowflakeUtil.generateId());
                newUserRole.setUserId(userId);
                newUserRole.setRoleId(roleId);
                newUserRole.setDeleteStatus(0);
                newUserRole.setType(type);

                if (type == 0) {
                    newUserRole.setAreaId(areaId);
                } else {
                    newUserRole.setPlaceId(areaId);
                }

                userRoleMapper.insertUserRole(newUserRole);
                log.info("添加用户角色关联: userId={}, roleId={}, managerId={}, type={}", userId, roleId, areaId, type);
            } else {
                log.info("用户角色关联已存在，跳过: userId={}, managerId={}, type={}", userId, areaId, type);
            }
        }
    }

    // 临时存储当前处理的type，用于在processUserRole中获取
    private ThreadLocal<Integer> currentProcessingType = new ThreadLocal<>();

    private Integer getCurrentProcessingType() {
        return currentProcessingType.get();
    }
}
