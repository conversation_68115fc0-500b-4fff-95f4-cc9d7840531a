<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jkga.wyyjs.mapper.AdminManagerMapper">

    <resultMap id="AdminManagerResultMap" type="com.jkga.wyyjs.model.entity.AdminManagerEntity">
        <id     property="id"     column="id"     />
        <result property="areaId"   column="area_id"   />
        <result property="userId"   column="user_id"   />
        <result property="userName"   column="user_name"   typeHandler="com.jkga.wyyjs.component.SM4TypeHandler"/>
        <result property="phone"   column="phone"   typeHandler="com.jkga.wyyjs.component.SM4TypeHandler"/>
        <result property="position"   column="position"   />
        <result property="intime"   column="intime"   />
        <result property="deleteStatus"   column="delete_status"   />
        <result property="departmentId"   column="department_id"   />
        <result property="placeId"   column="place_id"   />
        <result property="type"   column="type"   />
    </resultMap>

    <select id="getAdminManagerByCondition" resultMap="AdminManagerResultMap">
        SELECT * from "admin_manager" 
        WHERE "user_id" = #{userId} 
        AND "type" = #{type}
        AND "delete_status" = 0
        <if test="type == 0">
            AND "area_id" = #{areaId}
        </if>
        <if test="type == 1">
            AND "place_id" = #{placeId}
        </if>
        LIMIT 1
    </select>

    <insert id="insertAdminManager" parameterType="com.jkga.wyyjs.model.entity.AdminManagerEntity">
        insert into "admin_manager"(
            "id",
            "area_id",
            "user_id",
            "user_name",
            "phone",
            "position",
            "intime",
            "delete_status",
            "department_id",
            "place_id",
            "type"
            )values(
            #{id},
            #{areaId},
            #{userId},
            #{userName,typeHandler=com.jkga.wyyjs.component.SM4TypeHandler},
            #{phone,typeHandler=com.jkga.wyyjs.component.SM4TypeHandler},
            #{position},
            #{intime},
            #{deleteStatus},
            #{departmentId},
            #{placeId},
            #{type}
        )
    </insert>

    <update id="delAdminManager">
       update "admin_manager"
        set
        "delete_status" = 1
        where "id" = #{id}
    </update>

    <update id="delAdminManagerByCondition">
       update "admin_manager"
        set
        "delete_status" = 1
        WHERE "user_id" = #{userId} 
        AND "type" = #{type}
        <if test="type == 0">
            AND "area_id" = #{areaId}
        </if>
        <if test="type == 1">
            AND "place_id" = #{placeId}
        </if>
    </update>

    <select id="getTeamMembers" resultMap="AdminManagerResultMap">
        SELECT * from "admin_manager" 
        WHERE "type" = #{type}
        AND "delete_status" = 0
        <if test="type == 0">
            AND "area_id" = #{managerId}
        </if>
        <if test="type == 1">
            AND "place_id" = #{managerId}
        </if>
    </select>

    <update id="updateAdminManager" parameterType="com.jkga.wyyjs.model.entity.AdminManagerEntity">
        update "admin_manager"
        set
        "position" = #{position},
        "department_id" = #{departmentId}
        where "id" = #{id}
    </update>

    <update id="clearDepartmentByDepartmentId">
        update "admin_manager"
        set
        "department_id" = null
        where "department_id" = #{departmentId}
    </update>

    <select id="getIntermediaryManagersByUserId" resultMap="AdminManagerResultMap">
        SELECT * from "admin_manager"
        WHERE "user_id" = #{userId}
        AND "type" = 1
        AND "delete_status" = 0
    </select>

</mapper>
