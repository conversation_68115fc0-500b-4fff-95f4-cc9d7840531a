package com.jkga.wyyjs.service;

import com.jkga.wyyjs.model.CommonResult;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.List;
import java.util.Map;

/**
 * TeamManagementService获取团队成员测试类
 * 
 * @Author：clyde
 * @Date：2025/7/18
 */
@SpringBootTest
@ActiveProfiles("dev")
public class TeamManagementServiceGetMembersTest {

    @Autowired
    private TeamManagementService teamManagementService;

    /**
     * 测试获取团队成员 - 物业类型
     */
    @Test
    public void testGetTeamMembersForProperty() {
        String managerId = "test_area_001";
        Integer type = 0; // 物业
        
        System.out.println("测试获取物业团队成员: managerId=" + managerId + ", type=" + type);
        
        try {
            CommonResult result = teamManagementService.getTeamMembers(managerId, type);
            
            System.out.println("返回结果: " + result);
            System.out.println("状态码: " + result.getCode());
            System.out.println("消息: " + result.getMsg());
            
            if (result.getData() != null) {
                @SuppressWarnings("unchecked")
                List<Map<String, Object>> departments = (List<Map<String, Object>>) result.getData();
                
                System.out.println("部门总数: " + departments.size());
                
                for (Map<String, Object> department : departments) {
                    String deptName = (String) department.get("department_name");
                    String deptId = (String) department.get("department_id");
                    @SuppressWarnings("unchecked")
                    List<Map<String, Object>> members = (List<Map<String, Object>>) department.get("members");
                    
                    System.out.println("部门: " + deptName + " (ID: " + deptId + ")");
                    System.out.println("  成员数量: " + members.size());
                    
                    for (Map<String, Object> member : members) {
                        System.out.println("  - 姓名: " + member.get("name"));
                        System.out.println("    用户ID: " + member.get("user_id"));
                        System.out.println("    职位: " + member.get("position"));
                        System.out.println("    部门ID: " + member.get("department_id"));
                        System.out.println("    管理ID: " + member.get("manager_id"));
                    }
                    
                    if (members.isEmpty()) {
                        System.out.println("  该部门暂无成员");
                    }
                    System.out.println();
                }
            }
            
        } catch (Exception e) {
            System.err.println("测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试获取团队成员 - 中介类型
     */
    @Test
    public void testGetTeamMembersForIntermediary() {
        String managerId = "test_place_001";
        Integer type = 1; // 中介
        
        System.out.println("测试获取中介团队成员: managerId=" + managerId + ", type=" + type);
        
        try {
            CommonResult result = teamManagementService.getTeamMembers(managerId, type);
            
            System.out.println("返回结果: " + result);
            System.out.println("状态码: " + result.getCode());
            System.out.println("消息: " + result.getMsg());
            
            if (result.getData() != null) {
                @SuppressWarnings("unchecked")
                List<Map<String, Object>> departments = (List<Map<String, Object>>) result.getData();
                
                System.out.println("部门总数: " + departments.size());
                
                for (Map<String, Object> department : departments) {
                    String deptName = (String) department.get("department_name");
                    String deptId = (String) department.get("department_id");
                    @SuppressWarnings("unchecked")
                    List<Map<String, Object>> members = (List<Map<String, Object>>) department.get("members");
                    
                    System.out.println("部门: " + deptName + " (ID: " + deptId + ")");
                    System.out.println("  成员数量: " + members.size());
                    
                    if (members.isEmpty()) {
                        System.out.println("  该部门暂无成员");
                    } else {
                        for (Map<String, Object> member : members) {
                            System.out.println("  - 姓名: " + member.get("name"));
                            System.out.println("    用户ID: " + member.get("user_id"));
                            System.out.println("    职位: " + member.get("position"));
                        }
                    }
                    System.out.println();
                }
            }
            
        } catch (Exception e) {
            System.err.println("测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试验证返回结构
     */
    @Test
    public void testGetTeamMembersStructure() {
        String managerId = "test_area_001";
        Integer type = 0;
        
        System.out.println("测试验证返回结构");
        
        try {
            CommonResult result = teamManagementService.getTeamMembers(managerId, type);
            
            // 验证基本结构
            assert result != null : "结果不能为空";
            assert result.getCode() == 200 : "状态码应该为200";
            assert result.getData() != null : "数据不能为空";
            
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> departments = (List<Map<String, Object>>) result.getData();
            
            for (Map<String, Object> department : departments) {
                // 验证部门结构
                assert department.containsKey("department_name") : "应包含department_name字段";
                assert department.containsKey("department_id") : "应包含department_id字段";
                assert department.containsKey("members") : "应包含members字段";
                
                @SuppressWarnings("unchecked")
                List<Map<String, Object>> members = (List<Map<String, Object>>) department.get("members");
                assert members != null : "members字段不能为null";
                
                // 验证成员结构
                for (Map<String, Object> member : members) {
                    assert member.containsKey("user_id") : "成员应包含user_id字段";
                    assert member.containsKey("position") : "成员应包含position字段";
                    assert member.containsKey("department_id") : "成员应包含department_id字段";
                    assert member.containsKey("manager_id") : "成员应包含manager_id字段";
                }
            }
            
            System.out.println("结构验证通过！");
            
        } catch (Exception e) {
            System.err.println("结构验证失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
