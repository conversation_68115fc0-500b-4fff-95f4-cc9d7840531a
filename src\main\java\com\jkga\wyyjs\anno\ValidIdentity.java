package com.jkga.wyyjs.anno;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 身份证校验
 *
 * <AUTHOR>
 */
@Target({ElementType.FIELD, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = IdentityValidator.class)
public @interface ValidIdentity {
    String message() default "无效的身份证号";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
