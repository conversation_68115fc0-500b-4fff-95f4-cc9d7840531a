package com.jkga.wyyjs.filter;


import com.jkga.wyyjs.component.HeaderMapRequestWrapper;
import com.jkga.wyyjs.mapper.UserMapper;
import com.jkga.wyyjs.model.CommonCode;
import com.jkga.wyyjs.model.CommonResult;
import com.jkga.wyyjs.model.entity.UserEntity;
import com.jkga.wyyjs.utils.CusAccessObjectUtil;
import com.jkga.wyyjs.utils.JsonUtil;
import com.jkga.wyyjs.utils.TokenUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.web.context.request.RequestContextHolder;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.Arrays;
import java.util.List;


/**
 * 过滤器
 *
 * <AUTHOR>
 */
//改为在config中配置
//@WebFilter(filterName = "TokenFilter", urlPatterns = {
//        "/base/*",
//        "/back/*"})
@Slf4j
public class TokenFilter implements Filter {

    private static final List<String> PASS_URL_LIST = Arrays.asList("/user/login", "/openApi/*");

    @Autowired
    private UserMapper userMapper;

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain chain) throws IOException, ServletException {
        HttpServletRequest request = (HttpServletRequest) servletRequest;
        HttpServletResponse response = (HttpServletResponse) servletResponse;

        if (HttpMethod.OPTIONS.matches(request.getMethod())) {
            log.info("**********忽略预检请求**********");
            chain.doFilter(request, response);
            return;
        }

        String bearerToken = request.getHeader("Authorization");
        String requestUri = request.getRequestURI();

        //请求内容
        String requestContent = ">>>>>>>>>>进入过滤器<<<<<<<<<<\n" +
                "========================================\n" +
                "请求地址: " + request.getRequestURL() + "\n" +
                "请求类型: " + request.getMethod() + "\n" +
                "GET请求参数: " + request.getQueryString() + "\n" +
                "Bearer token: " + bearerToken + "\n" +
                "========================================";

        // 检查请求是否为可通过路径
        if (PASS_URL_LIST.stream().anyMatch(requestUri::startsWith)) {
            // 直接放行
            chain.doFilter(request, response);
            return;
        }

        // 验证token的逻辑
        if (StringUtils.isBlank(bearerToken) || !bearerToken.startsWith("Bearer ")) {
            sendJsonError(response, "token为空或格式不正确");
            return;
        }

        //实际的token值
        String token = bearerToken.substring(7);

        try {
            boolean verifyResult = TokenUtil.isTokenValid(token);
            if (verifyResult) {
                String userId = (String) TokenUtil.getPayload(token, "uid");

                UserEntity ex = userMapper.selectUserById(userId);
                if (ex == null) {
                    sendJsonError(response, "用户不存在");
                }
                //=====请求内容打印=====
                String ip = CusAccessObjectUtil.getIpAddress(request);
                requestContent = requestContent + "\n" +
                        "userId: " + userId + "\n" +
                        "login ip: " + ip + "\n" +
                        "========================================";
                log.info(requestContent);

                HeaderMapRequestWrapper requestWrapper = new HeaderMapRequestWrapper(request);
                requestWrapper.addHeader("uid", userId);
                requestWrapper.addHeader("ip", ip);

                chain.doFilter(requestWrapper, response);
            } else {
                sendJsonError(response, "无效的token或token已过期");
            }
        } catch (Exception e) {
            sendJsonError(response, "无效的token或token已过期");
        }
    }


    private void sendJsonError(HttpServletResponse response, String message) throws IOException {
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        response.setContentType("application/json;charset=UTF-8");

        // 构建自定义的JSON响应体
        CommonResult<Object> commonResult = new CommonResult<>(CommonCode.TOKEN_UNAUTHORIZED, message, null);
        String jsonResponse = JsonUtil.toJsonString(commonResult);

        try (PrintWriter out = response.getWriter()) {
            out.print(jsonResponse);
            out.flush();
        }
    }

}
