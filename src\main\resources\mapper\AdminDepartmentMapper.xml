<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jkga.wyyjs.mapper.AdminDepartmentMapper">

    <resultMap id="AdminDepartmentResultMap" type="com.jkga.wyyjs.model.entity.AdminDepartmentEntity">
        <id     property="id"     column="id"     />
        <result property="areaId"   column="area_id"   />
        <result property="name"   column="name"   />
        <result property="deleteStatus"   column="delete_status"   />
        <result property="placeId"   column="place_id"   />
        <result property="type"   column="type"   />
    </resultMap>

    <select id="getDepartmentByName" resultMap="AdminDepartmentResultMap">
        SELECT * from "admin_department" 
        WHERE "name" = #{name} 
        AND "type" = #{type}
        AND "delete_status" = 0
        <if test="type == 0">
            AND "area_id" = #{areaId}
        </if>
        <if test="type == 1">
            AND "place_id" = #{placeId}
        </if>
        LIMIT 1
    </select>

    <select id="getDepartmentById" resultMap="AdminDepartmentResultMap">
        SELECT * from "admin_department" WHERE "id" = #{id}
    </select>

    <insert id="insertAdminDepartment" parameterType="com.jkga.wyyjs.model.entity.AdminDepartmentEntity">
        insert into "admin_department"(
            "id",
            "area_id",
            "name",
            "delete_status",
            "place_id",
            "type"
            )values(
            #{id},
            #{areaId},
            #{name},
            #{deleteStatus},
            #{placeId},
            #{type}
        )
    </insert>

    <update id="updateDepartmentName">
        update "admin_department"
        set
        "name" = #{name}
        where "id" = #{id}
    </update>

    <update id="delDepartment">
        update "admin_department"
        set
        "delete_status" = 1
        where "id" = #{id}
    </update>

    <select id="getDepartmentList" resultMap="AdminDepartmentResultMap">
        SELECT * from "admin_department" 
        WHERE "type" = #{type}
        AND "delete_status" = 0
        <if test="type == 0">
            AND "area_id" = #{managerId}
        </if>
        <if test="type == 1">
            AND "place_id" = #{managerId}
        </if>
    </select>

</mapper>
