package com.jkga.wyyjs.config;

import org.springframework.amqp.rabbit.connection.CachingConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @Author：clyde
 * @Date：2025/1/13 11:00
 */
@Configuration
public class RabbitMQTemplateConfig {

    @Bean("primaryRabbitTemplate")
    public RabbitTemplate primaryRabbitTemplate(@Qualifier("primaryConnectionFactory") CachingConnectionFactory primaryConnectionFactory) {
        RabbitTemplate template = new RabbitTemplate(primaryConnectionFactory);
        return template;
    }

//    @Bean("secondaryRabbitTemplate")
//    public RabbitTemplate secondaryRabbitTemplate(@Qualifier("secondaryConnectionFactory") CachingConnectionFactory secondaryConnectionFactory) {
//        RabbitTemplate template = new RabbitTemplate(secondaryConnectionFactory);
//        return template;
//    }
}
