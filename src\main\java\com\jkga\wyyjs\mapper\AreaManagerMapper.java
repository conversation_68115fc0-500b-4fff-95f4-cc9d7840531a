package com.jkga.wyyjs.mapper;

import com.jkga.wyyjs.model.entity.AreaManagerEntity;
import com.jkga.wyyjs.model.vo.AreaManagerVO;
import com.jkga.wyyjs.model.vo.ManagerAreaVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author：clyde
 * @Date：2025/2/10 10:10
 */
@Mapper
public interface AreaManagerMapper {

    boolean insertAreaManager(AreaManagerEntity areaManagerEntity);

    AreaManagerEntity getAreaManagerByUserId(@Param("areaId") String areaId, @Param("userId") String userId);

    List<AreaManagerVO> getAreaManagerByDepartmentId(@Param("departmentId") String departmentId);

    boolean updAreaManager(AreaManagerEntity areaManagerEntity);

    AreaManagerEntity getAreaManagerById(@Param("id") String id);

    boolean delAreaManager(@Param("id") String id);

    List<ManagerAreaVO> getManagerAreas(@Param("userId") String userId);

}
