<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jkga.wyyjs.mapper.FileMapper">

    <select id="selectExistsFile" resultType="com.jkga.wyyjs.model.entity.FileEntity">
        SELECT * FROM "file" where "checksum" = #{checksum} and "tb_type" = #{tbType} and "tb_id" = #{tbId}
        and "is_deleted" = 0 limit 1
    </select>

    <insert id="insertFile">
        insert into "file"(
        "id",
        "file_name",
        "file_path",
        "file_type",
        "size",
        "checksum",
        "tb_type",
        "tb_id",
        "creator_id",
        "create_time",
        "is_deleted"
        )values(
        #{id},
        #{fileName},
        #{filePath},
        #{fileType},
        #{size},
        #{checksum},
        #{tbType},
        #{tbId},
        #{creatorId},
        NOW(),
        #{isDeleted}
        )
    </insert>

</mapper>