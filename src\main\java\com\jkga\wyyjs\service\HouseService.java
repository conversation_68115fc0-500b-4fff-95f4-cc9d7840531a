package com.jkga.wyyjs.service;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.jkga.wyyjs.exception.MyException;
import com.jkga.wyyjs.mapper.HouseMapper;
import com.jkga.wyyjs.mapper.UserMapper;
import com.jkga.wyyjs.mapper.VisitRecordMapper;
import com.jkga.wyyjs.model.CommonCode;
import com.jkga.wyyjs.model.CommonListResult;
import com.jkga.wyyjs.model.CommonResult;
import com.jkga.wyyjs.model.dto.AreaCarMqDTO;
import com.jkga.wyyjs.model.dto.HouseMemberDTO;
import com.jkga.wyyjs.model.dto.UserHouseDTO;
import com.jkga.wyyjs.model.dto.UserTransformDTO;
import com.jkga.wyyjs.model.entity.*;
import com.jkga.wyyjs.model.vo.*;
import com.jkga.wyyjs.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.security.NoSuchAlgorithmException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * ${description}
 *
 * <AUTHOR>
 * @date 2025/2/10 10:52
 */
@Slf4j
@Service
public class HouseService {

    @Autowired
    private HouseMapper houseMapper;
    @Autowired
    private UserMapper userMapper;

    @Autowired
    private ThirdService thirdService;

    @Autowired
    private VisitRecordMapper visitRecordMapper;

    @Autowired
    private HouseUtil houseUtil;


    public CommonListResult getHouseMemberList(int pageNum, int pageSize, Integer status, String area_id) {
        //计算数据开始索引
        int currIndex = (pageNum - 1) * pageSize;

        List<HouseMemberDTO> dataList = houseMapper.listHouseMember(currIndex, pageSize, status, area_id);
        //总记录数
        int totalNum = houseMapper.countHouseMember(status, area_id);
        //总页数
        int totalPageNum = (totalNum + pageSize - 1) / pageSize;

        return new CommonListResult<>(totalNum, totalPageNum, dataList);
    }

    @Transactional(rollbackFor = Exception.class)
    public void declareForHouseMember(DeclareForHouseMemberVO declareVO) throws IOException, NoSuchAlgorithmException {
//        //=====存user表=====
//        UserEntity userEntity = new UserEntity();
//        BeanUtils.copyProperties(declareVO, userEntity);
//        //雪花id
//        String userId = SnowflakeUtil.generateId();
//        userEntity.setId(userId);
//        userMapper.insertDeclareUser(userEntity);
//        //=====存人员照片=====
//        List<MultipartFile> fileList = Arrays.asList(declareVO.getFaceImgFile());
//        String tbType = TbTypeEnum.USER.getValue();
//        //这里modulePath（模块文件夹名）直接使用了tbType，也可以自定义该模块的文件夹名
//        List<FileSaveReturnDTO> fileSaveReturnList = fileService.saveFiles(declareVO.getCreatorId(), fileList, userId, tbType, tbType);
//        String userFaceImgUrl = null;
//        if (fileSaveReturnList.size() > 0) {
//            userFaceImgUrl = fileSaveReturnList.get(0).getUrl();
//        }
//        if (StringUtils.isNotBlank(userFaceImgUrl)) {
//            userMapper.updateUserFaceImg(userId, userFaceImgUrl);
//        }
        int ExistsHouseMemberNum = houseMapper.countExistsHouseMember(declareVO.getHouseId(), declareVO.getUserId());
        if (ExistsHouseMemberNum > 0) {
            throw new MyException(new CommonResult<>(CommonCode.SYSTEM_ERROR, "人员已在该房屋下或待审核状态", null));
        }
        //=====存house_member表=====
        HouseMemberEntity houseMemberEntity = new HouseMemberEntity();
        //雪花id
        String houseMemberId = SnowflakeUtil.generateId();
        houseMemberEntity.setId(houseMemberId);
        houseMemberEntity.setUserId(declareVO.getUserId());
        houseMemberEntity.setUserName(declareVO.getName());
        houseMemberEntity.setUrl(declareVO.getFaceImgUrl());
        houseMemberEntity.setAreaId(declareVO.getAreaId());
        houseMemberEntity.setHouseId(declareVO.getHouseId());
        houseMemberEntity.setBuildingName(declareVO.getBuildingName());
        houseMemberEntity.setType(declareVO.getType());
        houseMemberEntity.setStatus(1);
        houseMemberEntity.setCreator(declareVO.getCreatorId());
        houseMemberEntity.setDeleteStatus(0);
        houseMemberEntity.setRentStartTime(declareVO.getRentStartTime());
        houseMemberEntity.setRentEndTime(declareVO.getRentEndTime());
        houseMapper.insertHouseMember(houseMemberEntity);

        //推送数据到mq
        UserEntity adminEntity = userMapper.selectUserById(declareVO.getCreatorId());
        UserEntity userEntity = userMapper.selectUserById(declareVO.getUserId());
        AreaCarMqDTO areaCarMqDTO = new AreaCarMqDTO();
        areaCarMqDTO.setArea_id(declareVO.getAreaId());
        areaCarMqDTO.setHouse_id(declareVO.getHouseId());
        areaCarMqDTO.setId_card(userEntity.getIdentity());
        areaCarMqDTO.setUser_name(userEntity.getName());
        areaCarMqDTO.setPhone(userEntity.getPhone());
        areaCarMqDTO.setFace_image_url(userEntity.getFaceImgUrl());
        areaCarMqDTO.setType(1);
        areaCarMqDTO.setUpdate_user(adminEntity.getName());
        thirdService.sendAreaCarDataToMq(areaCarMqDTO);
    }

    public void reviewHouseMember(String adminId, String houseMemberId, int status) {
        houseMapper.reviewHouseMember(adminId, houseMemberId, status);

        //推送数据到mq
        HouseMemberEntity memberEntity = houseMapper.getHouseMemberById(houseMemberId);
        UserEntity adminEntity = userMapper.selectUserById(adminId);
        UserEntity userEntity = userMapper.selectUserById(memberEntity.getUserId());
        AreaCarMqDTO areaCarMqDTO = new AreaCarMqDTO();
        areaCarMqDTO.setArea_id(memberEntity.getAreaId());
        areaCarMqDTO.setHouse_id(memberEntity.getHouseId());
        areaCarMqDTO.setId_card(userEntity.getIdentity());
        areaCarMqDTO.setUser_name(userEntity.getName());
        areaCarMqDTO.setPhone(userEntity.getPhone());
        areaCarMqDTO.setFace_image_url(userEntity.getFaceImgUrl());
        areaCarMqDTO.setType(1);
        areaCarMqDTO.setUpdate_user(adminEntity.getName());
        thirdService.sendAreaCarDataToMq(areaCarMqDTO);
    }

    public CommonResult applyHouseMember(ApplyHouseMemberVO applyHouseMemberVO) {
        UserEntity userEntity = userMapper.selectUserById(applyHouseMemberVO.getUserId());
        if (userEntity == null || StringUtils.isBlank(userEntity.getIdentity())) {
            return new CommonResult<>(CommonCode.SYSTEM_ERROR, "用户不存在或者未实名", null);
        }

        int ExistsHouseMemberNum = houseMapper.countExistsHouseMember(applyHouseMemberVO.getHouseId(), applyHouseMemberVO.getUserId());
        if (ExistsHouseMemberNum > 0) {
            return new CommonResult<>(CommonCode.SYSTEM_ERROR, "人员已在该房屋下或待审核状态", null);
        }

        //=====存house_member表=====
        HouseMemberEntity houseMemberEntity = new HouseMemberEntity();
        BeanUtils.copyProperties(applyHouseMemberVO, houseMemberEntity);
        //雪花id
        String houseMemberId = SnowflakeUtil.generateId();
        houseMemberEntity.setId(houseMemberId);
        houseMemberEntity.setUserName(userEntity.getName());
        houseMemberEntity.setUrl(userEntity.getFaceImgUrl());
        houseMemberEntity.setCreateTime(DateUtils.getTime());
        houseMemberEntity.setStatus(0);
        houseMemberEntity.setDeleteStatus(0);
        houseMapper.insertHouseMember(houseMemberEntity);
        return new CommonResult();
    }

    public List<UserHouseDTO> getUserHouseList(String userId) {
        return houseMapper.listUserHouse(userId);
    }

    public CommonResult countHouseAndMemberByArea(String areaId) {
        List<HouseMemberEntity> list = houseMapper.getHouseMemberListByArea(areaId);
        //房屋
        int total_house_num = houseMapper.countHouseByArea(areaId);
        long apply_house_num = list.stream().map(HouseMemberEntity::getHouseId).distinct().count();
        long no_apply_house_num = total_house_num - apply_house_num;

        //住户
        int total_resident_num = list.size();
        long renter_num = list.stream().filter(houseMemberEntity -> houseMemberEntity.getType().intValue() == 3).count();
        long owner_num = total_resident_num - renter_num;

        Map map = new HashMap<>();
        map.put("total_house_num", total_house_num);
        map.put("apply_house_num", apply_house_num);
        map.put("no_apply_house_num", no_apply_house_num);

        map.put("total_resident_num", total_resident_num);
        map.put("renter_num", renter_num);
        map.put("owner_num", owner_num);

        return new CommonResult(map);
    }

    public CommonResult getBuildingByAreaId(String areaId) {
        return new CommonResult(houseMapper.getBuildingByAreaId(areaId));
    }

    public CommonResult getHouseByBuildingId(String buildingId) {
        List<HouseEntity> list = houseMapper.getHouseByBuildingId(buildingId);
        List<HouseVO> house_list = list.stream().map(houseEntity -> {
            HouseVO houseVO = new HouseVO();
            BeanUtils.copyProperties(houseEntity, houseVO);
            return houseVO;
        }).collect(Collectors.toList());
        List<HouseVO> dy_list = house_list.stream().filter(houseVO -> houseVO.getHouseType() == 1).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(dy_list)) {
            dy_list.forEach(e -> {
                createHouseTree(e, house_list);
            });
            return new CommonResult(dy_list);
        } else {
            return new CommonResult(house_list);
        }
    }

    public void createHouseTree(HouseVO houseVO, List<HouseVO> list) {
        list.stream().forEach(e -> {
            if (StringUtils.isNotBlank(e.getParentId()) && e.getParentId().equals(houseVO.getId())) {
                if (houseVO.getList() == null) {
                    houseVO.setList(new ArrayList<>());
                }
                createHouseTree(e, list);
                houseVO.getList().add(e);
            }
        });
    }

    public CommonResult getHouseMemberListByHouseId(String houseId) {
        List<HouseMemberDTO> list = houseMapper.getHouseMemberListByHouseId(houseId);
        return new CommonResult(list);
    }

    public CommonResult importCar(MultipartFile file, String adminId) throws IOException {

        List<LinkedHashMap<Integer, String>> data = EasyExcel.read(file.getInputStream())
                .sheet()
                .headRowNumber(1)
                .doReadSync();

        UserEntity adminEntity = userMapper.selectUserById(adminId);

        if (data != null && data.size() > 0) {
            String areaId = data.get(0).get(0);
            List<HouseEntity> house_list = houseMapper.getHouseListByAreaId(areaId);
            List<HouseEntity> building_list = house_list.stream().filter(item -> item.getHouseType() == 0).collect(Collectors.toList());
            List<HouseEntity> unit_list = house_list.stream().filter(item -> item.getHouseType() == 1).collect(Collectors.toList());
            List<HouseEntity> room_list = house_list.stream().filter(item -> item.getHouseType() == 2).collect(Collectors.toList());

            room_list.stream().forEach(x -> {
                unit_list.stream().forEach(y -> {
                    if (x.getParentId().equals(y.getId())) {
                        x.setName(y.getName() + x.getName());
                        x.setParentId(y.getParentId());
                    }
                });
                building_list.stream().forEach(z -> {
                    if (x.getParentId().equals(z.getId())) {
                        x.setName(z.getName() + x.getName());
                    }
                });
            });


            for (int i = 0; i < data.size(); i++) {

                Map<Integer, String> item = data.get(i);
                String area_location_id = item.get(0);
                String house_name = item.get(1);
                String user_name = item.get(2);
                String car_number = item.get(3);
                String phone = item.get(4);
                String park_number = item.get(5);
                String start_time = item.get(6);
                String end_time = item.get(7);
                String remark = item.get(8);

                Optional<HouseEntity> o = room_list.stream().filter(x -> x.getName().equals(house_name)).findFirst();
                if (o.isPresent()) {
                    int countCarManage = houseMapper.countHouseCarManage(o.get().getId(), car_number);
                    if (countCarManage == 0) {
                        CarManageEntity carManageEntity = new CarManageEntity();
                        //雪花id
                        String carManageId = SnowflakeUtil.generateId();
                        carManageEntity.setId(carManageId);
                        carManageEntity.setAreaLocationId(area_location_id);
                        carManageEntity.setHouseId(o.get().getId());
                        carManageEntity.setHouseName(house_name);
                        carManageEntity.setCarNumber(car_number);
                        if (StringUtils.isNotBlank(user_name)) {
                            carManageEntity.setName(user_name);
                        }
                        if (StringUtils.isNotBlank(phone)) {
                            carManageEntity.setPhone(phone);
                        }
                        carManageEntity.setParkNumber(park_number);
                        carManageEntity.setStartTime(start_time);
                        carManageEntity.setEndTime(end_time);
                        carManageEntity.setRemark(remark);
                        carManageEntity.setIsDelete(0);

                        houseMapper.addCarManage(carManageEntity);

                    }
                    CarTableEntity ex = houseMapper.getCarTableByNo(area_location_id, car_number);
                    if (ex != null) {
                        houseMapper.updCarTableBind(ex.getId());


                        List<CarDetailTableEntity> car_detail_list = houseMapper.getCarDetailTableByCarId(area_location_id, ex.getId());
                        car_detail_list.forEach(e -> {
                            if (e.getIsIdentify().intValue() == 1) {
                                UserEntity ex_user = userMapper.getUserByIdentity(e.getIdentificationId());
                                if (ex_user != null) {
                                    userMapper.updateUserDossierId(ex_user.getId(), e.getProfileId());
                                    addHouseMember(o.get(), ex_user, adminId);
                                } else {
                                    UserEntity add_user = new UserEntity();
                                    //雪花id
                                    String userId = SnowflakeUtil.generateId();
                                    add_user.setId(userId);
                                    add_user.setName(e.getName());
                                    add_user.setIdentity(e.getIdentificationId());
                                    add_user.setIdentityType("身份证");
                                    add_user.setFaceImgUrl(e.getFaceImageUrl());
                                    add_user.setCreatorId(adminId);
                                    add_user.setDossierId(e.getProfileId());
                                    userMapper.addImportCarUser(add_user);

                                    addHouseMember(o.get(), add_user, adminId);

                                    //用户推到自主申报小程序
                                    UserTransformDTO userTransformDTO = new UserTransformDTO();
                                    userTransformDTO.setName(add_user.getName());
                                    userTransformDTO.setIdentity(add_user.getIdentity());
                                    userTransformDTO.setIdentityType(add_user.getIdentityType());
                                    userTransformDTO.setFaceImgUrl(add_user.getFaceImgUrl());
                                    thirdService.pushUserToStoremgmt(userTransformDTO);
                                }

                                //推送数据到mq
                                AreaCarMqDTO areaCarMqDTO = new AreaCarMqDTO();
                                areaCarMqDTO.setArea_id(area_location_id);
                                areaCarMqDTO.setDossier_id(e.getProfileId());
                                areaCarMqDTO.setId_card(e.getIdentificationId());
                                areaCarMqDTO.setUser_name(e.getName());
                                areaCarMqDTO.setCar_no(ex.getCarNo());
                                areaCarMqDTO.setHouse_id(o.get().getId());
                                areaCarMqDTO.setFace_image_url(e.getFaceImageUrl());
                                areaCarMqDTO.setType(0);
                                areaCarMqDTO.setUpdate_user(adminEntity.getName());
                                thirdService.sendAreaCarDataToMq(areaCarMqDTO);

                            } else {
                                UserEntity userEntity = userMapper.getUserByDossierId(e.getProfileId());
                                if (userEntity != null) {
                                    addHouseMember(o.get(), userEntity, adminId);
                                } else {
                                    UserEntity add_user = new UserEntity();
                                    //雪花id
                                    String userId = SnowflakeUtil.generateId();
                                    add_user.setId(userId);
                                    add_user.setFaceImgUrl(e.getFaceImageUrl());
                                    add_user.setCreatorId(adminId);
                                    add_user.setDossierId(e.getProfileId());
                                    userMapper.addImportCarUser(add_user);

                                    addHouseMember(o.get(), add_user, adminId);
                                }

                                //推送数据到mq
                                AreaCarMqDTO areaCarMqDTO = new AreaCarMqDTO();
                                areaCarMqDTO.setArea_id(area_location_id);
                                areaCarMqDTO.setDossier_id(e.getProfileId());
//                                areaCarMqDTO.setId_card(e.getIdentificationId());
//                                areaCarMqDTO.setUser_name(e.getName());
                                areaCarMqDTO.setCar_no(ex.getCarNo());
                                areaCarMqDTO.setHouse_id(o.get().getId());
                                areaCarMqDTO.setFace_image_url(e.getFaceImageUrl());
                                areaCarMqDTO.setType(0);
                                areaCarMqDTO.setUpdate_user(adminEntity.getName());
                                thirdService.sendAreaCarDataToMq(areaCarMqDTO);
                            }
                        });
                    }
                } else {
                    log.info("=======查无该房屋：" + house_name);
                }
            }
        }
        return new CommonResult();
    }

    /***
     * 获取当前待绑定车辆
     * @param area_location_id
     * @param car_no
     * @param pageNum
     * @param pageSize
     * @return
     */
    public CommonListResult getToBindingCarListByAreaId(String area_location_id, String car_no, int pageNum, int pageSize) {

        //计算数据开始索引
        int currIndex = (pageNum - 1) * pageSize;

        int totalNum = houseMapper.getToBindingCarListByAreaIdCount(area_location_id, car_no);
        //总页数
        int totalPageNum = (totalNum + pageSize - 1) / pageSize;

        List<CarTableEntity> list = houseMapper.getToBindingCarListByAreaId(area_location_id, car_no, currIndex, pageSize);

        return new CommonListResult(totalNum, totalPageNum, list);
    }

    /***
     *  获取小区车辆管理列表
     * @param area_location_id
     * @param param
     * @param pageNum
     * @param pageSize
     * @return
     */
    public CommonListResult getCarManagerListByAreaId(String area_location_id, String param, int pageNum, int pageSize) {
        //计算数据开始索引
        int currIndex = (pageNum - 1) * pageSize;

        int totalNum = houseMapper.getCarManagerListByAreaIdCount(area_location_id, param);
        //总页数
        int totalPageNum = (totalNum + pageSize - 1) / pageSize;

        List<Map> dataList = houseMapper.getCarManagerListByAreaId(area_location_id, param, currIndex, pageSize);

        dataList.forEach(e -> {
            if (e.get("phone") != null) {
                e.put("phone", PrivacyUtil.hidePhone(String.valueOf(e.get("phone"))));
            }
            if (e.get("name") != null) {
                e.put("name", PrivacyUtil.hideChineseName(String.valueOf(e.get("name"))));
            }
        });

        return new CommonListResult(totalNum, totalPageNum, dataList);
    }

    /***
     *修改车辆信息接口
     * @param carManageEntity
     * @return
     */
    public CommonResult UpdateCarManagerById(CarManageEntity carManageEntity) {
//        int is_exist = houseMapper.checkCarIsExist(carManageEntity.getAreaLocationId(), carManageEntity.getCarNumber(), carManageEntity.getId());
//        if (is_exist != 0)
//            return new CommonResult(CommonCode.OK, "该车牌信息已存在", null);
//        else {
        houseMapper.updateCarManagerById(carManageEntity);
        return new CommonResult(CommonCode.OK);
        //  }
    }

    @Transactional(rollbackFor = Exception.class)
    public CommonResult addCarManage(CarManageVO carManageVO) {
        CarTableEntity ex = houseMapper.getCarTableById(carManageVO.getCarTableId());

        int countCarManage = houseMapper.countHouseCarManage(carManageVO.getHouseId(), ex.getCarNo());
        if (countCarManage == 0) {
            CarManageEntity carManageEntity = new CarManageEntity();
            //雪花id
            String carManageId = SnowflakeUtil.generateId();
            carManageEntity.setId(carManageId);
            carManageEntity.setAreaLocationId(ex.getAreaLocationId());
            carManageEntity.setHouseId(carManageVO.getHouseId());
            carManageEntity.setHouseName(carManageVO.getHouseName());
            carManageEntity.setCarNumber(ex.getCarNo());
            if (StringUtils.isNotBlank(carManageVO.getName())) {
                carManageEntity.setName(carManageVO.getName());
            }
            if (StringUtils.isNotBlank(carManageVO.getPhone())) {
                carManageEntity.setPhone(carManageVO.getPhone());
            }
            carManageEntity.setParkNumber(carManageVO.getParkNumber());
            carManageEntity.setStartTime(carManageVO.getStartTime());
            carManageEntity.setEndTime(carManageVO.getEndTime());
            carManageEntity.setRemark(carManageVO.getRemark());

            houseMapper.addCarManage(carManageEntity);
        }
        houseMapper.updCarTableBind(carManageVO.getCarTableId());

        return new CommonResult();
    }


    public void addHouseMember(HouseEntity houseEntity, UserEntity userEntity, String adminId) {
        int ExistsHouseMemberNum = houseMapper.countExistsHouseMember(houseEntity.getId(), userEntity.getId());
        if (ExistsHouseMemberNum == 0) {
            //=====存house_member表=====
            HouseMemberEntity houseMemberEntity = new HouseMemberEntity();
            //雪花id
            String houseMemberId = SnowflakeUtil.generateId();
            houseMemberEntity.setId(houseMemberId);
            houseMemberEntity.setUserId(userEntity.getId());
            houseMemberEntity.setUserName(userEntity.getName());
            houseMemberEntity.setUrl(userEntity.getFaceImgUrl());
            houseMemberEntity.setAreaId(houseEntity.getAreaId());
            houseMemberEntity.setHouseId(houseEntity.getId());
            houseMemberEntity.setBuildingName(houseEntity.getName());
            houseMemberEntity.setType(0);
            houseMemberEntity.setStatus(1);
            houseMemberEntity.setCreator(adminId);
            houseMemberEntity.setDeleteStatus(0);
            houseMapper.insertHouseMember(houseMemberEntity);
        }

    }

    public void addHouseMember(CarHouseVO carHouseVO, UserEntity userEntity, String adminId) {
        int ExistsHouseMemberNum = houseMapper.countExistsHouseMember(carHouseVO.getHouseId(), userEntity.getId());
        if (ExistsHouseMemberNum == 0) {
            //=====存house_member表=====
            HouseMemberEntity houseMemberEntity = new HouseMemberEntity();
            //雪花id
            String houseMemberId = SnowflakeUtil.generateId();
            houseMemberEntity.setId(houseMemberId);
            houseMemberEntity.setUserId(userEntity.getId());
            houseMemberEntity.setUserName(userEntity.getName());
            houseMemberEntity.setUrl(userEntity.getFaceImgUrl());
            houseMemberEntity.setAreaId(carHouseVO.getAreaId());
            houseMemberEntity.setHouseId(carHouseVO.getHouseId());
            houseMemberEntity.setBuildingName(carHouseVO.getBuildingName());
            if (StringUtils.isNotBlank(carHouseVO.getRent_end_time())) {
                houseMemberEntity.setRentEndTime(carHouseVO.getRent_end_time());
            }
            if (StringUtils.isNoneBlank(carHouseVO.getRent_start_time())) {
                houseMemberEntity.setRentEndTime(carHouseVO.getRent_start_time());
            }
            houseMemberEntity.setType(carHouseVO.getType());
            houseMemberEntity.setStatus(1);
            houseMemberEntity.setCreator(adminId);
            houseMemberEntity.setDeleteStatus(0);
            houseMapper.insertHouseMember(houseMemberEntity);
        }

    }

    public CommonResult noRemind(Integer id) {
        if (Objects.isNull(id))
            return new CommonResult(CommonCode.PARAM_ERROR, "参数不能为空", null);
        else {
            houseMapper.updCarTableRemind(id);
            return new CommonResult(CommonCode.OK);
        }
    }

    @Async("wyyjsExecutor")
    public void handSyncCarTable() {
        List<XqConfigTableVO> list = thirdService.getXqConfigTable();
        list.forEach(e -> {
            JSONObject jsonObject = thirdService.getCarList(e.getArea_location_id(), 1);
            handleCarTableData(jsonObject);
            for (int i = 2; i < jsonObject.getInt("totalPageNum") + 1; i++) {
                JSONObject item = thirdService.getCarList(e.getArea_location_id(), i);
                handleCarTableData(item);
            }
        });
    }

    public void handleCarTableData(JSONObject data) {
        if (data != null) {
            JSONArray jsonArray = data.getJSONArray("cars");
            List<CarVO> list = JSONUtil.toList(jsonArray, CarVO.class);
            list.forEach(e -> {
                int ex_car_count = houseMapper.countAreaCarTableById(e.getId());
                if (ex_car_count == 0) {
                    houseMapper.addCarTable(e);
                } else {
                    houseMapper.updateCarTableTime(e.getId(), e.getAppear_days());
                }
                List<CarDetailVO> detail_list = e.getCarDetails();
                if (detail_list != null && detail_list.size() > 0) {
                    detail_list.forEach(y -> {
                        int detail_exist = houseMapper.countAreaCarDetailTableById(y.getId());
                        if (detail_exist == 0) {
                            houseMapper.addCarDetailTable(y);
                        } else {
                            houseMapper.updateCarTableTime(y.getId(), y.getAppear_days());
                        }
                    });
                }
                //同步过来的数据跟导入的car_manager表数据碰撞
                collisionCarManagerData(e, "1");
            });
        }
    }

    public void collisionCarManagerData(CarVO carVO, String adminId) {
        List<CarDetailVO> car_detail_list = carVO.getCarDetails();

        List<CarManageEntity> house_list = houseMapper.getCarManagerListByAreaAndCar(carVO.getArea_location_id(), carVO.getCar_no());
        house_list.forEach(y -> {
            car_detail_list.forEach(e -> {
                if (e.getIs_identify().intValue() == 1) {
                    UserEntity ex_user = userMapper.getUserByIdentity(e.getId_card());
                    if (ex_user != null) {
                        userMapper.updateUserDossierId(ex_user.getId(), e.getDossier_id());
                        houseUtil.addHouseMember(y.getHouseId(), ex_user, adminId);
                    } else {
                        UserEntity add_user = new UserEntity();
                        //雪花id
                        String userId = SnowflakeUtil.generateId();
                        add_user.setId(userId);
                        add_user.setName(e.getUser_name());
                        add_user.setIdentity(e.getId_card());
                        add_user.setIdentityType("身份证");
                        add_user.setFaceImgUrl(e.getFace_image_url());
                        add_user.setCreatorId(adminId);
                        add_user.setDossierId(e.getDossier_id());
                        userMapper.addImportCarUser(add_user);

                        houseUtil.addHouseMember(y.getHouseId(), add_user, adminId);

                        //用户推到自主申报小程序
                        UserTransformDTO userTransformDTO = new UserTransformDTO();
                        userTransformDTO.setName(add_user.getName());
                        userTransformDTO.setIdentity(add_user.getIdentity());
                        userTransformDTO.setIdentityType(add_user.getIdentityType());
                        userTransformDTO.setFaceImgUrl(add_user.getFaceImgUrl());
                        thirdService.pushUserToStoremgmt(userTransformDTO);
                    }

                    //推送数据到mq
                    AreaCarMqDTO areaCarMqDTO = new AreaCarMqDTO();
                    areaCarMqDTO.setArea_id(e.getArea_location_id());
                    areaCarMqDTO.setDossier_id(e.getDossier_id());
                    areaCarMqDTO.setId_card(e.getId_card());
                    areaCarMqDTO.setUser_name(e.getUser_name());
                    areaCarMqDTO.setCar_no(carVO.getCar_no());
                    areaCarMqDTO.setHouse_id(y.getHouseId());
                    areaCarMqDTO.setFace_image_url(e.getFace_image_url());
                    areaCarMqDTO.setType(0);
                    areaCarMqDTO.setUpdate_user("管理员");
                    thirdService.sendAreaCarDataToMq(areaCarMqDTO);

                } else {
                    UserEntity userEntity = userMapper.getUserByDossierId(e.getDossier_id());
                    if (userEntity != null) {
                        houseUtil.addHouseMember(y.getHouseId(), userEntity, adminId);
                    } else {
                        UserEntity add_user = new UserEntity();
                        //雪花id
                        String userId = SnowflakeUtil.generateId();
                        add_user.setId(userId);
                        add_user.setFaceImgUrl(e.getFace_image_url());
                        add_user.setCreatorId(adminId);
                        add_user.setDossierId(e.getDossier_id());
                        userMapper.addImportCarUser(add_user);

                        houseUtil.addHouseMember(y.getHouseId(), add_user, adminId);
                    }

                    //推送数据到mq
                    AreaCarMqDTO areaCarMqDTO = new AreaCarMqDTO();
                    areaCarMqDTO.setArea_id(e.getArea_location_id());
                    areaCarMqDTO.setDossier_id(e.getDossier_id());
//                                areaCarMqDTO.setId_card(e.getIdentificationId());
//                                areaCarMqDTO.setUser_name(e.getName());
                    areaCarMqDTO.setCar_no(carVO.getCar_no());
                    areaCarMqDTO.setHouse_id(y.getHouseId());
                    areaCarMqDTO.setFace_image_url(e.getFace_image_url());
                    areaCarMqDTO.setType(0);
                    areaCarMqDTO.setUpdate_user("管理员");
                    thirdService.sendAreaCarDataToMq(areaCarMqDTO);
                }
            });
        });

    }

    @Transactional(rollbackFor = Exception.class)
    public CommonResult bindingHouseByCar(CarHouseVO carHouseVO, String adminId) {
        //将车辆添加到car_manage表
        int countCarManage = houseMapper.countHouseCarManage(carHouseVO.getAreaId(), carHouseVO.getCarNo());
        UserEntity adminEntity = userMapper.selectUserById(adminId);
        if (countCarManage == 0) {
            CarManageEntity carManageEntity = new CarManageEntity();
            //雪花id
            String carManageId = SnowflakeUtil.generateId();
            carManageEntity.setId(carManageId);
            carManageEntity.setAreaLocationId(carHouseVO.getAreaId());
            carManageEntity.setHouseId(carHouseVO.getHouseId());
            carManageEntity.setHouseName(carHouseVO.getBuildingName());
            carManageEntity.setCarNumber(carHouseVO.getCarNo());
            if (StringUtils.isNotBlank(carHouseVO.getName())) {
                carManageEntity.setName(carHouseVO.getName());
            }
            if (StringUtils.isNotBlank(carHouseVO.getPhone())) {
                carManageEntity.setPhone(carHouseVO.getPhone());
            }
            carManageEntity.setParkNumber(carHouseVO.getParkNumber());
            carManageEntity.setStartTime(carHouseVO.getRent_start_time());
            carManageEntity.setEndTime(carHouseVO.getRent_end_time());
            carManageEntity.setRemark(carHouseVO.getRemark());
            carManageEntity.setIsDelete(0);

            houseMapper.addCarManage(carManageEntity);

        }
        //将车辆设置为已绑定
        houseMapper.updCarTableBind(carHouseVO.getId());

        List<CarDetailTableEntity> car_detail_list = houseMapper.getCarDetailTableByCarId(carHouseVO.getAreaId(), carHouseVO.getId());
        car_detail_list.forEach(e -> {
            if (e.getIsIdentify().intValue() == 1) {
                UserEntity ex_user = userMapper.getUserByIdentity(e.getIdentificationId());
                if (ex_user != null) {
                    userMapper.updateUserDossierId(ex_user.getId(), e.getProfileId());
                    addHouseMember(carHouseVO, ex_user, adminId);
                } else {
                    UserEntity add_user = new UserEntity();
                    //雪花id
                    String userId = SnowflakeUtil.generateId();
                    add_user.setId(userId);
                    add_user.setName(e.getName());
                    add_user.setIdentity(e.getIdentificationId());
                    add_user.setIdentityType("身份证");
                    add_user.setFaceImgUrl(e.getFaceImageUrl());
                    add_user.setCreatorId(adminId);
                    add_user.setDossierId(e.getProfileId());
                    userMapper.addImportCarUser(add_user);

                    addHouseMember(carHouseVO, add_user, adminId);

                    //用户推到自主申报小程序
                    UserTransformDTO userTransformDTO = new UserTransformDTO();
                    userTransformDTO.setName(add_user.getName());
                    userTransformDTO.setIdentity(add_user.getIdentity());
                    userTransformDTO.setIdentityType(add_user.getIdentityType());
                    userTransformDTO.setFaceImgUrl(add_user.getFaceImgUrl());
                    thirdService.pushUserToStoremgmt(userTransformDTO);
                }

                //推送数据到mq
                AreaCarMqDTO areaCarMqDTO = new AreaCarMqDTO();
                areaCarMqDTO.setArea_id(carHouseVO.getAreaId());
                areaCarMqDTO.setDossier_id(e.getProfileId());
                areaCarMqDTO.setId_card(e.getIdentificationId());
                areaCarMqDTO.setUser_name(e.getName());
                areaCarMqDTO.setCar_no(carHouseVO.getCarNo());
                areaCarMqDTO.setHouse_id(carHouseVO.getHouseId());
                areaCarMqDTO.setFace_image_url(e.getFaceImageUrl());
                areaCarMqDTO.setType(0);
                areaCarMqDTO.setUpdate_user(adminEntity.getName());
                thirdService.sendAreaCarDataToMq(areaCarMqDTO);

            } else {
                UserEntity userEntity = userMapper.getUserByDossierId(e.getProfileId());
                if (userEntity != null) {
                    addHouseMember(carHouseVO, userEntity, adminId);
                } else {
                    UserEntity add_user = new UserEntity();
                    //雪花id
                    String userId = SnowflakeUtil.generateId();
                    add_user.setId(userId);
                    add_user.setFaceImgUrl(e.getFaceImageUrl());
                    add_user.setCreatorId(adminId);
                    add_user.setDossierId(e.getProfileId());
                    userMapper.addImportCarUser(add_user);

                    addHouseMember(carHouseVO, add_user, adminId);
                }

                //推送数据到mq
                AreaCarMqDTO areaCarMqDTO = new AreaCarMqDTO();
                areaCarMqDTO.setArea_id(carHouseVO.getAreaId());
                areaCarMqDTO.setDossier_id(e.getProfileId());
//                                areaCarMqDTO.setId_card(e.getIdentificationId());
//                                areaCarMqDTO.setUser_name(e.getName());
                areaCarMqDTO.setCar_no(carHouseVO.getCarNo());
                areaCarMqDTO.setHouse_id(carHouseVO.getHouseId());
                areaCarMqDTO.setFace_image_url(e.getFaceImageUrl());
                areaCarMqDTO.setType(0);
                areaCarMqDTO.setUpdate_user(adminEntity.getName());
                thirdService.sendAreaCarDataToMq(areaCarMqDTO);
            }
        });

        return new CommonResult();
    }

    public CommonResult updAlarmQcb(AlarmQcbVO alarmQcbVO, String adminId) {
        AlarmQcbEntity ex_alarm = houseMapper.getAreaAlarmQcbById(alarmQcbVO.getId());
        UserEntity adminEntity = userMapper.selectUserById(adminId);
        if (ex_alarm == null) {
            return new CommonResult(CommonCode.PARAM_ERROR);
        }
        // 内部车
        if (alarmQcbVO.getCarType().intValue() == 1) {

            AlarmQcbEntity alarmQcbEntity = new AlarmQcbEntity();
            alarmQcbEntity.setId(alarmQcbVO.getId());
            alarmQcbEntity.setHouseId(alarmQcbVO.getHouseId());
            alarmQcbEntity.setCarType(alarmQcbVO.getCarType());
            alarmQcbEntity.setUpdateUser(adminEntity.getName());
            boolean success = houseMapper.updateAlarmQcb(alarmQcbEntity);
            if (success) {
                UserEntity userEntity = new UserEntity();
                userEntity.setName(ex_alarm.getUserName());
                userEntity.setIdentity(ex_alarm.getIdCard());
                userEntity.setIdentityType("身份证");
                userEntity.setPhone(ex_alarm.getPhone());
                userEntity.setFaceImgUrl(ex_alarm.getFaceImageUrl());
                userEntity.setDossierId(ex_alarm.getDossierId());
                houseUtil.syncQcbUser(userEntity);
                UserEntity ex_user = userMapper.getUserByDossierId(ex_alarm.getDossierId());
                if (ex_user != null) {
                    houseUtil.addHouseMember(alarmQcbVO.getHouseId(), ex_user, "1");
                }

                int countCarManage = houseMapper.countHouseCarManage(alarmQcbVO.getHouseId(), ex_alarm.getCarNo());
                if (countCarManage == 0) {
                    CarManageEntity carManageEntity = new CarManageEntity();
                    //雪花id
                    String carManageId = SnowflakeUtil.generateId();
                    carManageEntity.setId(carManageId);
                    carManageEntity.setAreaLocationId(ex_alarm.getAreaId());
                    carManageEntity.setHouseId(alarmQcbVO.getHouseId());
                    HouseEntity houseEntity = houseMapper.getHouseById(alarmQcbVO.getHouseId());
                    HouseEntity p_house = houseMapper.getHouseById(houseEntity.getParentId());
                    if (p_house.getHouseType() == 1) {
                        HouseEntity b_house = houseMapper.getHouseById(p_house.getParentId());
                        carManageEntity.setHouseName(b_house.getName() + p_house.getName() + houseEntity.getName());
                    } else {
                        carManageEntity.setHouseName(p_house.getName() + houseEntity.getName());
                    }
                    carManageEntity.setCarNumber(ex_alarm.getCarNo());
                    if (StringUtils.isNotBlank(ex_alarm.getUserName())) {
                        carManageEntity.setName(ex_alarm.getUserName());
                    }
                    if (StringUtils.isNotBlank(alarmQcbVO.getPhone())) {
                        carManageEntity.setPhone(alarmQcbVO.getPhone());
                    } else {
                        carManageEntity.setPhone(ex_alarm.getPhone());
                    }
                    carManageEntity.setStartTime(alarmQcbVO.getStartTime());
                    carManageEntity.setEndTime(alarmQcbVO.getEndTime());
                    carManageEntity.setParkNumber(alarmQcbVO.getParkNumber());
                    carManageEntity.setIsDelete(0);
                    houseMapper.addCarManage(carManageEntity);
                }

                //推送数据到mq
                AreaCarMqDTO areaCarMqDTO = new AreaCarMqDTO();
                areaCarMqDTO.setArea_id(ex_alarm.getAreaId());
                areaCarMqDTO.setDossier_id(ex_alarm.getDossierId());
                areaCarMqDTO.setId_card(ex_alarm.getIdCard());
                areaCarMqDTO.setUser_name(ex_alarm.getUserName());
                areaCarMqDTO.setCar_no(ex_alarm.getCarNo());
                areaCarMqDTO.setHouse_id(alarmQcbVO.getHouseId());
                areaCarMqDTO.setFace_image_url(ex_alarm.getFaceImageUrl());
                areaCarMqDTO.setType(2);
                areaCarMqDTO.setCar_type(1);
                areaCarMqDTO.setUpdate_user(adminEntity.getName());
                thirdService.sendAreaCarDataToMq(areaCarMqDTO);
            }
            // 访客车
        } else if (alarmQcbVO.getCarType().intValue() == 2) {

            AlarmQcbEntity alarmQcbEntity = new AlarmQcbEntity();
            alarmQcbEntity.setId(alarmQcbVO.getId());
            alarmQcbEntity.setHouseId(alarmQcbVO.getHouseId());
            alarmQcbEntity.setCarType(alarmQcbVO.getCarType());
            alarmQcbEntity.setUpdateUser(adminEntity.getName());
            boolean success = houseMapper.updateAlarmQcb(alarmQcbEntity);
            if (success) {

                VisitRecordEntity visitRecordEntity = new VisitRecordEntity();
                //雪花id
                String recordId = SnowflakeUtil.generateId();
                visitRecordEntity.setId(recordId);
                visitRecordEntity.setHouseId(alarmQcbVO.getHouseId());
                HouseEntity houseEntity = houseMapper.getHouseById(alarmQcbVO.getHouseId());
                HouseEntity p_house = houseMapper.getHouseById(houseEntity.getParentId());
                if (p_house.getHouseType() == 1) {
                    HouseEntity b_house = houseMapper.getHouseById(p_house.getParentId());
                    visitRecordEntity.setHouseName(b_house.getName() + p_house.getName() + houseEntity.getName());
                } else {
                    visitRecordEntity.setHouseName(p_house.getName() + houseEntity.getName());
                }
                visitRecordEntity.setAreaLocationId(ex_alarm.getAreaId());
                visitRecordEntity.setCarNo(ex_alarm.getCarNo());
                visitRecordEntity.setType(1);
                visitRecordMapper.insertVisitRecord(visitRecordEntity);

                //推送数据到mq
                AreaCarMqDTO areaCarMqDTO = new AreaCarMqDTO();
                areaCarMqDTO.setArea_id(ex_alarm.getAreaId());
                areaCarMqDTO.setDossier_id(ex_alarm.getDossierId());
                areaCarMqDTO.setId_card(ex_alarm.getIdCard());
                areaCarMqDTO.setUser_name(ex_alarm.getUserName());
                areaCarMqDTO.setCar_no(ex_alarm.getCarNo());
                areaCarMqDTO.setHouse_id(alarmQcbVO.getHouseId());
                areaCarMqDTO.setFace_image_url(ex_alarm.getFaceImageUrl());
                areaCarMqDTO.setType(2);
                areaCarMqDTO.setCar_type(2);
                areaCarMqDTO.setUpdate_user(adminEntity.getName());
                thirdService.sendAreaCarDataToMq(areaCarMqDTO);
            }

            // 其他车
        } else if (alarmQcbVO.getCarType().intValue() == 3) {

            AlarmQcbEntity alarmQcbEntity = new AlarmQcbEntity();
            alarmQcbEntity.setId(alarmQcbVO.getId());
            alarmQcbEntity.setCarType(alarmQcbVO.getCarType());
            alarmQcbEntity.setUpdateUser(adminEntity.getName());
            boolean success = houseMapper.updateAlarmQcb(alarmQcbEntity);
            if (success) {
                //推送数据到mq
                AreaCarMqDTO areaCarMqDTO = new AreaCarMqDTO();
                areaCarMqDTO.setArea_id(ex_alarm.getAreaId());
                areaCarMqDTO.setDossier_id(ex_alarm.getDossierId());
                areaCarMqDTO.setId_card(ex_alarm.getIdCard());
                areaCarMqDTO.setUser_name(ex_alarm.getUserName());
                areaCarMqDTO.setCar_no(ex_alarm.getCarNo());
                areaCarMqDTO.setFace_image_url(ex_alarm.getFaceImageUrl());
                areaCarMqDTO.setType(2);
                areaCarMqDTO.setCar_type(3);
                areaCarMqDTO.setUpdate_user(adminEntity.getName());
                thirdService.sendAreaCarDataToMq(areaCarMqDTO);
            }
        }
        return new CommonResult(CommonCode.OK);
    }

    public CommonResult getAlarmQcbListByAreaId(String area_location_id, Integer status, String param, int pageNum, int pageSize) {
        //计算数据开始索引
        int currIndex = (pageNum - 1) * pageSize;

        int totalNum = houseMapper.getAlarmQcbListCountByAreaId(area_location_id, status, param);
        //总页数
        int totalPageNum = (totalNum + pageSize - 1) / pageSize;

        List<AlarmQcbEntity> dataList = houseMapper.getAlarmQcbListByAreaId(area_location_id, status, param, currIndex, pageSize);


        return new CommonResult(new CommonListResult(totalNum, totalPageNum, dataList));
    }

}
