package com.jkga.wyyjs.aspect;

import com.jkga.wyyjs.model.entity.SysOperateLogEntity;
import com.jkga.wyyjs.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;

@Slf4j
@Aspect
@Component
public class LogAspect {

    @Autowired
    private AnsyRecordOperateLog ansyRecordOperateLog;

    @Autowired
    private HttpServletRequest request;


    @Pointcut("@annotation(com.jkga.wyyjs.aspect.RecordLog)")
    public void pointcut() {
    }

    @Around("pointcut()")
    public Object around(ProceedingJoinPoint point) {
        Object obj = null;
        long beginTime = System.currentTimeMillis();
        // 执行控制器方法
        try {
            obj = point.proceed();
        } catch (Throwable throwable) {

        }
        //异步记录操作日志 不影响主程序
        try {
            //响应时长(毫秒)
            long time = System.currentTimeMillis() - beginTime;
            SysOperateLogEntity logEntity = new SysOperateLogEntity();
            logEntity.setIntime(DateUtils.getTime());
            logEntity.setResponseTime(time);
            String uid = request.getHeader("uid");
            String ip = request.getHeader("ip");
            ansyRecordOperateLog.log_info("========操作用户：" + uid);
            logEntity.setUserId(uid);
            logEntity.setIp(ip);
            String requestUri = request.getRequestURI();
            //请求地址
            ansyRecordOperateLog.log_info("=========请求地址：" + requestUri);
            logEntity.setRequestUri(requestUri);
            logEntity.setMethodType(request.getMethod());
            logEntity.setContentType(request.getContentType());
            ansyRecordOperateLog.saveOperateInfo(point, logEntity, obj);
        } catch (Exception e) {
            log.info("=======记录日志异常：" + e.getMessage());
        }
        return obj;
    }


}
