package com.jkga.wyyjs.service;

import com.jkga.wyyjs.model.CommonResult;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.List;
import java.util.Map;

/**
 * TeamManagementService权限相关测试类
 * 
 * @Author：clyde
 * @Date：2025/7/18
 */
@SpringBootTest
@ActiveProfiles("dev")
public class TeamManagementServicePermissionTest {

    @Autowired
    private TeamManagementService teamManagementService;

    /**
     * 测试获取用户中介权限（修改后的版本）
     */
    @Test
    public void testGetUserIntermediaryPermissions() {
        String userId = "test_user_001";
        
        System.out.println("测试获取用户中介权限（包含place_name）: userId=" + userId);
        
        try {
            CommonResult result = teamManagementService.getUserIntermediaryPermissions(userId);
            
            System.out.println("返回结果: " + result);
            System.out.println("状态码: " + result.getCode());
            System.out.println("消息: " + result.getMsg());
            
            if (result.getData() != null) {
                @SuppressWarnings("unchecked")
                List<Map<String, Object>> placeList = (List<Map<String, Object>>) result.getData();
                
                System.out.println("中介权限场所数量: " + placeList.size());
                for (Map<String, Object> place : placeList) {
                    System.out.println("场所信息:");
                    System.out.println("  place_id: " + place.get("place_id"));
                    System.out.println("  place_name: " + place.get("place_name"));
                }
            } else {
                System.out.println("该用户没有中介权限");
            }
            
        } catch (Exception e) {
            System.err.println("测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试获取用户权限类型信息
     */
    @Test
    public void testGetUserPermissionTypes() {
        String userId = "test_user_001";
        
        System.out.println("测试获取用户权限类型信息: userId=" + userId);
        
        try {
            CommonResult result = teamManagementService.getUserPermissionTypes(userId);
            
            System.out.println("返回结果: " + result);
            System.out.println("状态码: " + result.getCode());
            System.out.println("消息: " + result.getMsg());
            
            if (result.getData() != null) {
                @SuppressWarnings("unchecked")
                List<Integer> typeList = (List<Integer>) result.getData();
                
                System.out.println("用户权限类型: " + typeList);
                
                if (typeList.isEmpty()) {
                    System.out.println("用户没有管理权限，只有业主端权限");
                } else {
                    for (Integer type : typeList) {
                        if (type == 0) {
                            System.out.println("- 拥有物业管理权限");
                        } else if (type == 1) {
                            System.out.println("- 拥有中介管理权限");
                        }
                    }
                }
            }
            
        } catch (Exception e) {
            System.err.println("测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试不同权限组合的用户
     */
    @Test
    public void testDifferentPermissionCombinations() {
        String[] testUsers = {
            "property_only_user",    // 只有物业权限
            "intermediary_only_user", // 只有中介权限
            "both_permission_user",   // 同时拥有物业和中介权限
            "no_permission_user"      // 没有管理权限
        };
        
        for (String userId : testUsers) {
            System.out.println("\n测试用户: " + userId);
            
            try {
                CommonResult result = teamManagementService.getUserPermissionTypes(userId);
                
                if (result.getData() != null) {
                    @SuppressWarnings("unchecked")
                    List<Integer> typeList = (List<Integer>) result.getData();
                    
                    System.out.println("权限类型: " + typeList);
                    
                    if (typeList.isEmpty()) {
                        System.out.println("结果: 只有业主端权限");
                    } else if (typeList.contains(0) && typeList.contains(1)) {
                        System.out.println("结果: 同时拥有物业和中介管理权限");
                    } else if (typeList.contains(0)) {
                        System.out.println("结果: 只有物业管理权限");
                    } else if (typeList.contains(1)) {
                        System.out.println("结果: 只有中介管理权限");
                    }
                }
                
            } catch (Exception e) {
                System.err.println("测试用户 " + userId + " 失败: " + e.getMessage());
            }
        }
    }

    /**
     * 测试中介权限返回格式验证
     */
    @Test
    public void testIntermediaryPermissionFormat() {
        String userId = "test_intermediary_user";
        
        System.out.println("测试中介权限返回格式验证");
        
        try {
            CommonResult result = teamManagementService.getUserIntermediaryPermissions(userId);
            
            // 验证基本结构
            assert result != null : "结果不能为空";
            assert result.getCode() == 200 : "状态码应该为200";
            
            if (result.getData() != null) {
                @SuppressWarnings("unchecked")
                List<Map<String, Object>> placeList = (List<Map<String, Object>>) result.getData();
                
                for (Map<String, Object> place : placeList) {
                    // 验证每个场所信息的结构
                    assert place.containsKey("place_id") : "应包含place_id字段";
                    assert place.containsKey("place_name") : "应包含place_name字段";
                    
                    String placeId = (String) place.get("place_id");
                    String placeName = (String) place.get("place_name");
                    
                    assert placeId != null : "place_id不能为null";
                    // place_name可能为null，因为历史数据可能没有这个字段
                    
                    System.out.println("验证通过 - place_id: " + placeId + ", place_name: " + placeName);
                }
            }
            
            System.out.println("中介权限返回格式验证通过！");
            
        } catch (Exception e) {
            System.err.println("格式验证失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试权限类型返回格式验证
     */
    @Test
    public void testPermissionTypeFormat() {
        String userId = "test_user_001";
        
        System.out.println("测试权限类型返回格式验证");
        
        try {
            CommonResult result = teamManagementService.getUserPermissionTypes(userId);
            
            // 验证基本结构
            assert result != null : "结果不能为空";
            assert result.getCode() == 200 : "状态码应该为200";
            assert result.getData() != null : "数据不能为空";
            
            @SuppressWarnings("unchecked")
            List<Integer> typeList = (List<Integer>) result.getData();
            
            // 验证数据类型和内容
            for (Integer type : typeList) {
                assert type != null : "type不能为null";
                assert type == 0 || type == 1 : "type只能是0或1";
            }
            
            // 验证排序（应该是升序）
            for (int i = 1; i < typeList.size(); i++) {
                assert typeList.get(i) >= typeList.get(i-1) : "type列表应该是升序排列";
            }
            
            // 验证去重（不应该有重复值）
            long distinctCount = typeList.stream().distinct().count();
            assert distinctCount == typeList.size() : "type列表不应该有重复值";
            
            System.out.println("权限类型返回格式验证通过！");
            
        } catch (Exception e) {
            System.err.println("格式验证失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
