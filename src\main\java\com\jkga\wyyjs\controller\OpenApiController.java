package com.jkga.wyyjs.controller;

import com.jkga.wyyjs.model.CommonCode;
import com.jkga.wyyjs.model.CommonResult;
import com.jkga.wyyjs.model.dto.UserTransformDTO;
import com.jkga.wyyjs.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * 开放接口
 * @Author：clyde
 * @Date：2025/2/27 14:00
 */
@Slf4j
@CrossOrigin
@RestController
@RequestMapping("openApi")
public class OpenApiController {

    @Autowired
    private UserService userService;

    @Autowired
    HttpServletRequest request;

    /**
     * 接收同步小程序用户
     *
     * @Author：clyde
     * @Date：2025/4/9 14:16
     */
    @RequestMapping(value = "syncStoremgmtUser", method = RequestMethod.POST)
    public CommonResult syncStoremgmtUser(@RequestBody UserTransformDTO userTransformDTO) {
        try {
            return new CommonResult(userService.syncStoremgmtUser(userTransformDTO));
        } catch (Exception e) {
            return new CommonResult(CommonCode.SYSTEM_ERROR);
        }
    }




}
