package com.jkga.wyyjs.task;

import com.jkga.wyyjs.mapper.SubletMapper;
import com.jkga.wyyjs.model.entity.SubletHouseEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * @Author：clyde
 * @Date：2025/7/23 15:01
 */
@Slf4j
@Component
public class SubletHouseTask {

    @Autowired
    private SubletMapper subletMapper;

    /**
     * 二房东模块（定时设置当前时间超出离开日期的【在住】租客记录状态为【离开】）
     */
    @Scheduled(initialDelay = 1000, fixedRate = 60 * 60 * 1000) //每3600秒（1小时）执行一次
    public void setSubletRenterLeaveStatusTask() {
        Thread currentThread = Thread.currentThread();
        long threadId = currentThread.getId();
        String threadName = currentThread.getName();
        log.info("=====开始【二房东模块】租客离开状态设置定时任务--->线程ID：" + threadId + " 线程名：" + threadName);
        subletMapper.batchUpdateSubletRenterLeaveStatus();
        log.info("=====结束【二房东模块】租客离开状态设置定时任务=====");
    }

    /**
     * 二房东模块（定时解绑房源绑定的业主信息并插入历史表）
     */
//    @Scheduled(initialDelay = 1000, fixedRate = 60 * 60 * 1000) //每3600秒（1小时）执行一次
    @Scheduled(cron = "0 0 1 * * ?")     //每天凌晨一点运行
    @Transactional(rollbackFor = Exception.class)
    public void unbindSubletHouseOwnerTask() {
        Thread currentThread = Thread.currentThread();
        long threadId = currentThread.getId();
        String threadName = currentThread.getName();
        log.info("=====开始【二房东模块】房源解绑业主定时任务--->线程ID：" + threadId + " 线程名：" + threadName);
        //查询已到期的房源列表
        List<SubletHouseEntity> houseList = subletMapper.getExpireSubletHouseOwnerList();
        if (houseList != null && houseList.size() > 0) {
            //解绑房源上的业主和合同
            subletMapper.unbindExpireSubletHouseOwner(houseList);
            //插入历史表
            subletMapper.batchInsertExpireSubletHouseOwnerToHistory(houseList);
        }
        log.info("=====结束【二房东模块】房源解绑业主定时任务=====");
    }
}
