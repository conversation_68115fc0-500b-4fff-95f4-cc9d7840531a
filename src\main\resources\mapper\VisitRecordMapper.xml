<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jkga.wyyjs.mapper.VisitRecordMapper">

    <resultMap id="VisitRecordResultMap" type="com.jkga.wyyjs.model.entity.VisitRecordEntity">
        <result column="visited_name" jdbcType="VARCHAR" property="visitedName"
                typeHandler="com.jkga.wyyjs.component.SM4TypeHandler"/>
        <result column="visited_phone" jdbcType="VARCHAR" property="visitedPhone"
                typeHandler="com.jkga.wyyjs.component.SM4TypeHandler"/>
        <result column="car_no" jdbcType="VARCHAR" property="carNo"
                typeHandler="com.jkga.wyyjs.component.SM4TypeHandler"/>
    </resultMap>

    <insert id="insertVisitRecord" parameterType="com.jkga.wyyjs.model.entity.VisitRecordEntity">
        INSERT INTO "visit_record" ("id", "area_location_id", "house_id", "house_name", "visited_name", "visited_phone", "visited_reason", "car_no", "car_image", "people_image", "visit_time", "type")
        VALUES (#{id}, #{areaLocationId}, #{houseId}, #{houseName}, #{visitedName, typeHandler=com.jkga.wyyjs.component.SM4TypeHandler}, #{visitedPhone, typeHandler=com.jkga.wyyjs.component.SM4TypeHandler}, #{visitedReason}, #{carNo, typeHandler=com.jkga.wyyjs.component.SM4TypeHandler}, #{carImage}, #{peopleImage}, NOW(), #{type})
    </insert>

    <select id="getRecordList" resultMap="VisitRecordResultMap">
        select * from "visit_record" where "area_location_id" = #{areaId}
        <if test="type != null">
            AND "type" = #{type}
        </if>
        <if test="searchParam != null and searchParam != ''">
            AND ("car_no" = #{searchParam, typeHandler=com.jkga.wyyjs.component.SM4TypeHandler} or  "house_name" like concat('%',#{searchParam},'%'))
        </if>
        <if test="pageSize != 0">
            LIMIT #{currIndex},#{pageSize}
        </if>
    </select>

    <select id="countRecord" resultType="int">
        select count(1) from "visit_record" where "area_location_id" = #{areaId}
        <if test="type != null">
            AND "type" = #{type}
        </if>
        <if test="searchParam != null and searchParam != ''">
            AND ("car_no" = #{searchParam, typeHandler=com.jkga.wyyjs.component.SM4TypeHandler}  or  "house_name" like concat('%',#{searchParam},'%'))
        </if>
    </select>



</mapper>