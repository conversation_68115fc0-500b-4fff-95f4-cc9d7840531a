<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jkga.wyyjs.mapper.HouseMapper">

    <resultMap id="returnHouseMemberMap" type="com.jkga.wyyjs.model.dto.HouseMemberDTO">
        <id property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="url" column="url"/>
        <result property="areaId" column="area_id"/>
        <result property="houseId" column="house_id"/>
        <result property="buildingName" column="building_name"/>
        <result property="type" column="type"/>
        <result property="status" column="status"/>
        <result property="creator" column="creator"/>
        <result property="createTime" column="create_time"/>
        <result property="reviewer" column="reviewer"/>
        <result property="reviewTime" column="review_time"/>
        <result property="deleteStatus" column="delete_status"/>
        <result property="rentStartTime" column="rent_start_time"/>
        <result property="rentEndTime" column="rent_end_time"/>

        <result column="user_name" jdbcType="VARCHAR" property="userName"
                typeHandler="com.jkga.wyyjs.component.SM4TypeHandler"/>
        <result column="phone" jdbcType="VARCHAR" property="phone"
                typeHandler="com.jkga.wyyjs.component.SM4TypeHandler"/>
        <result column="reviewer_name" jdbcType="VARCHAR" property="reviewerName"
                typeHandler="com.jkga.wyyjs.component.SM4TypeHandler"/>
        <result property="areaName" column="area_name"/>
    </resultMap>

    <resultMap id="HouseResultMap" type="com.jkga.wyyjs.model.entity.HouseEntity">
        <id property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="parentId" column="parent_id"/>
        <result property="areaId" column="area_id"/>
        <result property="houseType" column="house_type"/>
        <result property="nameFull" column="name_full"/>
    </resultMap>

    <resultMap id="HouseMemberResultMap" type="com.jkga.wyyjs.model.entity.HouseMemberEntity">
        <id property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="userName" column="user_name"/>
        <result property="url" column="url"/>
        <result property="areaId" column="area_id"/>
        <result property="houseId" column="house_id"/>
        <result property="buildingName" column="building_name"/>
        <result property="type" column="type"/>
        <result property="status" column="status"/>
        <result property="creator" column="creator"/>
        <result property="createTime" column="create_time"/>
        <result property="reviewer" column="reviewer"/>
        <result property="reviewTime" column="review_time"/>
        <result property="deleteStatus" column="delete_status"/>
        <result property="rentStartTime" column="rent_start_time"/>
        <result property="rentEndTime" column="rent_end_time"/>
    </resultMap>

    <resultMap id="returnAlarmQcbMap" type="com.jkga.wyyjs.model.entity.AlarmQcbEntity">

        <result column="user_name" jdbcType="VARCHAR" property="userName"
                typeHandler="com.jkga.wyyjs.component.SM4TypeHandler"/>
        <result column="phone" jdbcType="VARCHAR" property="phone"
                typeHandler="com.jkga.wyyjs.component.SM4TypeHandler"/>
        <result column="id_card" jdbcType="VARCHAR" property="idCard"
                typeHandler="com.jkga.wyyjs.component.SM4TypeHandler"/>
        <result column="car_no" jdbcType="VARCHAR" property="carNo"
                typeHandler="com.jkga.wyyjs.component.SM4TypeHandler"/>
    </resultMap>

    <select id="listHouseMember" resultMap="returnHouseMemberMap">
        select hm.*,u."phone",a."name" area_name,u1."name" reviewer_name from "house_member" hm
        join "area" a on hm."area_id" = a."id"
        join "user" u on hm."user_id" = u."id"
        left join "user" u1 on hm."reviewer" = u1."id"
        <where>
            hm."delete_status" = 0 and hm."area_id" = #{area_id}
            <if test="status != null">
                and hm."status" = #{status}
            </if>
        </where>
        <if test="pageSize != 0">
            LIMIT #{currIndex},#{pageSize}
        </if>
    </select>

    <select id="countHouseMember" resultType="java.lang.Integer">
        select count(1) from "house_member"
        <where>
            "delete_status" = 0 and "area_id" = #{area_id}
            <if test="status != null">
                and "status" = #{status}
            </if>
        </where>
    </select>

    <insert id="insertHouseMember">
        insert into "house_member"(
        "id",
        "user_id",
        "user_name",
        "url",
        "area_id",
        "house_id",
        "building_name",
        "type",
        "status",
        "creator",
        "create_time",
        "delete_status",
        "rent_start_time",
        "rent_end_time"
        )values(
        #{id},
        #{userId},
        #{userName,typeHandler=com.jkga.wyyjs.component.SM4TypeHandler},
        #{url},
        #{areaId},
        #{houseId},
        #{buildingName},
        #{type},
        #{status},
        #{creator},
        NOW(),
        #{deleteStatus},
        #{rentStartTime},
        #{rentEndTime}
        )
    </insert>

    <update id="reviewHouseMember">
        update "house_member" set
        "status" = #{status},
        "reviewer" = #{adminId},
        "review_time" = NOW()
        where "id" = #{houseMemberId}
    </update>

    <select id="countExistsHouseMember" resultType="java.lang.Integer">
        select count(1) from "house_member" where "user_id" = #{userId} and "house_id" = #{houseId}
        and "delete_status" = 0 and ("status" = 0 or "status" = 1)
    </select>

    <select id="getHouseListByAreaId" resultMap="HouseResultMap">
        select * from "house" where "area_id" = #{areaId} and "delete_status" = 0
    </select>

    <select id="listUserHouse" resultType="com.jkga.wyyjs.model.dto.UserHouseDTO">
        select h."area_id",
               hm."house_id",
               a."name" as "area_name",
               hm."building_name" as "house_name"
          from "house_member" hm
          join "house" h
            on hm."house_id" = h."id"
          join "area" a
            on h."area_id" = a."id"
         where hm."user_id" = #{userId}
           and hm."status" = 1
           and hm."delete_status" = 0
           and h."delete_status" = 0
           and a."delete_status" = 0
    </select>

    <select id="countHouseByArea" resultType="int">
        select COUNT(1) from "house" where "area_id" = #{areaId} and "delete_status" = 0 and "house_type" = 2
    </select>

    <select id="countApplyHouseByArea" resultType="int">
        select count(DISTINCT "house_id") from "house_member" where "area_id" = #{areaId} and "status" = 1 AND "delete_status" = 0
    </select>

    <select id="countHouseMemberByArea" resultType="int">
       select count(1) from "house_member" where "area_id" = #{areaId} and "status" = 1 AND "delete_status" = 0
    </select>

    <select id="getHouseMemberListByArea" resultMap="HouseMemberResultMap">
        select * from "house_member" where "area_id" = #{areaId} and "status" = 1 AND "delete_status" = 0
    </select>

    <select id="getBuildingByAreaId" resultMap="HouseResultMap">
        select * from "house" where "area_id" = #{areaId} and "house_type" = 0 and "delete_status" = 0
    </select>

    <select id="getHouseByBuildingId" resultMap="HouseResultMap">
        select * from "house" where "parent_id" like concat(#{buildingId},'%')  and "delete_status" = 0
    </select>

    <select id="getHouseMemberListByHouseId" resultMap="returnHouseMemberMap">
        select hm.*,u."phone",a."name" area_name,u1."name" reviewer_name from "house_member" hm
        join "area" a on hm."area_id" = a."id"
        join "user" u on hm."user_id" = u."id"
        left join "user" u1 on hm."reviewer" = u1."id"
        where hm."house_id" = #{houseId} and hm."delete_status" = 0 and hm."status" = 1
    </select>

    <select id="getCarTableByNo" resultType="com.jkga.wyyjs.model.entity.CarTableEntity">
        select * from "car_table" where "area_location_id" = #{areaId} and "car_no" = #{car_no} limit 1
    </select>

    <select id="getCarDetailTableByCarId" resultType="com.jkga.wyyjs.model.entity.CarDetailTableEntity">
        select * from "car_detial_table" where "area_location_id" = #{areaId} and "car_id" = #{car_id}
    </select>

    <select id="getToBindingCarListByAreaId" resultType="com.jkga.wyyjs.model.entity.CarTableEntity">
        select * from "car_table" where "area_location_id" = #{area_location_id} AND "is_binding" = 0 AND "is_remind" !=
        1
        <if test="car_no != null and car_no != ''">
            AND "car_no" = #{car_no}
        </if>
        <if test="pageSize != 0">
            LIMIT #{currIndex},#{pageSize}
        </if>
    </select>

    <insert id="addCarManage" parameterType="com.jkga.wyyjs.model.entity.CarManageEntity">
        insert into "car_manage"("id", "area_location_id", "house_id", "house_name", "car_number", "user_id", "name", "phone", "park_number", "start_time", "end_time", "remark", "is_delete")
        VALUES (#{id}, #{areaLocationId}, #{houseId}, #{houseName}, #{carNumber}, #{userId}, #{name,typeHandler=com.jkga.wyyjs.component.SM4TypeHandler}, #{phone,typeHandler=com.jkga.wyyjs.component.SM4TypeHandler}, #{parkNumber}, #{startTime}, #{endTime}, #{remark}, #{isDelete})
    </insert>

    <update id="updCarTableBind">
        update "car_table" set "is_binding" = 1 where "id" = #{id}
    </update>

    <select id="getCarTableById" resultType="com.jkga.wyyjs.model.entity.CarTableEntity">
       select * from "car_table" where "id" = #{id}
    </select>

    <select id="getToBindingCarListByAreaIdCount" resultType="java.lang.Integer">
        select count(*) from "car_table" where "area_location_id" = #{area_location_id} AND "is_binding" = 0 and
        "is_remind" != 1
        <if test="car_no != null and car_no != ''">
            AND "car_no" = #{car_no}
        </if>
    </select>

    <resultMap id="CarManagerInfo" type="java.util.Map">
        <result column="name" jdbcType="VARCHAR" property="name"
                typeHandler="com.jkga.wyyjs.component.SM4TypeHandler"/>
        <result column="phone" jdbcType="VARCHAR" property="phone"
                typeHandler="com.jkga.wyyjs.component.SM4TypeHandler"/>
    </resultMap>

    <select id="getCarManagerListByAreaId" resultMap="CarManagerInfo">
        select
        "id",
        "area_location_id",
        "house_id","house_name",
        "car_number","user_id","name",
        "phone","park_number",
        "start_time","end_time",
        "remark",
        "is_delete"
        from "car_manage"
        where "area_location_id" = #{area_location_id} AND "is_delete" = 0
        <if test="param != null and param != ''">
            AND ("car_number" like concat(#{param},'%') or "house_name" like concat(#{param},'%'))
        </if>
        <if test="pageSize != 0">
            LIMIT #{currIndex},#{pageSize}
        </if>
    </select>

    <select id="getCarManagerListByAreaIdCount" resultType="java.lang.Integer">
        select count(*) from "car_manage" where "area_location_id" = #{area_location_id} AND "is_delete" = 0
        <if test="param != null and param != ''">
            AND ("car_number" like concat(#{param},'%') or "house_name" like concat(#{param},'%'))
        </if>
    </select>

    <update id="updateCarManagerById">
        update "car_manage"
        <set>
            <if test="houseId != null and houseId != ''">
                "house_id" = #{houseId},
            </if>
            <if test="houseName != null and houseName != ''">
                "house_name" = #{houseName},
            </if>
            <if test="carNumber != null and carNumber != ''">
                "car_number" = #{carNumber},
            </if>
            <if test="userId != null and userId != ''">
                "user_id" = #{userId},
            </if>
            <if test="name != null and name != ''">
                "name" = #{name,typeHandler=com.jkga.wyyjs.component.SM4TypeHandler},
            </if>
            <if test="phone != null and phone != ''">
                "phone" = #{phone,typeHandler=com.jkga.wyyjs.component.SM4TypeHandler},
            </if>
            <if test="parkNumber != null and parkNumber != ''">
                "park_number" = #{parkNumber},
            </if>
            <if test="startTime != null and startTime != ''">
                "start_time" = #{startTime},
            </if>
            <if test="endTime != null and endTime != ''">
                "end_time" = #{endTime},
            </if>
            <if test="remark != null and remark != ''">
                "remark" = #{remark},
            </if>
            <if test="isDelete != null">
                "is_delete" = #{isDelete},
            </if>
        </set>
        where "id" = #{id}
    </update>

    <select id="checkCarIsExist" resultType="java.lang.Integer">
        SELECT COUNT(*) from  "car_manage" where  "area_location_id" = #{area_location_id} and  "car_number" = #{car_no} and "is_delete" = 0 and "id" != #{id}
    </select>

    <select id="countHouseCarManage" resultType="int">
        select count(1) from "car_manage" where "house_id" = #{house_id} and "car_number" = #{car_number} and "is_delete" = 0
    </select>

    <select id="countAreaCarTableById" resultType="int">
        select count(1) from "car_table" where "id" = #{id}
    </select>

    <insert id="addCarTable" parameterType="com.jkga.wyyjs.model.vo.CarVO">
        INSERT INTO "car_table" ("id", "area_location_id", "car_no", "time", "is_binding", "is_remind") VALUES
            (#{id}, #{area_location_id}, #{car_no}, #{appear_days},'0','0')
    </insert>

    <select id="countAreaCarDetailTableById" resultType="int">
         select count(1) from "car_detial_table" where  "id" =  #{id}
    </select>

    <insert id="addCarDetailTable" parameterType="com.jkga.wyyjs.model.vo.CarDetailVO">
        INSERT INTO "car_detial_table" ( "id", "car_id", "area_location_id", "profile_id", "is_identify", "name", "identification_id", "identification_type", "face_image_url", "time") VALUES
            ( #{id}, #{car_id}, #{area_location_id}, #{dossier_id}, #{is_identify}, #{user_name}, #{id_card}, #{identification_type}, #{face_image_url}, #{appear_days})
    </insert>

    <select id="getHouseById" resultType="com.jkga.wyyjs.model.entity.HouseEntity">
        select * from "house" where "id" = #{id}
    </select>

    <update id="updCarTableRemind">
        update "car_table" SET "is_remind" = 1 where "id" = #{id}
    </update>

    <update id="updateCarTableTime">
        UPDATE "car_table" SET  "time" = #{time} WHERE "id" = #{id}
    </update>

    <update id="updateCarDetailTableTime">
        UPDATE "car_detial_table" SET  "time" = #{time} WHERE "id" = #{id}
    </update>

    <select id="getCarManagerListByAreaAndCar" resultType="com.jkga.wyyjs.model.entity.CarManageEntity">
        select * from "car_manage" where "area_location_id" = #{areaId} and "car_number" = #{car_number} and "is_delete" = 0
    </select>

    <select id="getAreaAlarmQcbByDIdAndCar" resultType="com.jkga.wyyjs.model.entity.AlarmQcbEntity">
        select * from "alarm_qcb" where "area_id" = #{areaId} and "dossier_id" = #{dossierId}
        <if test="carNo != null and carNo != ''">
            and "car_no" = #{carNo, typeHandler=com.jkga.wyyjs.component.SM4TypeHandler}
        </if>
        limit 1
    </select>

    <insert id="addAlarmQcb" parameterType="com.jkga.wyyjs.model.entity.AlarmQcbEntity">
        insert into "alarm_qcb"("id", "type", "area_id", "dossier_id", "id_card", "user_name", "phone", "car_no", "house_id", "update_user", "status", "face_image_url")
        VALUES (#{id}, #{type}, #{areaId}, #{dossierId}, #{idCard, typeHandler=com.jkga.wyyjs.component.SM4TypeHandler}, #{userName, typeHandler=com.jkga.wyyjs.component.SM4TypeHandler}, #{phone, typeHandler=com.jkga.wyyjs.component.SM4TypeHandler}, #{carNo, typeHandler=com.jkga.wyyjs.component.SM4TypeHandler}, #{houseId}, #{updateUser}, #{status}, #{faceImageUrl})
    </insert>

    <update id="updateAlarmQcb" parameterType="com.jkga.wyyjs.model.entity.AlarmQcbEntity">
        update "alarm_qcb"
        set "house_id" = #{houseId},
        "update_user" = #{updateUser},
        "status" = 1,
        "car_type" = #{carType}
        where "id" = #{id} and "status" = 0
    </update>

    <select id="getAreaAlarmQcbById" resultMap="returnAlarmQcbMap">
        select * from "alarm_qcb" where "id" = #{id}
    </select>

    <select id="getHouseMemberById" resultType="com.jkga.wyyjs.model.entity.HouseMemberEntity">
        select * from "house_member" where "id" = #{id}
    </select>

    <select id="getAlarmQcbListCountByAreaId" resultType="int">
        select count(*) from "alarm_qcb" where "area_id" = #{area_location_id}
        <if test="status != null">
            AND "status" = #{status}
        </if>
        <if test="param != null and param != ''">
            AND "car_no" = #{param, typeHandler=com.jkga.wyyjs.component.SM4TypeHandler}
        </if>
    </select>

    <select id="getAlarmQcbListByAreaId" resultMap="returnAlarmQcbMap">
        select * from "alarm_qcb" where "area_id" = #{area_location_id}
        <if test="status != null">
            AND "status" = #{status}
        </if>
        <if test="param != null and param != ''">
            AND "car_no" = #{param, typeHandler=com.jkga.wyyjs.component.SM4TypeHandler}
        </if>
        <if test="pageSize != 0">
            LIMIT #{currIndex},#{pageSize}
        </if>
    </select>


</mapper>