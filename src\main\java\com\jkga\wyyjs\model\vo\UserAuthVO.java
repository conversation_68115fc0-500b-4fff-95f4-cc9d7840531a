package com.jkga.wyyjs.model.vo;

import com.jkga.wyyjs.anno.NoQuotes;
import com.jkga.wyyjs.anno.ValidIdentity;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 用户认证前端传参实体类
 *
 * <AUTHOR>
 * @date 2025/1/17 15:49
 */
@Data
public class UserAuthVO {

    private String userId;
    @NotBlank(message = "姓名不能为空")
    @NoQuotes
    private String userName;
    @NotBlank(message = "证件号不能为空")
    @ValidIdentity
    private String identity;
    @NotBlank(message = "证件号类型不能为空")
    private String identityType;
    @NotNull(message = "文件不能为空")
    private MultipartFile faceImgFile;

}
