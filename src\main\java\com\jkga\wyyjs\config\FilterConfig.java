package com.jkga.wyyjs.config;

import com.jkga.wyyjs.filter.TokenFilter;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 过滤器配置
 *
 * <AUTHOR>
 */
@Configuration
public class FilterConfig {

//    @Bean
//    public RateLimiterFilter rateLimiterFilter() {
//        return new RateLimiterFilter();
//    }

    @Bean
    public TokenFilter tokenFilter() {
        return new TokenFilter();
    }

//    /**
//     * 限流过滤器
//     *
//     * @return
//     */
//    @Bean
//    public FilterRegistrationBean<RateLimiterFilter> firstFilter() {
//        FilterRegistrationBean<RateLimiterFilter> registration = new FilterRegistrationBean<>();
//        registration.setFilter(rateLimiterFilter());
//        registration.addUrlPatterns("/*");
//        registration.setOrder(1);
//        return registration;
//    }

    /**
     * 普通过滤器
     *
     * @return
     */
    @Bean
    public FilterRegistrationBean<TokenFilter> secondFilter() {
        FilterRegistrationBean<TokenFilter> registration = new FilterRegistrationBean<>();
        registration.setFilter(tokenFilter());
        registration.addUrlPatterns(
                "/user/*",
                "/house/*",
                "/propertyUserManagement/*",
                "/propertyInfoManagement/*",
                "/visit/*");
        registration.setOrder(2);
        return registration;
    }

}
