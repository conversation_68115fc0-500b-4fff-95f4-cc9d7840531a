package com.jkga.wyyjs.mapper;

import com.jkga.wyyjs.model.entity.AreaDepartmentEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author：clyde
 * @Date：2025/2/6 15:55
 */
@Mapper
public interface AreaDepartmentMapper {

    AreaDepartmentEntity getAreaDepartmentByName(@Param("areaId") String areaId, @Param("departName") String departName);

    boolean insertAreaDepartment(AreaDepartmentEntity areaDepartmentEntity);

    List<AreaDepartmentEntity> getAreaDepartmentList(@Param("areaId") String areaId);

}
