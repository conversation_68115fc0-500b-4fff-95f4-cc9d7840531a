package com.jkga.wyyjs.anno;


import com.jkga.wyyjs.utils.IdentityUtil;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

/**
 * 身份证校验
 *
 * <AUTHOR>
 */
public class IdentityValidator implements ConstraintValidator<ValidIdentity, String> {

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        return IdentityUtil.isIdentityCode(value);
    }

}
