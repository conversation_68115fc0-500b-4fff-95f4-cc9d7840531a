<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jkga.wyyjs.mapper.AreaInfoConfigMapper">

    <resultMap id="AreaInfoConfigResultMap" type="com.jkga.wyyjs.model.entity.AreaInfoConfigEntity">
        <id     property="id"     column="id"     />
        <result property="areaId"   column="area_id"   />
        <result property="type"   column="type"   />
        <result property="content"   column="content"   />
        <result property="operatorId"   column="operator_id"   />
        <result property="operatorTime"   column="operator_time"   />

    </resultMap>


    <select id="getAreaInfoConfigByType" resultMap="AreaInfoConfigResultMap">
        select * from "area_info_config" where "area_id" = #{areaId} and "type" = #{type} limit 1
    </select>

    <insert id="addAreaInfoConfig" parameterType="com.jkga.wyyjs.model.entity.AreaInfoConfigEntity">
        insert into "area_info_config" ("id","area_id","type","content","operator_id","operator_time")
        values(#{id},#{areaId},#{type},#{content},#{operatorId},#{operatorTime})
    </insert>

    <update id="updAreaInfoConfig" parameterType="com.jkga.wyyjs.model.entity.AreaInfoConfigEntity">
        update "area_info_config"
        set "content" = #{content},
        "operator_id" = #{operatorId},
        "operator_time" = #{operatorTime}
        where "id" = #{id}
    </update>

    <select id="getAreaInfoConfig" resultMap="AreaInfoConfigResultMap">
        select * from "area_info_config" where "area_id" = #{areaId}
    </select>





</mapper>