package com.jkga.wyyjs.model.vo;

import com.jkga.wyyjs.anno.NoQuotes;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @Author：clyde
 * @Date：2025/2/6 10:50
 */
@Data
public class RoleVO{

    private String id;
    @NotBlank(message = "角色名称不能为空")
    @NoQuotes
    private String roleName;
    private String roleDesc;
    private String createTime;
    private String updateTime;
    private Integer deleteStatus;
    private String areaId;
    //角色类型
    private Integer roleType;

}
