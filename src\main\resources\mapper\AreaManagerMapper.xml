<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jkga.wyyjs.mapper.AreaManagerMapper">

    <resultMap id="AreaManagerResultMap" type="com.jkga.wyyjs.model.entity.AreaManagerEntity">
        <id     property="id"     column="id"     />
        <result property="areaId"   column="area_id"   />
        <result property="userId"   column="user_id"   />
        <result property="userName"   column="user_name"   typeHandler="com.jkga.wyyjs.component.SM4TypeHandler"/>
        <result property="phone"   column="phone"   typeHandler="com.jkga.wyyjs.component.SM4TypeHandler"/>
        <result property="position"   column="position"   />
        <result property="intime"   column="intime"   />
        <result property="deleteStatus"   column="delete_status"   />
        <result property="departmentId"   column="department_id"   />
    </resultMap>

    <resultMap id="ManagerAreaResultMap" type="com.jkga.wyyjs.model.vo.ManagerAreaVO">
        <result property="areaId"   column="area_id"   />
        <result property="areaName" column="name" />
    </resultMap>

    <resultMap id="AreaManagerVOResultMap" type="com.jkga.wyyjs.model.vo.AreaManagerVO">
        <id     property="id"     column="id"     />
        <result property="areaId"   column="area_id"   />
        <result property="userId"   column="user_id"   />
        <result property="userName"   column="user_name"   typeHandler="com.jkga.wyyjs.component.SM4TypeHandler"/>
        <result property="phone"   column="phone"   typeHandler="com.jkga.wyyjs.component.SM4TypeHandler"/>
        <result property="position"   column="position"   />
        <result property="intime"   column="intime"   />
        <result property="deleteStatus"   column="delete_status"   />
        <result property="departmentId"   column="department_id"   />

        <result property="faceImgUrl"   column="face_img_url"   />
    </resultMap>

    <select id="getAreaManagerByUserId" resultMap="AreaManagerResultMap">
        SELECT * from "area_manager" where "area_id" = #{areaId} and "user_id" = #{userId} and "delete_status" = 0 limit 1
    </select>

    <insert id="insertAreaManager" parameterType="com.jkga.wyyjs.model.entity.AreaManagerEntity">
        insert into "area_manager"(
            "id",
            "area_id",
            "user_id",
            "user_name",
            "phone",
            "position",
            "intime",
            "delete_status",
            "department_id"
            )values(
            #{id},
            #{areaId},
            #{userId},
            #{userName,typeHandler=com.jkga.wyyjs.component.SM4TypeHandler},
            #{phone,typeHandler=com.jkga.wyyjs.component.SM4TypeHandler},
            #{position},
            #{intime},
            #{deleteStatus},
            #{departmentId}
        )
    </insert>

    <select id="getAreaManagerByDepartmentId" resultMap="AreaManagerVOResultMap">
        SELECT m.*,u."face_img_url" from "area_manager" m join "user" u on m."user_id" = u."id" where m."department_id" = #{departmentId} and m."delete_status" = 0
    </select>

    <update id="updAreaManager" parameterType="com.jkga.wyyjs.model.entity.AreaManagerEntity">
        update "area_manager"
        set
        "position" = #{position},
        "department_id" = #{departmentId}
        where "id" = #{id}
    </update>
    
    <select id="getAreaManagerById" resultMap="AreaManagerResultMap">
        SELECT * from "area_manager" where "id" = #{id}
    </select>

    <update id="delAreaManager">
       update "area_manager"
        set
        "delete_status" = 1
        where "id" = #{id}
    </update>

    <select id="getManagerAreas" resultMap="ManagerAreaResultMap">
        SELECT m."area_id",a."name" from "admin_manager" m inner join "area" a on m."area_id" = a."id" where m."user_id" = #{userId} and m."delete_status" = 0 and a."delete_status" = 0
    </select>

</mapper>