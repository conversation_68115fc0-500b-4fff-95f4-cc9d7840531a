package com.jkga.wyyjs.mapper;

import com.jkga.wyyjs.model.entity.*;
import com.jkga.wyyjs.model.vo.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * @Author：clyde
 * @Date：2025/7/16 10:42
 */

@Mapper
public interface SubletMapper {

    List<SubletHouseVO> getSubletHouseByBuildingId(@Param("buildingId") String buildingId, @Param("dataType") int dataType, @Param("userId") String userId);

    boolean insertSubletRenter(SubletRenterRegisterVO registerVO);

    List<ListNeedHandleSubletRenterVO> listNeedHandleSubletRenter(@Param("currIndex") int currIndex,
                                                                  @Param("pageSize") int pageSize,
                                                                  @Param("searchParam") String searchParam,
                                                                  @Param("adminId") String adminId);

    int countNeedHandleSubletRenter(@Param("searchParam") String searchParam,
                                    @Param("adminId") String adminId);

    List<Map<String, Object>> listMySubletCommunity(@Param("adminId") String adminId);

    List<Map<String, Object>> listMySubletRoomByCommunity(@Param("areaId") String areaId,
                                                          @Param("adminId") String adminId,
                                                          @Param("searchParam") String searchParam,
                                                          @Param("dataType") int dataType);

    SubletRenterVO getSubletRenterById(@Param("id") String id);

    int countSubletRenterInHouseNum(@Param("identityList") List<String> identityList,
                                    @Param("subletHouseId") String subletHouseId,
                                    @Param("adminId") String adminId);

    SubletHouseEntity getSubletHouseById(@Param("subletHouseId") String subletHouseId);

    int countSubletRenterHouseAlreadyLiveNum(@Param("subletHouseId") String subletHouseId,
                                             @Param("adminId") String adminId);

    void insertSubletContract(SubletContractEntity subletContractEntity);

    void handleMainRenterRoom(HandleSubletRoomToRenterVO handleVO);

    void batchInsertSubletRenterFull(@Param("dataList") List<SubletRenterRegisterVO> renterDO);

    void batchInsertSubletContractRelation(@Param("dataList") List<SubletContractRelationEntity> subletContractRelationEntityList);

    void cancelSubletRenterRegister(@Param("renterId") String renterId,
                                    @Param("adminId") String adminId);

    List<ListMySubletRenterVO> listMySubletRenter(@Param("currIndex") int currIndex,
                                                  @Param("pageSize") int pageSize,
                                                  @Param("searchParam") String searchParam,
                                                  @Param("adminId") String adminId,
                                                  @Param("dataType") int dataType);

    int countMySubletRenter(@Param("searchParam") String searchParam,
                            @Param("adminId") String adminId,
                            @Param("dataType") int dataType);

    void leaveSubletRenter(@Param("renterId") String renterId,
                           @Param("adminId") String adminId);

    void continueSubletRenter(@Param("renterId") String renterId,
                              @Param("contractId") String contractId,
                              @Param("adminId") String adminId);

    List<Map> getHouseSubletHouseCountByArea(@Param("user_id") String user_id);

    List<Map> getHouseSubletRenterCountByArea(@Param("user_id") String user_id);

    List<Map> getUnUsedHouseSubletHouseCountByArea(@Param("user_id") String user_id);

    List<ListMySubletRenterVO> getHouseSubletRenterList(@Param("currIndex") int currIndex,
                                                        @Param("pageSize") int pageSize,
                                                        @Param("searchParam") String searchParam,
                                                        @Param("user_id") String user_id,
                                                        @Param("sublet_house_id") String sublet_house_id);

    int countHouseSubletRenter(@Param("searchParam") String searchParam,
                               @Param("user_id") String user_id,
                               @Param("sublet_house_id") String sublet_house_id);

    List<Map> getHouseSubletContractImgList(@Param("contract_id") String contract_id);

    int getHouseSubletRenterNum(@Param("sublet_house_id") String sublet_house_id);

    boolean delSubletUnusedRoom(@Param("sublet_house_id") String sublet_house_id);

    int getSubletRenterNum(@Param("user_id") String user_id);

    int getSubletOwnerNum(@Param("user_id") String user_id);

    int getVacantHouseNum(@Param("user_id") String user_id);

    int countMyTotalSubletHouse(@Param("user_id") String user_id);

    void updateSubletHouseOwner(HandleSubletHouseOwnerVO handleVO);

    List<ListBindSubletHouseOwnerVO> listBindSubletHouseOwner(@Param("currIndex") int currIndex,
                                                 @Param("pageSize") int pageSize,
                                                 @Param("searchParam") String searchParam,
                                                 @Param("adminId") String adminId,
                                                 @Param("dataType") int dataType);

    int countBindSubletHouseOwner(@Param("searchParam") String searchParam,
                                  @Param("adminId") String adminId,
                                  @Param("dataType") int dataType);

    void leaveSubletHouseOwner(@Param("subletHouseId") String subletHouseId,
                               @Param("adminId") String adminId);

    void continueSubletHouseOwner(HandleSubletHouseOwnerVO handleVO);

    List<Map<String, Object>> getNoBindOwnerSubletCommunityList(@Param("adminId") String adminId);

    List<SubletHouseEntity> listNoBindOwnerSubletHouseListByCommunity(@Param("areaId") String areaId,
                                                                      @Param("searchParam") String searchParam,
                                                                      @Param("adminId") String adminId);

    int countNoOwnerSubletHouse(@Param("user_id") String user_id);

    int countManagerSubletHouse(@Param("adminId") String adminId);

    List<SubletHouseEntity> getManagerSubletHouseList(@Param("currIndex") int currIndex,
                                                      @Param("pageSize") int pageSize,
                                                      @Param("adminId") String adminId);

    SubletHouseEntity getSubletHouseByHouseId(@Param("houseId") String houseId, @Param("userId") String userId);

    void insertSubletRoom(SubletHouseEntity subletHouseEntity);

    void updSubletRoom(SubletHouseEntity subletHouseEntity);

    void batchUpdateSubletRenterLeaveStatus();

    List<SubletHouseEntity> getExpireSubletHouseOwnerList();

    void unbindExpireSubletHouseOwner(@Param("dataList") List<SubletHouseEntity> houseList);

    void batchInsertExpireSubletHouseOwnerToHistory(@Param("dataList") List<SubletHouseEntity> houseList);


}
