package com.jkga.wyyjs.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.GregorianCalendar;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
public class IdentityUtil {

    public static final int IDENTITYCODE_OLD = 15; // 老身份证15位
    public static final int IDENTITYCODE_NEW = 18; // 新身份证18位
    public static int[] Wi = new int[17];

    public static String getSexByIdentity(String identity) {
        if (identity.length() == 15 || identity.length() == 18) {
            String code_str = identity.length() == 15 ? identity.substring(14, 15) : identity.substring(16, 17);
            return Integer.parseInt(code_str) % 2 == 0 ? "女" : "男";
        } else {
            return "未知";
        }
    }

    public static String getBirthByIdentity(String identity){
        String birthDay = "";
        if (identity.length() == IDENTITYCODE_OLD) {
            birthDay = "19" + identity.substring(6, 12);
        } else {
            birthDay = identity.substring(6, 14);
        }
       return birthDay;
    }

    /**
     * 判断身份证号码是否正确。
     *
     * @param code
     *            身份证号码。
     * @return 如果身份证号码正确，则返回true，否则返回false。
     */

    public static boolean isIdentityCode(String code) {

        if (StringUtils.isBlank(code)) {
            return false;
        }

        String birthDay = "";
        code = StringUtils.trim(code);

        // 长度只有15和18两种情况
        if ((code.length() != IDENTITYCODE_OLD)
                && (code.length() != IDENTITYCODE_NEW)) {
            return false;
        }

        // 身份证号码必须为数字(18位的新身份证最后一位可以是x)
        Pattern pt = Pattern.compile("\\d{15,17}([\\dxX]{1})?");
        Matcher mt = pt.matcher(code);
        if (!mt.find()) {
            return false;
        }

        // ================ 出生年月是否有效 ================

        // 验证生日
        if (code.length() == IDENTITYCODE_OLD) {
            birthDay = "19" + code.substring(6, 12);
        } else {
            birthDay = code.substring(6, 14);
        }
        String strYear = birthDay.substring(0,4);// 年份
        String strMonth = birthDay.substring(4,6);// 月份
        String strDay = birthDay.substring(6,8);// 天
        if (isDate(strYear + "-" + strMonth + "-" + strDay) == false) {
            return false;
        }
        GregorianCalendar gc = new GregorianCalendar();
        SimpleDateFormat s = new SimpleDateFormat("yyyy-MM-dd");
        try {
            if ((gc.get(Calendar.YEAR) - Integer.parseInt(strYear)) > 150
                    || (gc.getTime().getTime() - s.parse(
                    strYear + "-" + strMonth + "-" + strDay).getTime()) < 0) {
                log.info("身份证年份不在有效范围。");
                return false;
            }
        } catch (NumberFormatException e) {
            return false;
        } catch (java.text.ParseException e) {
            return false;
        }
        if (Integer.parseInt(strMonth) > 12 || Integer.parseInt(strMonth) == 0) {
            log.info("身份证月份无效");
            return false;
        }
        if (Integer.parseInt(strDay) > 31 || Integer.parseInt(strDay) == 0) {
            log.info("身份证日期无效");
            return false;
        }

        // 最后一位校验码验证
        if (code.length() == IDENTITYCODE_NEW) {
            String lastNum = getCheckFlag(code.substring(0,
                    IDENTITYCODE_NEW - 1));
            // check last digit
            if (!("" + code.charAt(IDENTITYCODE_NEW - 1)).toUpperCase().equals(
                    lastNum)) {
                return false;
            }
        }

        return true;
    }

    /**
     * 获取新身份证的最后一位:检验位
     *
     * @param code
     *            18位身份证的前17位
     * @return 新身份证的最后一位
     */
    private static String getCheckFlag(String code) {

        int[] varArray = new int[code.length()];
        String lastNum = "";
        int numSum = 0;
        // 初始化位权值
        setWiBuffer();
        for (int i = 0; i < code.length(); i++) {
            varArray[i] = new Integer("" + code.charAt(i)).intValue();
            varArray[i] = varArray[i] * Wi[i];
            numSum = numSum + varArray[i];
        }
        int checkDigit = 12 - numSum % 11;
        switch (checkDigit) {
            case 10:
                lastNum = "X";
                break;
            case 11:
                lastNum = "0";
                break;
            case 12:
                lastNum = "1";
                break;
            default:
                lastNum = String.valueOf(checkDigit);
        }
        return lastNum;
    }

    /**
     * 初始化位权值
     */
    private static void setWiBuffer() {
        for (int i = 0; i < Wi.length; i++) {
            int k = (int) Math.pow(2, (Wi.length - i));
            Wi[i] = k % 11;
        }
    }

    /**
     * 判别是否字符串为null或者没有内容，或者全部为空格。
     */
    public static boolean empty(String o) {
        return ((null == o) || (o.length() <= 0) || (o.trim().equals("")));
    }

    /**
     * 将15位身份证号码升级为18位身份证号码
     *
     * @param code
     *            15位身份证号码
     * @return 18位身份证号码
     */
    public static String update2eighteen(String code) {

        if (code == null || "".equals(code.trim())) {
            return "";
        }

        code = code.trim();

        if (code.length() != IDENTITYCODE_OLD || !isIdentityCode(code)) {
            return "";
        }

        code = code.substring(0, 6) + "19" + code.substring(6);
        //
        code = code + getCheckFlag(code);

        return code;
    }

    /**
     * 还原15位身份证号码
     * @param code
     * @return
     */
    public  static String resume2fifteen(String code){

        if (code == null || "".equals(code.trim())) {
            return "";
        }

        code = code.trim();

//        if (code.length() != IDENTITYCODE_NEW || !isIdentityCode(code)) {
//            return "";
//        }

        if (code.length() != IDENTITYCODE_NEW ) {
            return "";
        }
        StringBuffer codebuffer = new StringBuffer(code);
        codebuffer.delete(6, 8);
        codebuffer.deleteCharAt(codebuffer.length() -1 );

        return codebuffer.toString();
    }

    /**
     * 功能：判断字符串是否为日期格式
     *
     * @param strDate
     * @return
     */
    public static boolean isDate(String strDate) {
        Pattern pattern = Pattern
                .compile("^((\\d{2}(([02468][048])|([13579][26]))[\\-\\/\\s]?((((0?[13578])|(1[02]))[\\-\\/\\s]?((0?[1-9])|([1-2][0-9])|(3[01])))|(((0?[469])|(11))[\\-\\/\\s]?((0?[1-9])|([1-2][0-9])|(30)))|(0?2[\\-\\/\\s]?((0?[1-9])|([1-2][0-9])))))|(\\d{2}(([02468][1235679])|([13579][01345789]))[\\-\\/\\s]?((((0?[13578])|(1[02]))[\\-\\/\\s]?((0?[1-9])|([1-2][0-9])|(3[01])))|(((0?[469])|(11))[\\-\\/\\s]?((0?[1-9])|([1-2][0-9])|(30)))|(0?2[\\-\\/\\s]?((0?[1-9])|(1[0-9])|(2[0-8]))))))(\\s(((0?[0-9])|([1-2][0-3]))\\:([0-5]?[0-9])((\\s)|(\\:([0-5]?[0-9])))))?$");
        Matcher m = pattern.matcher(strDate);
        if (m.matches()) {
            return true;
        } else {
            return false;
        }
    }


}
