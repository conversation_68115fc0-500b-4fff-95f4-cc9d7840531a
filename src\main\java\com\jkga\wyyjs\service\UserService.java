package com.jkga.wyyjs.service;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.jkga.wyyjs.constant.TbTypeEnum;
import com.jkga.wyyjs.exception.MyException;
import com.jkga.wyyjs.mapper.UserMapper;
import com.jkga.wyyjs.model.CommonCode;
import com.jkga.wyyjs.model.CommonResult;
import com.jkga.wyyjs.model.configuration.WeixinConfiguration;
import com.jkga.wyyjs.model.dto.FileSaveReturnDTO;
import com.jkga.wyyjs.model.dto.LoginDTO;
import com.jkga.wyyjs.model.dto.UserTransformDTO;
import com.jkga.wyyjs.model.entity.UserEntity;
import com.jkga.wyyjs.model.vo.DeclareForUserVO;
import com.jkga.wyyjs.model.vo.UserAuthVO;
import com.jkga.wyyjs.model.weixin.LoginResult;
import com.jkga.wyyjs.model.weixin.UserInfo;
import com.jkga.wyyjs.utils.*;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.faceid.v20180301.models.DetectAuthResponse;
import com.tencentcloudapi.faceid.v20180301.models.GetDetectInfoResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.security.NoSuchAlgorithmException;
import java.util.*;

/**
 * 用户服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class UserService {

    @Autowired
    private WeixinConfiguration weixinConf;
    @Autowired
    private UserMapper userMapper;
    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private WeixinUtil weixinUtil;
    @Autowired
    private FileService fileService;
    @Autowired
    private TencentUtil tencentUtil;
    @Autowired
    private ThirdService thirdService;


    public LoginDTO login(String jsCode, String encryptedData, String iv) throws Exception {
        //获取sessionKey
        Map<String, String> getParam = new HashMap<>();
        getParam.put("appid", weixinConf.getAppId());
        getParam.put("secret", weixinConf.getAppSecret());
        getParam.put("js_code", jsCode);
        getParam.put("grant_type", "authorization_code");
        String resultStr = restTemplate.getForObject(weixinConf.getOpenidUrl() + "?appid={appid}&secret={secret}" +
                "&js_code={js_code}&grant_type={grant_type}", String.class, getParam);
        log.info(resultStr);
        LoginResult loginResult = JsonUtil.toObject(LoginResult.class, resultStr);
        if (loginResult != null) {
            if (loginResult.getErrcode() == null || loginResult.getErrcode() == 0) {
                //从微信API获取到的用户openid（同一个微信号在同一个微信应用【通过appid标识】中唯一）
                String openid = loginResult.getOpenid();
                String userInfoStr = weixinUtil.getUserInfoStr(encryptedData, loginResult.getSession_key(), iv);
                //=====不授权手机号返回=====
                //{"openId":"oTAQZ7cpp022BmsvTiojKMEO8qGM","nickName":"微信用户","gender":0,"language":"","city":"","province":"","country":"","avatarUrl":"https://thirdwx.qlogo.cn/mmopen/vi_32/POgEwh4mIHO4nibH0KlMECNjjGxQUq24ZEaGT4poC6icRiccVGKSyXwibcPq4BWmiaIGuG1icwxaQX6grC9VemZoJ8rg/132","watermark":{"timestamp":1736991523,"appid":"wx7e228a507aa83ea4"}}
                //=====授权手机号后返回=====
                //{"phoneNumber":"18806539529","purePhoneNumber":"18806539529","countryCode":"86","watermark":{"timestamp":1732522437,"appid":"wx2993985e9ddab7b6"}}
                log.info(userInfoStr);
                UserInfo wxUserInfo = JsonUtil.toObject(UserInfo.class, userInfoStr);
                if (wxUserInfo == null) {
                    throw new MyException(new CommonResult<>(CommonCode.SYSTEM_ERROR, "获取用户信息失败", null));
                }
                //微信API获取到的手机号
                String weixinPhone = wxUserInfo.getPhoneNumber();
                UserEntity existsUserEntity = userMapper.getUserByPhone(weixinPhone);
                if (existsUserEntity != null) {
                    String existsUserId = existsUserEntity.getId();
                    String existsOpenId = existsUserEntity.getOpenId();
                    //如果被代申报过或者openid不一致，则更新openid
                    if (StringUtils.isBlank(existsOpenId) || !existsOpenId.equals(openid)) {
                        userMapper.updateLoginUserOpenid(existsUserId, openid);
                        existsUserEntity.setOpenId(openid);
                    }
                    LoginDTO loginDTO = new LoginDTO();
                    BeanUtils.copyProperties(existsUserEntity, loginDTO);
                    String token = TokenUtil.generate(existsUserEntity.getId());
                    loginDTO.setToken(token);
                    return loginDTO;
                } else {
                    UserEntity loginUser = new UserEntity();
                    //雪花id
                    String userId = SnowflakeUtil.generateId();
                    loginUser.setId(userId);
                    loginUser.setPhone(weixinPhone);
                    loginUser.setOpenId(openid);
                    loginUser.setCreatorId(userId);
                    userMapper.insertLoginUser(loginUser);
                    LoginDTO loginDTO = new LoginDTO();
                    BeanUtils.copyProperties(loginUser, loginDTO);
                    String token = TokenUtil.generate(userId);
                    loginDTO.setToken(token);
                    return loginDTO;
                }
            }
        }
        throw new MyException(new CommonResult(CommonCode.SYSTEM_ERROR));
    }

    public UserEntity getUserById(String id) {
        return userMapper.selectUserById(id);
    }


    @Transactional(rollbackFor = Exception.class)
    public void auth(UserAuthVO authVO) throws IOException, NoSuchAlgorithmException {
        int existsIdentityUserNum = userMapper.countUserIdentityIsAuth(authVO.getUserId(), authVO.getIdentity());
        if (existsIdentityUserNum > 0) {
            throw new MyException(new CommonResult<>(CommonCode.SYSTEM_ERROR, "该身份证已被认证", null));
        }
        //先更新user认证信息
        userMapper.updateUserAuthInfo(authVO);
        //上传user人脸文件
        List<MultipartFile> fileList = Arrays.asList(authVO.getFaceImgFile());
        String tbType = TbTypeEnum.USER.getValue();
        //这里modulePath（模块文件夹名）直接使用了tbType，也可以自定义该模块的文件夹名
        List<FileSaveReturnDTO> fileSaveReturnList = fileService.saveFiles(authVO.getUserId(), fileList, authVO.getUserId(), tbType, tbType);
        if (fileSaveReturnList.size() > 0) {
            FileSaveReturnDTO fileReturn = fileSaveReturnList.get(0);
            userMapper.updateUserFaceImg(authVO.getUserId(), fileReturn.getUrl());

            //用户推到自主申报小程序
            UserEntity userEntity = userMapper.selectUserById(authVO.getUserId());
            UserTransformDTO userTransformDTO = new UserTransformDTO();
            userTransformDTO.setName(authVO.getUserName());
            userTransformDTO.setPhone(userEntity.getPhone());
            userTransformDTO.setIdentity(authVO.getIdentity());
            userTransformDTO.setIdentityType(authVO.getIdentityType());
            userTransformDTO.setFaceImgUrl(fileReturn.getUrl());
            thirdService.pushUserToStoremgmt(userTransformDTO);

        }
    }


    public CommonResult getUserAuthInfo(String name, String identity) {
        UserEntity existsUserEntity = userMapper.getUserByIdentity(identity);
        Map<String, Object> resultMap = new HashMap<>();
        if (existsUserEntity != null) {
            String existsUserName = existsUserEntity.getName();
            if (StringUtils.isNotBlank(existsUserName) && existsUserName.equals(name)) {
                resultMap.put("state", 1);
                resultMap.put("user", existsUserEntity);
                return new CommonResult<>(resultMap);
            } else {
                resultMap.put("state", 2);
                resultMap.put("user", null);
                resultMap.put("userName", existsUserEntity.getName());
                return new CommonResult<>(CommonCode.OK, "身份证存在，姓名不匹配", resultMap);
            }
        } else {
            resultMap.put("state", 0);
            resultMap.put("user", null);
            return new CommonResult<>(CommonCode.OK, "用户不存在", resultMap);
        }
    }

    public void declareForUser(DeclareForUserVO declareVO) throws IOException, NoSuchAlgorithmException {
        UserEntity existsUserEntity = userMapper.getUserByIdentityOrPhone(declareVO.getIdentity(), declareVO.getPhone());
        if (existsUserEntity != null) {
            throw new MyException(new CommonResult<>(CommonCode.SYSTEM_ERROR, "身份证或手机号已被注册", null));
        }
        //=====存user表=====
        UserEntity userEntity = new UserEntity();
        BeanUtils.copyProperties(declareVO, userEntity);
        //雪花id
        String userId = SnowflakeUtil.generateId();
        userEntity.setId(userId);
        userMapper.insertDeclareUser(userEntity);
        //=====存人员照片=====
        List<MultipartFile> fileList = Arrays.asList(declareVO.getFaceImgFile());
        String tbType = TbTypeEnum.USER.getValue();
        //这里modulePath（模块文件夹名）直接使用了tbType，也可以自定义该模块的文件夹名
        List<FileSaveReturnDTO> fileSaveReturnList = fileService.saveFiles(declareVO.getCreatorId(), fileList, userId, tbType, tbType);
        String userFaceImgUrl = null;
        if (fileSaveReturnList.size() > 0) {
            userFaceImgUrl = fileSaveReturnList.get(0).getUrl();
        }
        if (StringUtils.isNotBlank(userFaceImgUrl)) {
            userMapper.updateUserFaceImg(userId, userFaceImgUrl);
        }

        //用户推到自主申报小程序
        UserEntity pushUserEntity = userMapper.selectUserById(userId);
        UserTransformDTO userTransformDTO = new UserTransformDTO();
        userTransformDTO.setName(pushUserEntity.getName());
        userTransformDTO.setPhone(pushUserEntity.getPhone());
        userTransformDTO.setIdentity(pushUserEntity.getIdentity());
        userTransformDTO.setIdentityType(pushUserEntity.getIdentityType());
        userTransformDTO.setFaceImgUrl(pushUserEntity.getFaceImgUrl());
        thirdService.pushUserToStoremgmt(userTransformDTO);

    }

    public CommonResult detectAuth(String idCard, String name) throws TencentCloudSDKException {
        DetectAuthResponse detectAuthResponse = tencentUtil.detectAuth(idCard, name);
        return new CommonResult(detectAuthResponse);
    }

    public CommonResult getDetectInfo(String bizToken, String userId) throws NoSuchAlgorithmException {

        UserEntity userEntity = userMapper.selectUserById(userId);
        if (StringUtils.isNotBlank(userEntity.getIdentity())) {
            return new CommonResult(-1, "用户已经实名");
        }

        //调用腾讯人脸核身接口获取返回数据
        GetDetectInfoResponse res = tencentUtil.getDetectInfo(bizToken);
        JSONObject resJo = JSONUtil.parseObj(res.getDetectInfo());

        if (!resJo.containsKey("Text")) {
            return new CommonResult(-1, "腾讯认证返回数据没有基础用户信息");
        }

        JSONObject text = resJo.getJSONObject("Text");
        if (text.getInt("ErrCode") != 0) {
            return new CommonResult(-1, "腾讯认证返回数据错误");
        }

        String identity = text.getStr("IdCard");
        UserEntity ex = userMapper.getUserByIdentity(identity);
        if (ex != null) {
            return new CommonResult(-1, "该身份证已经被实名");
        }

        userEntity = new UserEntity();

        JSONObject bestFrame = resJo.getJSONObject("BestFrame");
        String base64 = bestFrame.getStr("BestFrame");


        //=====存人员照片=====
        List<String> fileList = Arrays.asList(base64);
        String tbType = TbTypeEnum.USER.getValue();
        //这里modulePath（模块文件夹名）直接使用了tbType，也可以自定义该模块的文件夹名
        List<FileSaveReturnDTO> fileSaveReturnList = fileService.saveBase64Files(userId, fileList, userId, tbType, tbType);
        String userFaceImgUrl = null;
        if (fileSaveReturnList.size() > 0) {
            userFaceImgUrl = fileSaveReturnList.get(0).getUrl();
            userEntity.setFaceImgUrl(userFaceImgUrl);
        }

        userEntity.setName(text.getStr("Name"));
        userEntity.setIdentity(identity);
        userEntity.setIdentityType("身份证");

        userMapper.smrzUser(userEntity);

        //用户推到自主申报小程序
        UserEntity pushUserEntity = userMapper.selectUserById(userId);
        UserTransformDTO userTransformDTO = new UserTransformDTO();
        userTransformDTO.setName(pushUserEntity.getName());
        userTransformDTO.setPhone(pushUserEntity.getPhone());
        userTransformDTO.setIdentity(pushUserEntity.getIdentity());
        userTransformDTO.setIdentityType(pushUserEntity.getIdentityType());
        userTransformDTO.setFaceImgUrl(pushUserEntity.getFaceImgUrl());
        thirdService.pushUserToStoremgmt(userTransformDTO);

        return new CommonResult(userEntity);
    }

    public CommonResult getuseridkey(String IdCard, String Name) {
        UserEntity userEntity = userMapper.getUserByIdentity(IdCard);
        if (userEntity == null) {
            JSONObject resultJo = thirdService.getuseridkey(IdCard, Name);
            if (resultJo.getInt("errcode") == 0) {
                return new CommonResult(resultJo.getStr("user_id_key"));
            } else {
                return new CommonResult(resultJo.getInt("errcode"), resultJo.getStr("errmsg"));
            }
        } else {
            return new CommonResult(-1, "用户已经实名");
        }
    }

    public CommonResult getinfo(String userId, String IdCard, String Name, String verify_result) {
        UserEntity userEntity = userMapper.selectUserById(userId);
        if (StringUtils.isBlank(userEntity.getIdentity())) {
            JSONObject resultJo = thirdService.getinfo(verify_result);
            if (resultJo.getInt("errcode") == 0) {
                String IdCard_md5 = MD5Util.MD5Encode(IdCard, "utf-8");
                String Name_md5 = MD5Util.MD5Encode(Name, "utf-8");
                String id_card_number_md5 = resultJo.getStr("id_card_number_md5");
                String name_utf8_md5 = resultJo.getStr("name_utf8_md5");
                if (IdCard_md5.equals(id_card_number_md5) && Name_md5.equals(name_utf8_md5)) {
                    //todo 处理实名认证
                    userEntity.setIdentityType("身份证");
                    userEntity.setIdentity(IdCard);
                    userEntity.setName(Name);
                    userMapper.smrzUser(userEntity);
                    return new CommonResult();
                } else {
                    return new CommonResult(-1, "用户已经实名");
                }
            } else {
                return new CommonResult(resultJo.getInt("errcode"), resultJo.getStr("errmsg"));
            }
        } else {
            return new CommonResult(-1, "用户已经实名");
        }

    }

    //1身份证 手机都不存在 新增用户 2手机存在 身份证为空更新 3身份证存在 不处理 4手机存在 身份证不同 不处理
    public int syncStoremgmtUser(UserTransformDTO userTransformDTO) {

        UserEntity ex = userMapper.getUserByIdentity(userTransformDTO.getIdentity());
        if (ex == null) {
            if (StringUtils.isNotBlank(userTransformDTO.getPhone())) {
                UserEntity phone_ex = userMapper.getUserByPhone(userTransformDTO.getPhone());
                if (phone_ex == null) {
                    UserEntity addUserEntity = new UserEntity();
                    BeanUtils.copyProperties(userTransformDTO, addUserEntity);
                    String userId = SnowflakeUtil.generateId();
                    addUserEntity.setId(userId);
                    userMapper.syncAddStoremgmtUser(addUserEntity);
                    return 1;
                }else{
                    if(StringUtils.isBlank(phone_ex.getIdentity())){
                        UserEntity updateUserEntity = new UserEntity();
                        BeanUtils.copyProperties(userTransformDTO, updateUserEntity);
                        updateUserEntity.setId(phone_ex.getId());
                        userMapper.syncUpdStoremgmtUser(updateUserEntity);
                        return 2;
                    }else{
                        return 4;
                    }
                }
            } else {
                UserEntity addUserEntity = new UserEntity();
                BeanUtils.copyProperties(userTransformDTO, addUserEntity);
                String userId = SnowflakeUtil.generateId();
                addUserEntity.setId(userId);
                userMapper.syncAddStoremgmtUser(addUserEntity);
                return 1;
            }
        }else{
            return 3;
        }

    }

}
