package com.jkga.wyyjs.mapper;

import com.jkga.wyyjs.model.entity.UserRoleEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author：clyde
 * @Date：2025/2/11 10:59
 */
@Mapper
public interface UserRoleMapper {

    boolean batchInsertUserRole(@Param("list") List<UserRoleEntity> list);

    boolean delUserRoleByArea(@Param("areaId") String areaId, @Param("userId") String userId);

    List<UserRoleEntity> getAreaManagerUserRole(@Param("areaId") String areaId, @Param("userId") String userId);

    boolean insertUserRole(UserRoleEntity userRoleEntity);

    boolean delUserRole(@Param("id") String id);

    List<UserRoleEntity> getUserRoleByUserId(@Param("userId") String userId);
}
