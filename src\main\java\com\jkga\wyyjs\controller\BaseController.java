package com.jkga.wyyjs.controller;

import com.jkga.wyyjs.exception.MyException;
import com.jkga.wyyjs.mapper.UserMapper;
import com.jkga.wyyjs.model.CommonCode;
import com.jkga.wyyjs.model.CommonResult;
import com.jkga.wyyjs.model.entity.UserEntity;
import com.jkga.wyyjs.utils.CusAccessObjectUtil;
import com.jkga.wyyjs.utils.TokenUtil;
import lombok.extern.slf4j.Slf4j;
import net.coobird.thumbnailator.Thumbnails;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * 基础接口
 *
 * <AUTHOR>
 * @date 2025/3/3 10:20
 */
@Slf4j
@CrossOrigin
@RestController
@RequestMapping("base")
public class BaseController {

    @Autowired
    private UserMapper userMapper;

    @Value("${file.root-path}")
    private String FILE_ROOT_PATH;


    /**
     * 图片访问控制
     *
     * @param token
     * @param imgUrl
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/imgAccessControl", method = RequestMethod.GET)
    public void imgAccessControl(String token,
                                 String imgUrl,
                                 HttpServletRequest request,
                                 HttpServletResponse response) throws Exception {
        if (StringUtils.isBlank(imgUrl) || "undefined".equals(imgUrl) || "null".equals(imgUrl)) {
            throw new MyException(new CommonResult<>(CommonCode.PARAM_ERROR, "图片路径无效", null));
        }
        //路径遍历漏洞/../../root/SpringBoot.jpg
        Path resolvedPath = Paths.get(FILE_ROOT_PATH).resolve(imgUrl).normalize();
        if (!resolvedPath.startsWith(FILE_ROOT_PATH)) {
            throw new MyException(new CommonResult<>(CommonCode.SYSTEM_ERROR, "非法路径访问", null));
        }
        boolean verifyResult = TokenUtil.isTokenValid(token);
        if (!verifyResult) {
            throw new MyException(new CommonResult<>(CommonCode.TOKEN_UNAUTHORIZED, "token无效", null));
        }

        //当前用户信息
        String userId = (String) TokenUtil.getPayload(token, "uid");
        UserEntity userEntity = userMapper.selectUserById(userId);
        if (userEntity == null) {
            throw new MyException(new CommonResult<>(CommonCode.SYSTEM_ERROR, "用户不存在", null));
        }
        //信息打印
        String ip = CusAccessObjectUtil.getIpAddress(request);
        log.info("图片访问: 用户ID={}, IP={}, 文件名={}", userEntity.getId(), ip, imgUrl);

        //==========1.返回字节流（弃用）==========
//        ByteArrayOutputStream out = new ByteArrayOutputStream();
////        try {
////            String imgLocalPath = prefixPath + img_url;
////            BufferedImage bufferedImage = ImageIO.read(new FileInputStream(new File(imgLocalPath)));
////            ImageIO.write(bufferedImage, "png", out);
////        } catch (Exception e) {
////            e.printStackTrace();
////            return null;
////        }
////        return out.toByteArray();

        //==========2.图片压缩返回（弃用）==========
//        response.setContentType(MediaType.IMAGE_JPEG_VALUE);
//        ServletOutputStream outputStream = response.getOutputStream();
//
//        //本地文件路径
//        String filePath = prefixPath + img_url;
//        //判断文件是否存在
//        Path path = Paths.get(filePath);
//        boolean exists = Files.exists(path);
//        if (!exists) {
//            return;
//        }
//
//        BufferedImage originalImage = ImageIO.read(new File(filePath));
//
//        // 获取 JPEG writer
//        Iterator<ImageWriter> writers = ImageIO.getImageWritersByFormatName("jpg");
//        if (!writers.hasNext()) {
//            throw new IllegalStateException("No writers found for format jpg.");
//        }
//        ImageWriter writer = writers.next();
//
//        // 设置输出流
//        ImageOutputStream ios = ImageIO.createImageOutputStream(outputStream);
//        writer.setOutput(ios);
//
//        // 设置高质量的压缩参数
//        ImageWriteParam iwp = writer.getDefaultWriteParam();
//        iwp.setCompressionMode(ImageWriteParam.MODE_EXPLICIT);
//        iwp.setCompressionQuality(0.8f); // 质量因子，范围是0.0到1.0，值越接近1.0质量越好
//
//        // 写出图片
//        writer.write(null, new IIOImage(originalImage, null, null), iwp);
//
//        // 清理资源
//        writer.dispose();
//        ios.close();


        //==========3.直接将图片流写入HTTP响应流（弃用，请求响应快但内容未经过压缩，大图片下载慢）==========
//        response.setContentType(MediaType.IMAGE_JPEG_VALUE);
//        ServletOutputStream outputStream = response.getOutputStream();
//
//        //本地文件路径
//        String filePath = prefixPath + img_url;
//        //判断文件是否存在
//        Path path = Paths.get(filePath);
//        boolean exists = Files.exists(path);
//        if (!exists) {
//            return;
//        }
//
//        try (InputStream inputStream = new FileInputStream(new File(filePath))) {
//            //复制流，直接将图片流写入HTTP响应，避免将整个图片加载到内存中，从而降低内存消耗和提高处理效率
//            IOUtils.copy(inputStream, outputStream);
//        }
//
//        outputStream.flush();

        //==========4.使用Thumbnails进行流式压缩（与直接将图片流写入HTTP响应流结合）==========
        String mimeType = Files.probeContentType(resolvedPath);
        //动态设置Content-Type
        response.setContentType(StringUtils.isNotBlank(mimeType) ? mimeType : MediaType.IMAGE_JPEG_VALUE);
        //添加缓存头减少服务器压力，避免重复请求影响性能
        response.setHeader("Cache-Control", "max-age=3600, public");

        //判断文件是否存在
        boolean exists = Files.exists(resolvedPath);
        if (!exists) {
            throw new MyException(new CommonResult<>(CommonCode.SYSTEM_ERROR, "文件不存在", null));
        }
        //判断文件大小，是否小于5MB
        long length = Files.size(resolvedPath);
        boolean isLessThan5MB = length < 5 * 1024 * 1024; // 5 MB in bytes

        //输出图片流
        try (ServletOutputStream outputStream = response.getOutputStream();
             InputStream inputStream = Files.newInputStream(resolvedPath)) {
            if (isLessThan5MB) {
                //复制流，直接将图片流写入HTTP响应，避免将整个图片加载到内存中，从而降低内存消耗和提高处理效率
                IOUtils.copy(inputStream, outputStream);
            } else {
                //输出质量
                double quality = 0.5;
                //使用 Thumbnails 进行流式压缩
                Thumbnails.of(inputStream)
                        .scale(1f)//不改变图片尺寸
                        .outputQuality(quality)//设置输出质量，范围是0.0-1.0
                        .toOutputStream(outputStream);//直接写入输出流
            }
            outputStream.flush();
        } catch (IOException e) {
            log.error("图片处理失败: {}", e.getMessage());
            throw new MyException(new CommonResult<>(CommonCode.SYSTEM_ERROR, "图片处理失败", null));
        }
    }

}
