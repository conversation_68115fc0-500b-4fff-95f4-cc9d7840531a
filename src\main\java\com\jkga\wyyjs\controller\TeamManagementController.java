package com.jkga.wyyjs.controller;

import com.jkga.wyyjs.constant.UserFunctionEnum;
import com.jkga.wyyjs.model.CommonCode;
import com.jkga.wyyjs.model.CommonResult;
import com.jkga.wyyjs.service.TeamManagementService;
import com.jkga.wyyjs.utils.PermissionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * 团队管理控制器
 * 
 * @Author：clyde
 * @Date：2025/7/18
 */
@RestController
@RequestMapping("/api/team")
@Slf4j
public class TeamManagementController {

    @Autowired
    HttpServletRequest request;
    @Autowired
    private TeamManagementService teamManagementService;
    @Autowired
    private PermissionUtils permissionUtils;
    /**
     * 获取当前团队人员
     */
    @GetMapping("/getTeamMembers")
    public CommonResult getTeamMembers(@RequestParam String managerId,
                                      @RequestParam Integer type) {
        log.info("获取团队人员: managerId={}, type={}", managerId, type);
        return teamManagementService.getTeamMembers(managerId, type);
    }

    /**
     * 新增部门
     */
    @PostMapping("/addDepartment")
    public CommonResult addDepartment(@RequestParam String managerId,
                                     @RequestParam Integer type,
                                     @RequestParam String name) {
        log.info("新增部门: managerId={}, type={}, name={}", managerId, type, name);
        //权限控制
        if (!permissionUtils.judeUserPermission(request.getHeader("uid"), request.getHeader("area_id"),request.getHeader("place_id"), UserFunctionEnum.WYBMGL, request.getRequestURI())) {
            return new CommonResult(CommonCode.NO_PERMISSION);
        }
        return teamManagementService.addDepartment(managerId, type, name);
    }

    /**
     * 修改部门名称
     */
    @PostMapping("/updateDepartmentName")
    public CommonResult updateDepartmentName(@RequestParam String departmentId,
                                            @RequestParam String name) {
        log.info("修改部门名称: departmentId={}, name={}", departmentId, name);
        //权限控制
        if (!permissionUtils.judeUserPermission(request.getHeader("uid"), request.getHeader("area_id"),request.getHeader("place_id"), UserFunctionEnum.WYBMGL, request.getRequestURI())) {
            return new CommonResult(CommonCode.NO_PERMISSION);
        }
        return teamManagementService.updateDepartmentName(departmentId, name);
    }

    /**
     * 删除部门
     */
    @GetMapping("/deleteDepartment")
    public CommonResult deleteDepartment(@RequestParam String departmentId) {
        log.info("删除部门: departmentId={}", departmentId);
        //权限控制
        if (!permissionUtils.judeUserPermission(request.getHeader("uid"), request.getHeader("area_id"),request.getHeader("place_id"), UserFunctionEnum.WYBMGL, request.getRequestURI())) {
            return new CommonResult(CommonCode.NO_PERMISSION);
        }
        return teamManagementService.deleteDepartment(departmentId);
    }

    /**
     * 获取部门列表
     */
    @GetMapping("/getDepartmentList")
    public CommonResult getDepartmentList(@RequestParam String managerId,
                                         @RequestParam Integer type) {
        log.info("获取部门列表: managerId={}, type={}", managerId, type);
        //权限控制
        if (!permissionUtils.judeUserPermission(request.getHeader("uid"), request.getHeader("area_id"),request.getHeader("place_id"), UserFunctionEnum.WYTDGL, request.getRequestURI())) {
            return new CommonResult(CommonCode.NO_PERMISSION);
        }
        return teamManagementService.getDepartmentList(managerId, type);
    }

    /**
     * 获取角色列表
     */
    @GetMapping("/getRoleList")
    public CommonResult getRoleList(@RequestParam String managerId,
                                   @RequestParam Integer type) {
        log.info("获取角色列表: managerId={}, type={}", managerId, type);
        //权限控制
        if (!permissionUtils.judeUserPermission(request.getHeader("uid"), request.getHeader("area_id"),request.getHeader("place_id"), UserFunctionEnum.WYTDGL, request.getRequestURI())) {
            return new CommonResult(CommonCode.NO_PERMISSION);
        }
        return teamManagementService.getRoleList(managerId, type);
    }

    /**
     * 修改用户的角色、部门、职位
     */
    @PostMapping("/updateMember")
    public CommonResult updateMember(@RequestParam String managerId,
                                    @RequestParam Integer type,
                                    @RequestParam String userId,
                                    @RequestParam String roleId,
                                    @RequestParam(required = false) String departmentId,
                                    @RequestParam(required = false) String position) {
        log.info("修改用户信息: managerId={}, type={}, userId={}, roleId={}, departmentId={}, position={}",
                managerId, type, userId, roleId, departmentId, position);
        //权限控制
        if (!permissionUtils.judeUserPermission(request.getHeader("uid"), request.getHeader("area_id"),request.getHeader("place_id"), UserFunctionEnum.WYTDGL, request.getRequestURI())) {
            return new CommonResult(CommonCode.NO_PERMISSION);
        }
        return teamManagementService.updateMember(managerId, type, userId, roleId, departmentId, position);
    }

    /**
     * 删除人员
     */
    @GetMapping("/deleteMember")
    public CommonResult deleteMember(@RequestParam String managerId,
                                    @RequestParam Integer type,
                                    @RequestParam String userId) {
        log.info("删除人员: managerId={}, type={}, userId={}", managerId, type, userId);
        //权限控制
        if (!permissionUtils.judeUserPermission(request.getHeader("uid"), request.getHeader("area_id"),request.getHeader("place_id"), UserFunctionEnum.WYTDGL, request.getRequestURI())) {
            return new CommonResult(CommonCode.NO_PERMISSION);
        }
        return teamManagementService.deleteMember(managerId, type, userId);
    }

    /**
     * 获取当前用户的中介权限
     */
    @GetMapping("/getUserIntermediaryPermissions")
    public CommonResult getUserIntermediaryPermissions() {
        String userId = request.getHeader("uid");
        log.info("获取用户中介权限: userId={}", userId);
        return teamManagementService.getUserIntermediaryPermissions(userId);
    }
}
