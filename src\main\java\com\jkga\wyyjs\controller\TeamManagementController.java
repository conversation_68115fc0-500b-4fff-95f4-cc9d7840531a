package com.jkga.wyyjs.controller;

import com.jkga.wyyjs.model.CommonResult;
import com.jkga.wyyjs.service.TeamManagementService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 团队管理控制器
 * 
 * @Author：clyde
 * @Date：2025/7/18
 */
@RestController
@RequestMapping("/api/team")
@Slf4j
public class TeamManagementController {

    @Autowired
    private TeamManagementService teamManagementService;

    /**
     * 获取当前团队人员
     */
    @GetMapping("/members")
    public CommonResult getTeamMembers(@RequestParam String managerId,
                                      @RequestParam Integer type) {
        log.info("获取团队人员: managerId={}, type={}", managerId, type);
        return teamManagementService.getTeamMembers(managerId, type);
    }

    /**
     * 新增部门
     */
    @PostMapping("/department")
    public CommonResult addDepartment(@RequestParam String managerId,
                                     @RequestParam Integer type,
                                     @RequestParam String name) {
        log.info("新增部门: managerId={}, type={}, name={}", managerId, type, name);
        return teamManagementService.addDepartment(managerId, type, name);
    }

    /**
     * 修改部门名称
     */
    @PutMapping("/department/{departmentId}")
    public CommonResult updateDepartmentName(@PathVariable String departmentId,
                                            @RequestParam String name) {
        log.info("修改部门名称: departmentId={}, name={}", departmentId, name);
        return teamManagementService.updateDepartmentName(departmentId, name);
    }

    /**
     * 删除部门
     */
    @DeleteMapping("/department/{departmentId}")
    public CommonResult deleteDepartment(@PathVariable String departmentId) {
        log.info("删除部门: departmentId={}", departmentId);
        return teamManagementService.deleteDepartment(departmentId);
    }

    /**
     * 获取部门列表
     */
    @GetMapping("/departments")
    public CommonResult getDepartmentList(@RequestParam String managerId,
                                         @RequestParam Integer type) {
        log.info("获取部门列表: managerId={}, type={}", managerId, type);
        return teamManagementService.getDepartmentList(managerId, type);
    }

    /**
     * 获取角色列表
     */
    @GetMapping("/roles")
    public CommonResult getRoleList(@RequestParam String managerId,
                                   @RequestParam Integer type) {
        log.info("获取角色列表: managerId={}, type={}", managerId, type);
        return teamManagementService.getRoleList(managerId, type);
    }

    /**
     * 修改用户的角色、部门、职位
     */
    @PutMapping("/member")
    public CommonResult updateMember(@RequestParam String managerId,
                                    @RequestParam Integer type,
                                    @RequestParam String userId,
                                    @RequestParam String roleId,
                                    @RequestParam(required = false) String departmentId,
                                    @RequestParam(required = false) String position) {
        log.info("修改用户信息: managerId={}, type={}, userId={}, roleId={}, departmentId={}, position={}",
                managerId, type, userId, roleId, departmentId, position);
        return teamManagementService.updateMember(managerId, type, userId, roleId, departmentId, position);
    }

    /**
     * 删除人员
     */
    @DeleteMapping("/member")
    public CommonResult deleteMember(@RequestParam String managerId,
                                    @RequestParam Integer type,
                                    @RequestParam String userId) {
        log.info("删除人员: managerId={}, type={}, userId={}", managerId, type, userId);
        return teamManagementService.deleteMember(managerId, type, userId);
    }
}
