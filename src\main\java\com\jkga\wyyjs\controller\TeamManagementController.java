package com.jkga.wyyjs.controller;

import com.jkga.wyyjs.constant.UserFunctionEnum;
import com.jkga.wyyjs.model.CommonCode;
import com.jkga.wyyjs.model.CommonResult;
import com.jkga.wyyjs.service.TeamManagementService;
import com.jkga.wyyjs.utils.PermissionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

/**
 * 团队管理控制器
 * 
 * @Author：clyde
 * @Date：2025/7/18
 */
@RestController
@RequestMapping("/api/team")
@Slf4j
public class TeamManagementController {

    @Autowired
    HttpServletRequest request;
    @Autowired
    private TeamManagementService teamManagementService;
    @Autowired
    private PermissionUtils permissionUtils;
    /**
     * 获取当前团队人员
     */
    @GetMapping("/getTeamMembers")
    public CommonResult getTeamMembers(@RequestParam String managerId,
                                      @RequestParam Integer type) {
        log.info("获取团队人员: managerId={}, type={}", managerId, type);
        return teamManagementService.getTeamMembers(managerId, type);
    }

    /**
     * 新增部门
     */
    @PostMapping("/addDepartment")
    public CommonResult addDepartment(@RequestParam String managerId,
                                     @RequestParam Integer type,
                                     @RequestParam String name) {
        log.info("新增部门: managerId={}, type={}, name={}", managerId, type, name);
        //权限控制
        if (!permissionUtils.judeUserPermission(request.getHeader("uid"), request.getHeader("area_id"),request.getHeader("place_id"), UserFunctionEnum.WYBMGL, request.getRequestURI())) {
            return new CommonResult(CommonCode.NO_PERMISSION);
        }
        return teamManagementService.addDepartment(managerId, type, name);
    }

    /**
     * 修改部门名称
     */
    @PostMapping("/updateDepartmentName")
    public CommonResult updateDepartmentName(@RequestParam String departmentId,
                                            @RequestParam String name) {
        log.info("修改部门名称: departmentId={}, name={}", departmentId, name);
        //权限控制
        if (!permissionUtils.judeUserPermission(request.getHeader("uid"), request.getHeader("area_id"),request.getHeader("place_id"), UserFunctionEnum.WYBMGL, request.getRequestURI())) {
            return new CommonResult(CommonCode.NO_PERMISSION);
        }
        return teamManagementService.updateDepartmentName(departmentId, name);
    }

    /**
     * 删除部门
     */
    @GetMapping("/deleteDepartment")
    public CommonResult deleteDepartment(@RequestParam String departmentId) {
        log.info("删除部门: departmentId={}", departmentId);
        //权限控制
        if (!permissionUtils.judeUserPermission(request.getHeader("uid"), request.getHeader("area_id"),request.getHeader("place_id"), UserFunctionEnum.WYBMGL, request.getRequestURI())) {
            return new CommonResult(CommonCode.NO_PERMISSION);
        }
        return teamManagementService.deleteDepartment(departmentId);
    }

    /**
     * 获取部门列表
     */
    @GetMapping("/getDepartmentList")
    public CommonResult getDepartmentList(@RequestParam String managerId,
                                         @RequestParam Integer type) {
        log.info("获取部门列表: managerId={}, type={}", managerId, type);
        //权限控制
        if (!permissionUtils.judeUserPermission(request.getHeader("uid"), request.getHeader("area_id"),request.getHeader("place_id"), UserFunctionEnum.WYTDGL, request.getRequestURI())) {
            return new CommonResult(CommonCode.NO_PERMISSION);
        }
        return teamManagementService.getDepartmentList(managerId, type);
    }

    /**
     * 获取角色列表
     */
    @GetMapping("/getRoleList")
    public CommonResult getRoleList(@RequestParam String managerId,
                                   @RequestParam Integer type) {
        log.info("获取角色列表: managerId={}, type={}", managerId, type);
        //权限控制
        if (!permissionUtils.judeUserPermission(request.getHeader("uid"), request.getHeader("area_id"),request.getHeader("place_id"), UserFunctionEnum.WYTDGL, request.getRequestURI())) {
            return new CommonResult(CommonCode.NO_PERMISSION);
        }
        return teamManagementService.getRoleList(managerId, type);
    }

    /**
     * 修改用户的角色、部门、职位
     */
    @PostMapping("/updateMember")
    public CommonResult updateMember(@RequestParam String managerId,
                                    @RequestParam Integer type,
                                    @RequestParam String userId,
                                    @RequestParam String roleId,
                                    @RequestParam(required = false) String departmentId,
                                    @RequestParam(required = false) String position) {
        log.info("修改用户信息: managerId={}, type={}, userId={}, roleId={}, departmentId={}, position={}",
                managerId, type, userId, roleId, departmentId, position);
        //权限控制
        if (!permissionUtils.judeUserPermission(request.getHeader("uid"), request.getHeader("area_id"),request.getHeader("place_id"), UserFunctionEnum.WYTDGL, request.getRequestURI())) {
            return new CommonResult(CommonCode.NO_PERMISSION);
        }
        return teamManagementService.updateMember(managerId, type, userId, roleId, departmentId, position);
    }

    /**
     * 删除人员
     */
    @GetMapping("/deleteMember")
    public CommonResult deleteMember(@RequestParam String managerId,
                                    @RequestParam Integer type,
                                    @RequestParam String userId) {
        log.info("删除人员: managerId={}, type={}, userId={}", managerId, type, userId);
        //权限控制
        if (!permissionUtils.judeUserPermission(request.getHeader("uid"), request.getHeader("area_id"),request.getHeader("place_id"), UserFunctionEnum.WYTDGL, request.getRequestURI())) {
            return new CommonResult(CommonCode.NO_PERMISSION);
        }
        return teamManagementService.deleteMember(managerId, type, userId);
    }

    /**
     * 获取当前用户的中介权限
     */
    @GetMapping("/getUserIntermediaryPermissions")
    public CommonResult getUserIntermediaryPermissions() {
        String userId = request.getHeader("uid");
        log.info("获取用户中介权限: userId={}", userId);
        return teamManagementService.getUserIntermediaryPermissions(userId);
    }

    /**
     * 新增角色
     */
    @PostMapping("/addRole")
    public CommonResult addRole(@RequestParam String roleName,
                               @RequestParam Integer roleType,
                               @RequestParam String manageId) {
        log.info("新增角色: roleName={}, roleType={}, manageId={}", roleName, roleType, manageId);
        //权限控制
        if (!permissionUtils.judeUserPermission(request.getHeader("uid"), request.getHeader("area_id"),request.getHeader("place_id"), UserFunctionEnum.WYJSGL, request.getRequestURI())) {
            return new CommonResult(CommonCode.NO_PERMISSION);
        }
        return teamManagementService.addRole(roleName, roleType, manageId);
    }

    /**
     * 删除角色
     */
    @GetMapping("/deleteRole")
    public CommonResult deleteRole(@RequestParam String id) {
        log.info("删除角色: id={}", id);
        //权限控制
        if (!permissionUtils.judeUserPermission(request.getHeader("uid"), request.getHeader("area_id"),request.getHeader("place_id"), UserFunctionEnum.WYJSGL, request.getRequestURI())) {
            return new CommonResult(CommonCode.NO_PERMISSION);
        }
        return teamManagementService.deleteRole(id);
    }

    /**
     * 获取角色权限
     */
    @GetMapping("/getRolePermissions")
    public CommonResult getRolePermissions(@RequestParam String roleId) {
        log.info("获取角色权限: roleId={}", roleId);
        //权限控制
        if (!permissionUtils.judeUserPermission(request.getHeader("uid"), request.getHeader("area_id"),request.getHeader("place_id"), UserFunctionEnum.WYTDGL, request.getRequestURI())) {
            return new CommonResult(CommonCode.NO_PERMISSION);
        }
        return teamManagementService.getRolePermissions(roleId);
    }

    /**
     * 获取所有功能权限
     */
    @GetMapping("/getAllPermissions")
    public CommonResult getAllPermissions(@RequestParam Integer type) {
        log.info("获取所有功能权限: type={}", type);
        //权限控制
        if (!permissionUtils.judeUserPermission(request.getHeader("uid"), request.getHeader("area_id"),request.getHeader("place_id"), UserFunctionEnum.WYTDGL, request.getRequestURI())) {
            return new CommonResult(CommonCode.NO_PERMISSION);
        }
        return teamManagementService.getAllPermissions(type);
    }

    /**
     * 保存当前角色的权限
     */
    @PostMapping("/saveRolePermissions")
    public CommonResult saveRolePermissions(@RequestBody List<Map<String, String>> permissions) {
        log.info("保存角色权限: permissions={}", permissions);
        //权限控制
        if (!permissionUtils.judeUserPermission(request.getHeader("uid"), request.getHeader("area_id"),request.getHeader("place_id"), UserFunctionEnum.WYJSGL, request.getRequestURI())) {
            return new CommonResult(CommonCode.NO_PERMISSION);
        }
        return teamManagementService.saveRolePermissions(permissions);
    }

    /**
     * 获取当前用户的角色信息
     */
    @PostMapping("/getUserRoles")
    public CommonResult getUserRoles(@RequestParam Integer type,
                                    @RequestParam String userId,
                                     @RequestParam(required = false) String areaId,
                                     @RequestParam(required = false) String placeId
                                     ) {
        log.info("获取用户角色信息: type={}, userId={}", type, userId);
        //权限控制
        if (!permissionUtils.judeUserPermission(request.getHeader("uid"), request.getHeader("area_id"),request.getHeader("place_id"), UserFunctionEnum.WYTDGL, request.getRequestURI())) {
            return new CommonResult(CommonCode.NO_PERMISSION);
        }
        return teamManagementService.getUserRoles(type, userId, areaId, placeId);
    }

    /**
     * 增加管理员
     */
    @PostMapping("/addManager")
    public CommonResult addManager(@RequestParam(required = false) String areaId,
                                  @RequestParam(required = true) String userId,
                                  @RequestParam(required = false) String departmentId,
                                  @RequestParam(required = false) String position,
                                  @RequestParam(required = true) List<String> roleIds,
                                  @RequestParam(required = false) String placeId,
                                  @RequestParam(required = true) Integer type) {
        log.info("增加管理员: areaId={}, userId={}, departmentId={}, position={}, roleIds={}, placeId={}, type={}",
                areaId, userId, departmentId, position, roleIds, placeId, type);
        //权限控制
        if (!permissionUtils.judeUserPermission(request.getHeader("uid"), request.getHeader("area_id"),request.getHeader("place_id"), UserFunctionEnum.WYTDGL, request.getRequestURI())) {
            return new CommonResult(CommonCode.NO_PERMISSION);
        }
        return teamManagementService.addManager(areaId, userId, departmentId, position, roleIds, placeId, type);
    }

    /**
     * 获取用户权限信息
     */
    @GetMapping("/getUserPermissionTypes")
    public CommonResult getUserPermissionTypes(@RequestParam String userId) {
        log.info("获取用户权限信息: userId={}", userId);
        //权限控制
        if (!permissionUtils.judeUserPermission(request.getHeader("uid"), request.getHeader("area_id"),request.getHeader("place_id"), UserFunctionEnum.WYFKDJ, request.getRequestURI())) {
            return new CommonResult(CommonCode.NO_PERMISSION);
        }
        return teamManagementService.getUserPermissionTypes(userId);
    }
}
