<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jkga.wyyjs.mapper.IntermediaryManagerMapper">

    <resultMap id="IntermediaryManagerResultMap" type="com.jkga.wyyjs.model.entity.IntermediaryManagerEntity">
        <id     property="id"     column="id"     />
        <result property="userId"   column="user_id"   />
        <result property="userName"   column="user_name"   typeHandler="com.jkga.wyyjs.component.SM4TypeHandler"/>
        <result property="phone"   column="phone"   typeHandler="com.jkga.wyyjs.component.SM4TypeHandler"/>
        <result property="position"   column="position"   />
        <result property="intime"   column="intime"   />
        <result property="deleteStatus"   column="delete_status"   />
        <result property="departmentId"   column="department_id"   />
    </resultMap>

    <select id="getIntermediaryManagerByUserId" resultMap="IntermediaryManagerResultMap">
        SELECT * from "intermediary_manager" where "user_id" = #{userId} and "delete_status" = 0 limit 1
    </select>

    <insert id="insertIntermediaryManager" parameterType="com.jkga.wyyjs.model.entity.IntermediaryManagerEntity">
        insert into "intermediary_manager"(
            "id",
            "user_id",
            "user_name",
            "phone",
            "position",
            "intime",
            "delete_status",
            "department_id"
            )values(
            #{id},
            #{userId},
            #{userName,typeHandler=com.jkga.wyyjs.component.SM4TypeHandler},
            #{phone,typeHandler=com.jkga.wyyjs.component.SM4TypeHandler},
            #{position},
            #{intime},
            #{deleteStatus},
            #{departmentId}
        )
    </insert>

    <update id="delIntermediaryManagerByUserId">
       update "intermediary_manager"
        set
        "delete_status" = 1
        where "user_id" = #{userId}
    </update>

</mapper>
