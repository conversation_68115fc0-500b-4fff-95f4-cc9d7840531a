package com.jkga.wyyjs.utils;

import com.jkga.wyyjs.constant.UserFunctionEnum;
import com.jkga.wyyjs.mapper.UserMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Author：clyde
 * @Date：2025/7/7 16:19
 */
@Service
@Slf4j
public class PermissionUtils {

    @Autowired
    private UserMapper userMapper;


    public boolean judeUserPermission(String userId, String area_id, String place_id, UserFunctionEnum userFunctionEnum, String path) {

        if (StringUtils.isBlank(userId)) {
            log.info("调用" + path + "接口,用户不存在");
            return false;
        }

        List<String> permissions = userMapper.getUserPermissions(userId, area_id, place_id);
        if (permissions != null && permissions.contains(userFunctionEnum.getCode())) {
            return true;
        } else {
            log.info("没有" + path + "接口权限");
            return false;
        }
    }

}
