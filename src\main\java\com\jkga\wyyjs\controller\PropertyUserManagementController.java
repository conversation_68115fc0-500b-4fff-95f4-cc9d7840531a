package com.jkga.wyyjs.controller;

import com.jkga.wyyjs.aspect.RecordLog;
import com.jkga.wyyjs.constant.UserFunctionEnum;
import com.jkga.wyyjs.model.CommonCode;
import com.jkga.wyyjs.model.CommonResult;
import com.jkga.wyyjs.model.vo.*;
import com.jkga.wyyjs.service.PropertyUserManagementService;
import com.jkga.wyyjs.utils.PermissionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

/**
 * 小区物业人员管理
 *
 * @Author：clyde
 * @Date：2025/2/7 14:26
 */
@Slf4j
@CrossOrigin
@RestController
@RequestMapping("propertyUserManagement")
public class PropertyUserManagementController {

    @Autowired
    HttpServletRequest request;
    @Autowired
    private PropertyUserManagementService propertyUserManagementService;
    @Autowired
    private PermissionUtils permissionUtils;

    /**
     * 添加物业角色
     *
     * @Author：clyde
     * @Date：2025/2/6 10:42
     */
    @RecordLog(optModel = "用户管理", optDesc = "添加物业角色")
    @RequestMapping(value = "addAreaRole", method = RequestMethod.POST)
    public CommonResult addAreaRole(@RequestBody @Valid RoleVO roleVO) {
        //权限控制
        if (!permissionUtils.judeUserPermission(request.getHeader("uid"), request.getHeader("area_id"), UserFunctionEnum.WYJSGL, request.getRequestURI())) {
            return new CommonResult(CommonCode.NO_PERMISSION);
        }
        try {
            return propertyUserManagementService.addAreaRole(roleVO);
        } catch (Exception e) {
            return new CommonResult(CommonCode.SYSTEM_ERROR);
        }
    }

    /**
     * 添加物业部门
     *
     * @Author：clyde
     * @Date：2025/2/6 15:04
     */
    @RecordLog(optModel = "用户管理", optDesc = "添加物业部门")
    @RequestMapping(value = "addAreaDepartment", method = RequestMethod.POST)
    public CommonResult addAreaDepartment(@RequestBody @Valid AreaDepartmentVO areaDepartmentVO) {
        //权限控制
        if (!permissionUtils.judeUserPermission(request.getHeader("uid"), request.getHeader("area_id"), UserFunctionEnum.WYBMGL, request.getRequestURI())) {
            return new CommonResult(CommonCode.NO_PERMISSION);
        }
        try {
            return propertyUserManagementService.addAreaDepartment(areaDepartmentVO);
        } catch (Exception e) {
            return new CommonResult(CommonCode.SYSTEM_ERROR);
        }
    }

    /**
     * 获取小区角色列表
     *
     * @Author：clyde
     * @Date：2025/2/6 16:37
     */
    @RequestMapping(value = "getAreaRoleList", method = RequestMethod.GET)
    public CommonResult getAreaRoleList(String areaId) {

        //权限控制
        if (!permissionUtils.judeUserPermission(request.getHeader("uid"), request.getHeader("area_id"), UserFunctionEnum.WYTDGL, request.getRequestURI())) {
            return new CommonResult(CommonCode.NO_PERMISSION);
        }
        try {
            return propertyUserManagementService.getAreaRoleList(areaId);
        } catch (Exception e) {
            return new CommonResult(CommonCode.SYSTEM_ERROR);
        }
    }

    /**
     * 获取小区物业部门列表
     *
     * @Author：clyde
     * @Date：2025/2/7 10:10
     */
    @RequestMapping(value = "getAreaDepartmentList", method = RequestMethod.GET)
    public CommonResult getAreaDepartmentList(String areaId) {
        try {
            return propertyUserManagementService.getAreaDepartmentList(areaId);
        } catch (Exception e) {
            return new CommonResult(CommonCode.SYSTEM_ERROR);
        }
    }

    /**
     * 获取物业人员详细信息
     *
     * @Author：clyde
     * @Date：2025/2/11 14:28
     */
    @RequestMapping(value = "getAreaManagerUserRole", method = RequestMethod.GET)
    public CommonResult getAreaManagerUserRole(String id) {

        //权限控制
        if (!permissionUtils.judeUserPermission(request.getHeader("uid"), request.getHeader("area_id"), UserFunctionEnum.WYTDGL, request.getRequestURI())) {
            return new CommonResult(CommonCode.NO_PERMISSION);
        }
        try {
            return propertyUserManagementService.getAreaManagerUserRole(id);
        } catch (Exception e) {
            return new CommonResult(CommonCode.SYSTEM_ERROR);
        }
    }

    /**
     * 添加物业人员
     *
     * @Author：clyde
     * @Date：2025/2/7 11:11
     */
    @RecordLog(optModel = "用户管理", optDesc = "添加物业人员")
    @RequestMapping(value = "addAreaManager", method = RequestMethod.POST)
    public CommonResult addAreaManager(@RequestBody @Valid AreaManagerVO areaManagerVO) {
        //权限控制
        if (!permissionUtils.judeUserPermission(request.getHeader("uid"), request.getHeader("area_id"), UserFunctionEnum.WYTDGL, request.getRequestURI())) {

            return new CommonResult(CommonCode.NO_PERMISSION);
        }
        try {
            return propertyUserManagementService.addAreaManager(areaManagerVO);
        } catch (Exception e) {
            return new CommonResult(CommonCode.SYSTEM_ERROR);
        }
    }

    /**
     * 修改物业人员部门 职位 角色
     *
     * @Author：clyde
     * @Date：2025/2/10 16:32
     */
    @RecordLog(optModel = "用户管理", optDesc = "修改物业人员")
    @RequestMapping(value = "updAreaManager", method = RequestMethod.POST)
    public CommonResult updAreaManager(@RequestBody AreaManagerVO areaManagerVO) {
        //权限控制
        if (!permissionUtils.judeUserPermission(request.getHeader("uid"), request.getHeader("area_id"), UserFunctionEnum.WYTDGL, request.getRequestURI())) {
            return new CommonResult(CommonCode.NO_PERMISSION);
        }
        try {
            return propertyUserManagementService.updAreaManager(areaManagerVO);
        } catch (Exception e) {
            return new CommonResult(CommonCode.SYSTEM_ERROR);
        }
    }

    /**
     * 获取权限列表
     *
     * @Author：clyde
     * @Date：2025/2/7 15:16
     */
    @RequestMapping(value = "getPermissionList", method = RequestMethod.GET)
    public CommonResult getPermissionList() {
        //权限控制
        if (!permissionUtils.judeUserPermission(request.getHeader("uid"), request.getHeader("area_id"), UserFunctionEnum.WYJSGL, request.getRequestURI())) {

            return new CommonResult(CommonCode.NO_PERMISSION);
        }
        try {
            return propertyUserManagementService.getPermissionList();
        } catch (Exception e) {
            return new CommonResult(CommonCode.SYSTEM_ERROR);
        }
    }

    /**
     * 添加角色权限
     *
     * @Author：clyde
     * @Date：2025/2/7 14:07
     */
    @RecordLog(optModel = "用户管理", optDesc = "添加角色权限")
    @RequestMapping(value = "addAreaRolePermission", method = RequestMethod.POST)
    public CommonResult addAreaRolePermission(@RequestBody RolePermissionVO rolePermissionVO) {
        //权限控制
        if (!permissionUtils.judeUserPermission(request.getHeader("uid"), request.getHeader("area_id"), UserFunctionEnum.WYJSGL, request.getRequestURI())) {
            return new CommonResult(CommonCode.NO_PERMISSION);
        }
        try {
            return propertyUserManagementService.addAreaRolePermission(rolePermissionVO);
        } catch (Exception e) {
            return new CommonResult(CommonCode.SYSTEM_ERROR);
        }
    }

    /**
     * 获取角色权限
     *
     * @Author：clyde
     * @Date：2025/2/7 15:35
     */
    @RequestMapping(value = "getRolePermissionList", method = RequestMethod.GET)
    public CommonResult getRolePermissionList(String roleId) {
        //权限控制
        if (!permissionUtils.judeUserPermission(request.getHeader("uid"), request.getHeader("area_id"), UserFunctionEnum.WYJSGL, request.getRequestURI())) {
            return new CommonResult(CommonCode.NO_PERMISSION);
        }
        try {
            return propertyUserManagementService.getRolePermissionList(roleId);
        } catch (Exception e) {
            return new CommonResult(CommonCode.SYSTEM_ERROR);
        }
    }

    /**
     * 删除物业人员
     *
     * @Author：clyde
     * @Date：2025/2/17 14:04
     */
    @RecordLog(optModel = "用户管理", optDesc = "删除物业人员")
    @RequestMapping(value = "delAreaManager", method = RequestMethod.GET)
    public CommonResult delAreaManager(String id) {
        //权限控制
        if (!permissionUtils.judeUserPermission(request.getHeader("uid"), request.getHeader("area_id"), UserFunctionEnum.WYTDGL, request.getRequestURI())) {
            return new CommonResult(CommonCode.NO_PERMISSION);
        }
        try {
            return propertyUserManagementService.delAreaManager(id);
        } catch (Exception e) {
            return new CommonResult(CommonCode.SYSTEM_ERROR);
        }
    }

    /**
     * 获取物业人员小区列表
     *
     * @Author：clyde
     * @Date：2025/2/27 14:00
     */
    @RequestMapping(value = "getManagerAreas", method = RequestMethod.GET)
    public CommonResult getManagerAreas() {
        try {
            String userId = request.getHeader("uid");
            return propertyUserManagementService.getManagerAreas(userId);
        } catch (Exception e) {
            return new CommonResult(CommonCode.SYSTEM_ERROR);
        }
    }


    /**
     * 获取我的小区的权限列表
     *
     * @Author：clyde
     * @Date：2025/2/27 14:00
     */
    @RequestMapping(value = "getManagerPermissions", method = RequestMethod.GET)
    public CommonResult getManagerPermissions() {
        try {
            String userId = request.getHeader("uid");
            String areaId = request.getHeader("area_id");
            return propertyUserManagementService.getManagerPermissions(userId, areaId);
        } catch (Exception e) {
            e.printStackTrace();
            return new CommonResult(CommonCode.SYSTEM_ERROR);
        }
    }


}
