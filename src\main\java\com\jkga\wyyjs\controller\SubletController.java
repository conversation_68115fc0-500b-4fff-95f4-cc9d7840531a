package com.jkga.wyyjs.controller;

import cn.hutool.json.JSONObject;
import com.jkga.wyyjs.aspect.RecordLog;
import com.jkga.wyyjs.model.CommonCode;
import com.jkga.wyyjs.model.CommonResult;
import com.jkga.wyyjs.model.configuration.WeixinConfiguration;
import com.jkga.wyyjs.model.entity.SubletHouseEntity;
import com.jkga.wyyjs.model.vo.HandleSubletHouseOwnerVO;
import com.jkga.wyyjs.model.vo.HandleSubletRoomToRenterVO;
import com.jkga.wyyjs.model.vo.SubletRenterRegisterVO;
import com.jkga.wyyjs.service.SubletService;
import com.jkga.wyyjs.utils.PermissionUtils;
import com.jkga.wyyjs.utils.WaterBase64;
import com.jkga.wyyjs.utils.WeixinUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 中介二房东
 *
 * @Author：clyde
 * @Date：2025/7/15 14:35
 */
@Slf4j
@CrossOrigin
@RestController
@RequestMapping("sublet")
public class SubletController {

    @Autowired
    HttpServletRequest request;
    @Autowired
    private PermissionUtils permissionUtils;
    @Autowired
    private WeixinConfiguration wxConfiguration;
    @Autowired
    private WeixinUtil weixinUtil;
    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private SubletService subletService;

    /**
     * 1.生成程序报送码
     *
     * @Author：clyde
     * @Date：2025/7/15 14:39
     */
    @RequestMapping(value = "/generateSubletMiniProgramCodePic", method = RequestMethod.GET)
    public CommonResult generateSubletMiniProgramCodePic() {
        String userId = request.getHeader("uid");
        if (StringUtils.isBlank(userId)) {
            return new CommonResult(CommonCode.NO_USER);
        }

        byte[] byteArray;
        String unlimited_url = wxConfiguration.getUnlimitedUrl();
        //获取access_token
        String access_token = weixinUtil.getAccessToken();
//        String access_token =  TokenStorage.accessToken.getAccess_token();
        //调用微信官方生成小程序码的api
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        JSONObject jsonParam = new JSONObject();
        jsonParam.put("scene", "cid=" + userId);
        jsonParam.put("page", "pagesIntermediary/wdbsm/index");
//        jsonParam.put("width", 280);
        jsonParam.put("width", 640);
        HttpEntity<String> requestEntity = new HttpEntity<>(jsonParam.toString(), headers);
        ResponseEntity<byte[]> responseEntity = restTemplate.exchange(unlimited_url + "?access_token=" + access_token,
                HttpMethod.POST, requestEntity, byte[].class);
        byteArray = responseEntity.getBody();
        String wxReturnStr = new String(byteArray);
        if (wxReturnStr.contains("errcode")) {
            return new CommonResult(500, wxReturnStr);
        }
        try {
            String base64img = WaterBase64.markImageByTextWithTip("", "出租登记码", byteArray, "", "相关人员请按规定，填报本人信息");
            return new CommonResult(base64img);
        } catch (Exception e) {
            return new CommonResult(CommonCode.SYSTEM_ERROR);
        }
    }

    /**
     * 2.获取楼幢下的房源列表
     * data_type：0：未添加到个人 1：已添加到个人 2：全部
     *
     * @Author：clyde
     * @Date：2025/7/15 16:23
     */
    @RequestMapping(value = "/getSubletRoomList", method = RequestMethod.GET)
    public CommonResult getSubletRoomList(@RequestParam(value = "data_type", required = false) int data_type,
                                          @RequestParam(value = "building_id", required = true) String building_id) {
        try {
            //当前用户信息
            String userId = request.getHeader("uid");
            return subletService.getSubletRoomList(building_id, data_type, userId);
        } catch (Exception e) {
            return new CommonResult(CommonCode.SYSTEM_ERROR);
        }
    }

    /**
     * 3.批量添加房源
     *
     * @Author：clyde
     * @Date：2025/7/16 16:39
     */
    @RecordLog(optModel = "中介二房东",optDesc = "批量添加房源")
    @RequestMapping(value = "/batchAddSubletRoom", method = RequestMethod.POST)
    public CommonResult batchAddSubletRoom(@RequestBody List<SubletHouseEntity> listVO) {
        try {
            //当前用户信息
            String userId = request.getHeader("uid");
            return subletService.batchAddSubletRoom(listVO, userId);
        } catch (Exception e) {
            return new CommonResult(CommonCode.SYSTEM_ERROR);
        }
    }


    /**
     * 4.扫码登记入住
     *
     * @Author：clyde
     * @Date：2025/7/17 9:18
     */
    @RecordLog(optModel = "中介二房东",optDesc = "扫码登记入住")
    @RequestMapping(value = "/registerSubletRenter", method = RequestMethod.POST, consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public CommonResult registerSubletRenter(SubletRenterRegisterVO registerVO) throws IOException {
        try {
            //当前用户信息
            String userId = request.getHeader("uid");
            return subletService.registerSubletRenter(registerVO, userId);
        } catch (Exception e) {
            return new CommonResult(CommonCode.SYSTEM_ERROR);
        }
    }

    /**
     * 5.未分配房间的租客列表
     *
     * @Author：clyde
     * @Date：2025/7/17 14:09
     */
    @RequestMapping(value = "/getNeedHandleSubletRenterList", method = RequestMethod.GET)
    public CommonResult getNeedHandleSubletRenterList(@RequestParam(defaultValue = "1") int page_num,
                                                      @RequestParam(defaultValue = "10") int page_size,
                                                      String search_param) {
        try {
            //当前用户信息
            String userId = request.getHeader("uid");
            return subletService.getNeedHandleSubletRenterList(page_num, page_size, search_param, userId);
        } catch (Exception e) {
            e.printStackTrace();
            return new CommonResult(CommonCode.SYSTEM_ERROR);
        }
    }

    /**
     * 6.我的房源所在的小区列表
     *
     * @Author：clyde
     * @Date：2025/7/17 15:24
     */
    @RequestMapping(value = "/getMySubletCommunityList", method = RequestMethod.GET)
    public CommonResult getMySubletCommunityList() {
        try {
            //当前用户信息
            String userId = request.getHeader("uid");
            return subletService.getMySubletCommunityList(userId);
        } catch (Exception e) {
            return new CommonResult(CommonCode.SYSTEM_ERROR);
        }
    }

    /**
     * 7.我关联小区下的房间列表（全部、已入住、闲置筛选）
     *
     * @param area_id
     * @param data_type 0：全部 1：闲置 2：已住
     * @Author：clyde
     * @Date：2025/7/17 15:47
     */
    @RequestMapping(value = "/getMySubletRoomListByCommunity", method = RequestMethod.GET)
    public CommonResult getMySubletRoomListByCommunity(String area_id,
                                                       int data_type,
                                                       String search_param) {
        try {
            //当前用户信息
            String userId = request.getHeader("uid");
            return subletService.getMySubletRoomListByCommunity(area_id, data_type, search_param, userId);
        } catch (Exception e) {
            e.printStackTrace();
            return new CommonResult(CommonCode.SYSTEM_ERROR);
        }
    }

    /**
     * 8.分配房间（上传合同，可新增其他租客）
     *
     * @Author：clyde
     * @Date：2025/7/17 16:36
     */
    @RecordLog(optModel = "中介二房东",optDesc = "分配房间（上传合同，可新增其他租客）")
    @RequestMapping(value = "/handleSubletRoomToRenter", method = RequestMethod.POST, consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public CommonResult handleSubletRoomToRenter(HandleSubletRoomToRenterVO handleVO) throws IOException {
        try {
            //当前用户信息
            String userId = request.getHeader("uid");
            handleVO.setOperatorId(userId);
            return subletService.handleSubletRoomToRenter(handleVO);
        } catch (Exception e) {
            e.printStackTrace();
            return new CommonResult(CommonCode.SYSTEM_ERROR);
        }
    }

    /**
     * 9.删除租客记录（取消入住）
     *
     * @Author：clyde
     * @Date：2025/7/18 16:00
     */
    @RecordLog(optModel = "中介二房东",optDesc = "删除租客记录（取消入住）")
    @RequestMapping(value = "/cancelSubletRenterRegister", method = RequestMethod.GET)
    public CommonResult cancelSubletRenterRegister(String renter_id) {
        try {
            //当前用户信息
            String userId = request.getHeader("uid");
            return subletService.cancelSubletRenterRegister(renter_id, userId);
        } catch (Exception e) {
            return new CommonResult(CommonCode.SYSTEM_ERROR);
        }
    }

    /**
     * 10.即将到期/已到期（或退租）租客记录列表
     * data_type    0：即将到期  1：已到期（或退租）
     *
     * @Author：clyde
     * @Date：2025/7/18 16:30
     */
    @RequestMapping(value = "/getMySubletRenterList", method = RequestMethod.GET)
    public CommonResult getMySubletRenterList(@RequestParam(defaultValue = "1") int page_num,
                                              @RequestParam(defaultValue = "10") int page_size,
                                              String search_param,
                                              int data_type) {
        try {
            //当前用户信息
            String userId = request.getHeader("uid");
            return subletService.getMySubletRenterList(page_num, page_size, search_param, data_type, userId);
        } catch (Exception e) {
            return new CommonResult(CommonCode.SYSTEM_ERROR);
        }
    }

    /**
     * 11.换房（重签合同）
     *
     * @return
     */
    @RecordLog(optModel = "中介二房东",optDesc = "换房（重签合同）")
    @RequestMapping(value = "/changeSubletRenterRoom", method = RequestMethod.POST, consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public CommonResult changeSubletRenterRoom(HandleSubletRoomToRenterVO handleVO) throws IOException {
        try {
            //当前用户信息
            String userId = request.getHeader("uid");
            handleVO.setOperatorId(userId);
            return subletService.changeSubletRenterRoom(handleVO);
        } catch (Exception e) {
            e.printStackTrace();
            return new CommonResult(CommonCode.SYSTEM_ERROR);
        }
    }


    /**
     * 12.退租
     *
     * @Author：clyde
     * @Date：2025/7/21 10:15
     */
    @RecordLog(optModel = "中介二房东",optDesc = "退租")
    @RequestMapping(value = "/leaveSubletRenterRoom", method = RequestMethod.POST)
    public CommonResult leaveSubletRenterRoom(@RequestBody Map<String, Object> paramMap) {
        try {
            String renterId = String.valueOf(paramMap.get("renter_id"));
            //当前用户信息
            String userId = request.getHeader("uid");
            return subletService.leaveSubletRenterRoom(renterId, userId);
        } catch (Exception e) {
            return new CommonResult(CommonCode.SYSTEM_ERROR);
        }
    }

    /**
     * 13.续租（重签合同）
     *
     * @Author：clyde
     * @Date：2025/7/21 10:19
     */
    @RecordLog(optModel = "中介二房东",optDesc = "续租（重签合同）")
    @RequestMapping(value = "/continueSubletRenterRoom", method = RequestMethod.POST)
    public CommonResult continueSubletRenterRoom(HandleSubletRoomToRenterVO handleVO) throws IOException {
        try {
            //当前用户信息
            String userId = request.getHeader("uid");
            handleVO.setOperatorId(userId);
            return subletService.continueSubletRenterRoom(handleVO);
        } catch (Exception e) {
            return new CommonResult(CommonCode.SYSTEM_ERROR);
        }
    }

    /**
     * 14.已有客人入住的小区列表
     *
     * @Author：clyde
     * @Date：2025/7/21 14:13
     */
    @RequestMapping(value = "/getHouseSubletCountByArea", method = RequestMethod.GET)
    public CommonResult getHouseSubletCountByArea() {
        try {
            //当前用户信息
            String userId = request.getHeader("uid");
            return subletService.getHouseSubletCountByArea(userId);
        } catch (Exception e) {
            return new CommonResult(CommonCode.SYSTEM_ERROR);
        }
    }

    /**
     * 15.有闲置房间的小区列表
     *
     * @Author：clyde
     * @Date：2025/7/21 14:53
     */
    @RequestMapping(value = "/getUnUsedHouseSubletCountByArea", method = RequestMethod.GET)
    public CommonResult getUnUsedHouseSubletCountByArea() {
        try {
            //当前用户信息
            String userId = request.getHeader("uid");
            return subletService.getUnUsedHouseSubletCountByArea(userId);
        } catch (Exception e) {
            return new CommonResult(CommonCode.SYSTEM_ERROR);
        }
    }

    /**
     * 16.房间下的租客（已住人员）列表
     *
     * @Author：clyde
     * @Date：2025/7/21 15:02
     */
    @RequestMapping(value = "/getHouseSubletRenterList", method = RequestMethod.GET)
    public CommonResult getHouseSubletRenterList(@RequestParam(defaultValue = "1") int page_num,
                                                 @RequestParam(defaultValue = "10") int page_size,
                                                 @RequestParam(value = "sublet_house_id", required = true) String sublet_house_id,
                                                 @RequestParam(value = "search_param", required = false) String search_param) {
        try {
            //当前用户信息
            String userId = request.getHeader("uid");
            return subletService.getHouseSubletRenterList(page_num, page_size, search_param, sublet_house_id, userId);
        } catch (Exception e) {
            return new CommonResult(CommonCode.SYSTEM_ERROR);
        }
    }

    /**
     * 17.租客合同照片列表
     *
     * @Author：clyde
     * @Date：2025/7/21 15:20
     */
    @RequestMapping(value = "/getHouseSubletContractImgList", method = RequestMethod.GET)
    public CommonResult getHouseSubletContractImgList(@RequestParam(value = "contract_id", required = true) String contract_id) {
        try {
            return subletService.getHouseSubletContractImgList(contract_id);
        } catch (Exception e) {
            return new CommonResult(CommonCode.SYSTEM_ERROR);
        }
    }

    /**
     * 18.新增租客到闲置房间
     *
     * @Author：clyde
     * @Date：2025/7/21 15:39
     */
    @RecordLog(optModel = "中介二房东",optDesc = "新增租客到闲置房间")
    @RequestMapping(value = "/addRenterToUnusedRoom", method = RequestMethod.POST)
    public CommonResult addRenterToUnusedRoom(HandleSubletRoomToRenterVO handleSubletRoomToRenterVO) {
        try {
            //当前用户信息
            String userId = request.getHeader("uid");
            handleSubletRoomToRenterVO.setOperatorId(userId);
            return subletService.addRenterToUnusedRoom(handleSubletRoomToRenterVO);
        } catch (Exception e) {
            return new CommonResult(CommonCode.SYSTEM_ERROR);
        }
    }

    /**
     * 19.删除闲置房间
     *
     * @Author：clyde
     * @Date：2025/7/21 16:10
     */
    @RecordLog(optModel = "中介二房东",optDesc = "删除闲置房间")
    @RequestMapping(value = "/delSubletUnusedRoom", method = RequestMethod.GET)
    public CommonResult delSubletUnusedRoom(@RequestParam(value = "sublet_house_id", required = true) String sublet_house_id) {
        try {
            return subletService.delSubletUnusedRoom(sublet_house_id);
        } catch (Exception e) {
            return new CommonResult(CommonCode.SYSTEM_ERROR);
        }
    }

    /**
     * 20.获取租客记录-人员身份证或手机号完整信息
     *
     * @Author：clyde
     * @Date：2025/7/21 16:22
     */
    @RecordLog(optModel = "中介二房东",optDesc = "获取租客记录-人员身份证或手机号完整信息")
    @RequestMapping(value = "/getRenterDecryptInfo", method = RequestMethod.GET)
    public CommonResult getRenterDecryptInfo(@RequestParam(value = "rentId", required = true) String rentId,
                                             @RequestParam(value = "type", required = true) Integer type) {
        try {
            return subletService.getRenterDecryptInfo(rentId, type);
        } catch (Exception e) {
            return new CommonResult(CommonCode.SYSTEM_ERROR);
        }
    }

    /**
     * 21.二房东租房管理首页统计
     *
     * @Author：clyde
     * @Date：2025/7/21 16:29
     */
    @RequestMapping(value = "/getSubletRoomIndexCount", method = RequestMethod.GET)
    public CommonResult getSubletRoomIndexCount() {
        try {
            //当前用户信息
            String userId = request.getHeader("uid");
            return subletService.getSubletRoomIndexCount(userId);
        } catch (Exception e) {
            return new CommonResult(CommonCode.SYSTEM_ERROR);
        }
    }

    /**
     * 22.房源绑定业主
     *
     * @Author：clyde
     * @Date：2025/7/21 16:40
     */
    @RecordLog(optModel = "中介二房东",optDesc = "房源绑定业主")
    @RequestMapping(value = "/bindSubletHouseOwner", method = RequestMethod.POST, consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public CommonResult bindSubletHouseOwner(HandleSubletHouseOwnerVO handleVO) {
        try {
            //当前用户信息
            String userId = request.getHeader("uid");
            handleVO.setOperatorId(userId);
            return subletService.bindSubletHouseOwner(handleVO);
        } catch (Exception e) {
            return new CommonResult(CommonCode.SYSTEM_ERROR);
        }
    }

    /**
     * 23.已绑定业主列表（即将到期筛选）
     * data_type    0：全部 1：即将到期
     *
     * @Author：clyde
     * @Date：2025/7/21 17:22
     */
    @RequestMapping(value = "/getBindSubletHouseOwnerList", method = RequestMethod.GET)
    public CommonResult getBindSubletHouseOwnerList(@RequestParam(defaultValue = "1") int page_num,
                                                    @RequestParam(defaultValue = "10") int page_size,
                                                    String search_param,
                                                    int data_type) {
        try {
            //当前用户信息
            String userId = request.getHeader("uid");
            return subletService.getBindSubletHouseOwnerList(page_num, page_size, search_param, userId, data_type);
        } catch (Exception e) {
            return new CommonResult(CommonCode.SYSTEM_ERROR);
        }
    }

    /**
     * 24.和业主退租（房源假删、租客在住不能退租）
     *
     * @Author：clyde
     * @Date：2025/7/22 10:06
     */
    @RecordLog(optModel = "中介二房东",optDesc = "和业主退租（房源假删、租客在住不能退租）")
    @RequestMapping(value = "/leaveSubletHouseOwner", method = RequestMethod.POST)
    public CommonResult leaveSubletHouseOwner(@RequestBody Map<String, Object> paramMap) {
        try {
            //当前用户信息
            String userId = request.getHeader("uid");
            String subletHouseId = String.valueOf(paramMap.get("subletHouseId"));
            return subletService.leaveSubletHouseOwner(subletHouseId, userId);
        } catch (Exception e) {
            return new CommonResult(CommonCode.SYSTEM_ERROR);
        }
    }

    /**
     * 25.和业主续期（重签合同）
     *
     * @Author：clyde
     * @Date：2025/7/22 10:14
     */
    @RecordLog(optModel = "中介二房东",optDesc = "和业主续期（重签合同）")
    @RequestMapping(value = "/continueSubletHouseOwner", method = RequestMethod.POST, consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public CommonResult continueSubletHouseOwner(HandleSubletHouseOwnerVO handleVO) throws IOException {
        try {
            //当前用户信息
            String userId = request.getHeader("uid");
            handleVO.setOperatorId(userId);
            return subletService.continueSubletHouseOwner(handleVO);
        } catch (Exception e) {
            return new CommonResult(CommonCode.SYSTEM_ERROR);
        }
    }


    /**
     * 26.未绑定业主的小区列表
     *
     * @Author：clyde
     * @Date：2025/7/22 10:33
     */
    @RequestMapping(value = "/getNoBindOwnerSubletCommunityList", method = RequestMethod.GET)
    public CommonResult getNoBindOwnerSubletCommunityList() {
        try {
            //当前用户信息
            String userId = request.getHeader("uid");
            return subletService.getNoBindOwnerSubletCommunityList(userId);
        } catch (Exception e) {
            return new CommonResult(CommonCode.SYSTEM_ERROR);
        }
    }

    /**
     * 27.未绑定业主的房源列表
     *
     * @Author：clyde
     * @Date：2025/7/22 10:45
     */
    @RequestMapping(value = "/getNoBindOwnerSubletHouseListByCommunity", method = RequestMethod.GET)
    public CommonResult getNoBindOwnerSubletHouseListByCommunity(String area_id,
                                                                 String search_param) {
        try {
            //当前用户信息
            String userId = request.getHeader("uid");
            return subletService.getNoBindOwnerSubletHouseListByCommunity(area_id, search_param, userId);
        } catch (Exception e) {
            e.printStackTrace();
            return new CommonResult(CommonCode.SYSTEM_ERROR);
        }
    }

    /**
     * 28.未绑定业主的房源数量
     *
     * @Author：clyde
     * @Date：2025/7/22 10:52
     */
    @RequestMapping(value = "/countNoOwnerSubletHouse", method = RequestMethod.GET)
    public CommonResult countNoOwnerSubletHouse() {
        try {
            //当前用户信息
            String userId = request.getHeader("uid");
            return subletService.countNoOwnerSubletHouse(userId);
        } catch (Exception e) {
            return new CommonResult(CommonCode.SYSTEM_ERROR);
        }
    }

    /**
     * 29.即将到期业主数
     *
     * @Author：clyde
     * @Date：2025/7/22 10:56
     */
    @RequestMapping(value = "/countSubletHouseAdventOwner", method = RequestMethod.GET)
    public CommonResult countSubletHouseAdventOwner() {
        try {
            //当前用户信息
            String userId = request.getHeader("uid");
            return subletService.countSubletHouseAdventOwner(userId);
        } catch (Exception e) {
            return new CommonResult(CommonCode.SYSTEM_ERROR);
        }
    }

    /**
     * 30.未分配房间的租客数量
     *
     * @Author：clyde
     * @Date：2025/7/22 11:01
     */
    @RequestMapping(value = "/getNeedHandleSubletRenterTodoNum", method = RequestMethod.GET)
    public CommonResult getNeedHandleSubletRenterTodoNum() {
        try {
            //当前用户信息
            String userId = request.getHeader("uid");
            return subletService.getNeedHandleSubletRenterTodoNum(userId);
        } catch (Exception e) {
            return new CommonResult(CommonCode.SYSTEM_ERROR);
        }
    }

    /**
     * 31.即将到期的租客记录数量
     *
     * @Author：clyde
     * @Date：2025/7/22 11:08
     */
    @RequestMapping(value = "/getMySubletRenterWillExpireNum", method = RequestMethod.GET)
    public CommonResult getMySubletRenterWillExpireNum() {
        try {
            //当前用户信息
            String userId = request.getHeader("uid");
            return subletService.getMySubletRenterWillExpireNum(userId);
        } catch (Exception e) {
            return new CommonResult(CommonCode.SYSTEM_ERROR);
        }
    }

    /**
     * 32.添加单个房源
     *
     * @Author：clyde
     * @Date：2025/7/22 14:18
     */
    @RecordLog(optModel = "中介二房东",optDesc = "添加单个房源")
    @RequestMapping(value = "/addSubletRoom", method = RequestMethod.POST)
    public CommonResult addSubletRoom(@RequestBody SubletHouseEntity subletHouseEntity) {
        try {
            //当前用户信息
            String userId = request.getHeader("uid");
            subletHouseEntity.setCreatorId(userId);
            return subletService.addSubletRoom(subletHouseEntity);
        } catch (Exception e) {
            return new CommonResult(CommonCode.SYSTEM_ERROR);
        }
    }

    /**
     * 33.获取我的房源
     *
     * @Author：clyde
     * @Date：2025/7/22 14:28
     */
    @RequestMapping(value = "/getManagerSubletRoom", method = RequestMethod.GET)
    public CommonResult getManagerSubletRoom(@RequestParam(defaultValue = "1") int page_num,
                                             @RequestParam(defaultValue = "10") int page_size) {
        try {
            //当前用户信息
            String userId = request.getHeader("uid");
            return subletService.getManagerSubletRoom(page_num, page_size, userId);
        } catch (Exception e) {
            return new CommonResult(CommonCode.SYSTEM_ERROR);
        }
    }


}
