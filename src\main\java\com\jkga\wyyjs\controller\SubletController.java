package com.jkga.wyyjs.controller;

import cn.hutool.json.JSONObject;
import com.jkga.wyyjs.model.CommonCode;
import com.jkga.wyyjs.model.CommonResult;
import com.jkga.wyyjs.model.configuration.WeixinConfiguration;
import com.jkga.wyyjs.utils.PermissionUtils;
import com.jkga.wyyjs.utils.WaterBase64;
import com.jkga.wyyjs.utils.WeixinUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.servlet.http.HttpServletRequest;

/**
 * @Author：clyde
 * @Date：2025/7/15 14:35
 */
@Slf4j
@CrossOrigin
@RestController
@RequestMapping("sublet")
public class SubletController {

    @Autowired
    HttpServletRequest request;
    @Autowired
    private PermissionUtils permissionUtils;
    @Autowired
    private WeixinConfiguration wxConfiguration;
    @Autowired
    private WeixinUtil weixinUtil;
    @Autowired
    private RestTemplate restTemplate;

    /**
     * 1.生成程序报送码
     *
     * @Author：clyde
     * @Date：2025/7/15 14:39
     *
     */
    @RequestMapping(value = "/generateSubletMiniProgramCodePic", method = RequestMethod.GET)
    public CommonResult generateSubletMiniProgramCodePic() {
        String userId = request.getHeader("uid");
        if(StringUtils.isBlank(userId)){
            return new CommonResult(CommonCode.NO_USER);
        }

        byte[] byteArray;
        String unlimited_url = wxConfiguration.getUnlimitedUrl();
        //获取access_token
        String access_token = weixinUtil.getAccessToken();
//        String access_token =  TokenStorage.accessToken.getAccess_token();
        //调用微信官方生成小程序码的api
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        JSONObject jsonParam = new JSONObject();
        jsonParam.put("scene", "cid=" + userId);
        jsonParam.put("page", "pages/expandWebRentalManagement/expandWebRentalManagement");
//        jsonParam.put("width", 280);
        jsonParam.put("width", 640);
        HttpEntity<String> requestEntity = new HttpEntity<>(jsonParam.toString(), headers);
        ResponseEntity<byte[]> responseEntity = restTemplate.exchange(unlimited_url + "?access_token=" + access_token,
                HttpMethod.POST, requestEntity, byte[].class);
        byteArray = responseEntity.getBody();
        String wxReturnStr = new String(byteArray);
        if (wxReturnStr.contains("errcode")) {
            return new CommonResult(500, wxReturnStr);
        }
        try {
            String base64img = WaterBase64.markImageByTextWithTip("", "出租登记码", byteArray, "", "相关人员请按规定，填报本人信息");
            return new CommonResult(base64img);
        } catch (Exception e) {
            return new CommonResult(CommonCode.SYSTEM_ERROR);
        }
    }

    /**
     * 2.获取楼幢下的房源列表
     * data_type：0：未添加到个人 1：已添加到个人 2：全部
     * @Author：clyde
     * @Date：2025/7/15 16:23
     *
     */
    @RequestMapping(value = "/getSubletRoomList", method = RequestMethod.GET)
    public CommonResult getSubletRoomList(int data_type, String building_id) {

        return new CommonResult();
    }

}
