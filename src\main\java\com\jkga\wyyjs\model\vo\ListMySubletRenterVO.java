package com.jkga.wyyjs.model.vo;

import com.jkga.wyyjs.anno.PrivacyEncrypt;
import com.jkga.wyyjs.constant.PrivacyTypeEnum;
import lombok.Data;

/**
 *
 *
 * @Author：clyde
 * @Date：2025/7/22 9:55
 *
 */

@Data
public class ListMySubletRenterVO {

    private String id;
    private String subletHouseId;
    private String userId;
    private String userName;
    @PrivacyEncrypt(type = PrivacyTypeEnum.PHONE)// 脱敏手机号
    private String userPhone;
    private String userIdentityType;
    @PrivacyEncrypt(type = PrivacyTypeEnum.ID_CARD) // 脱敏证件号
    private String userIdentity;
    private String userImgUrl;
    private Integer status;
    private String contractId;
    private Integer applyType;
    //
    private String liveStartDate;
    private String liveEndDate;
    private String remark;
    //
    private String areaId;
    private String areaLocationName;
    private String houseId;
    private String houseName;
    private String maxLiveNum;

}
