package com.jkga.wyyjs.service;

import com.jkga.wyyjs.mapper.*;
import com.jkga.wyyjs.model.CommonCode;
import com.jkga.wyyjs.model.CommonResult;
import com.jkga.wyyjs.model.entity.*;
import com.jkga.wyyjs.model.vo.*;
import com.jkga.wyyjs.utils.DateUtils;
import com.jkga.wyyjs.utils.SnowflakeUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 小区物业人员管理
 *
 * @Author：clyde
 * @Date：2025/2/7 14:27
 */
@Service
public class PropertyUserManagementService {

    @Autowired
    private RoleMapper roleMapper;
    @Autowired
    private AreaDepartmentMapper areaDepartmentMapper;
    @Autowired
    private AreaManagerMapper areaManagerMapper;
    @Autowired
    private UserRoleMapper userRoleMapper;
    @Autowired
    private PermissionMapper permissionMapper;
    @Autowired
    private RolePermissionMapper rolePermissionMapper;
    @Autowired
    private UserMapper userMapper;


    public CommonResult addAreaRole(RoleVO roleVO) {
        //同一小区下角色不能同名
        RoleEntity roleEntity = roleMapper.getAreaRoleByName(roleVO.getAreaId(), roleVO.getRoleName());
        if (roleEntity != null) {
            return new CommonResult(CommonCode.DATA_EXIST);
        } else {
            roleEntity = new RoleEntity();
            BeanUtils.copyProperties(roleVO, roleEntity);
            roleEntity.setId(SnowflakeUtil.generateId());
            roleEntity.setDeleteStatus(0);
            roleEntity.setCreateTime(DateUtils.getTime());
            roleMapper.insertAreaRole(roleEntity);
            return new CommonResult();
        }
    }

    public CommonResult addAreaDepartment(AreaDepartmentVO areaDepartmentVO) {
        //同一小区下物业部门不能同名
        AreaDepartmentEntity areaDepartmentEntity = areaDepartmentMapper.getAreaDepartmentByName(areaDepartmentVO.getAreaId(), areaDepartmentVO.getName());
        if (areaDepartmentEntity != null) {
            return new CommonResult(CommonCode.DATA_EXIST);
        } else {
            areaDepartmentEntity = new AreaDepartmentEntity();
            BeanUtils.copyProperties(areaDepartmentVO, areaDepartmentEntity);
            areaDepartmentEntity.setId(SnowflakeUtil.generateId());
            areaDepartmentEntity.setDeleteStatus(0);
            areaDepartmentMapper.insertAreaDepartment(areaDepartmentEntity);
            return new CommonResult();
        }
    }

    public CommonResult<List<RoleEntity>> getAreaRoleList(String areaId) {
        List<RoleEntity> list = roleMapper.getAreaRoleList(areaId);
        return new CommonResult<>(list);
    }

    public CommonResult<List<AreaDepartmentVO>> getAreaDepartmentList(String areaId) {
        List<AreaDepartmentEntity> list = areaDepartmentMapper.getAreaDepartmentList(areaId);
        List<AreaDepartmentVO> areaDepartmentVOList = list.stream().map(areaDepartmentEntity -> {
            AreaDepartmentVO areaDepartmentVO = new AreaDepartmentVO();
            BeanUtils.copyProperties(areaDepartmentEntity, areaDepartmentVO);
            List<AreaManagerVO> areaManagerVOList = areaManagerMapper.getAreaManagerByDepartmentId(areaDepartmentVO.getId());
            areaDepartmentVO.setAreaManagerList(areaManagerVOList);
            return areaDepartmentVO;
        }).collect(Collectors.toList());
        return new CommonResult<>(areaDepartmentVOList);
    }

    public CommonResult getAreaManagerUserRole(String id) {
        AreaManagerEntity ex = areaManagerMapper.getAreaManagerById(id);
        List<UserRoleEntity> list = userRoleMapper.getAreaManagerUserRole(ex.getAreaId(), ex.getUserId());
        AreaManagerVO areaManagerVO = new AreaManagerVO();
        BeanUtils.copyProperties(ex, areaManagerVO);
        List<String> roleIds = list.stream().map(item -> item.getRoleId()).collect(Collectors.toList());
        areaManagerVO.setRoleIds(roleIds);
        return new CommonResult(areaManagerVO);
    }

    @Transactional(rollbackFor = Exception.class)
    public CommonResult addAreaManager(AreaManagerVO areaManagerVO) {
        AreaManagerEntity areaManagerEntity = areaManagerMapper.getAreaManagerByUserId(areaManagerVO.getAreaId(), areaManagerVO.getUserId());
        //用户已经在该小区物业
        if (areaManagerEntity != null) {
            return new CommonResult(CommonCode.DATA_EXIST);
        }

        UserEntity userEntity = userMapper.selectUserById(areaManagerVO.getUserId());
        //用户不存在
        if (userEntity == null) {
            return new CommonResult(CommonCode.PARAM_ERROR);
        }

        areaManagerEntity = new AreaManagerEntity();
        BeanUtils.copyProperties(areaManagerVO, areaManagerEntity);
        areaManagerEntity.setId(SnowflakeUtil.generateId());
        areaManagerEntity.setUserName(userEntity.getName());
        areaManagerEntity.setPhone(userEntity.getPhone());
        areaManagerEntity.setIntime(DateUtils.getTime());
        areaManagerEntity.setDeleteStatus(0);
        areaManagerMapper.insertAreaManager(areaManagerEntity);

        List<String> roleIds = areaManagerVO.getRoleIds();
        if (!CollectionUtils.isEmpty(roleIds)) {
            List<UserRoleEntity> add_list = new ArrayList<>();
            roleIds.stream().forEach(e -> {
                UserRoleEntity userRoleEntity = new UserRoleEntity();
                userRoleEntity.setId(SnowflakeUtil.generateId());
                userRoleEntity.setAreaId(areaManagerVO.getAreaId());
                userRoleEntity.setUserId(areaManagerVO.getUserId());
                userRoleEntity.setRoleId(e);
                userRoleEntity.setDeleteStatus(0);
                userRoleEntity.setCreateTime(DateUtils.getTime());
                add_list.add(userRoleEntity);
            });
            userRoleMapper.batchInsertUserRole(add_list);
        }
        return new CommonResult();
    }

    @Transactional(rollbackFor = Exception.class)
    public CommonResult updAreaManager(AreaManagerVO areaManagerVO) {
        AreaManagerEntity areaManagerEntity = new AreaManagerEntity();
        BeanUtils.copyProperties(areaManagerVO, areaManagerEntity);
        areaManagerMapper.updAreaManager(areaManagerEntity);

        AreaManagerEntity ex = areaManagerMapper.getAreaManagerById(areaManagerVO.getId());
        userRoleMapper.delUserRoleByArea(ex.getAreaId(), ex.getUserId());

        List<String> roleIds = areaManagerVO.getRoleIds();
        if (!CollectionUtils.isEmpty(roleIds)) {
            List<UserRoleEntity> add_list = new ArrayList<>();
            roleIds.stream().forEach(e -> {
                UserRoleEntity userRoleEntity = new UserRoleEntity();
                userRoleEntity.setId(SnowflakeUtil.generateId());
                userRoleEntity.setAreaId(ex.getAreaId());
                userRoleEntity.setUserId(ex.getUserId());
                userRoleEntity.setRoleId(e);
                userRoleEntity.setDeleteStatus(0);
                userRoleEntity.setCreateTime(DateUtils.getTime());
                add_list.add(userRoleEntity);
            });
            userRoleMapper.batchInsertUserRole(add_list);
        }
        return new CommonResult();
    }

    public CommonResult getPermissionList() {
        return new CommonResult(permissionMapper.getPermissionList());
    }


    @Transactional(rollbackFor = Exception.class)
    public CommonResult addAreaRolePermission(RolePermissionVO rolePermissionVO) {
        List<String> list = rolePermissionVO.getPermissionIds();
        //删除角色权限
        rolePermissionMapper.delAreaRolePermission(rolePermissionVO.getRoleId());
        if (!CollectionUtils.isEmpty(list)) {
            List<RolePermissionEntity> add_list = new ArrayList<>();
            list.stream().forEach(e -> {
                RolePermissionEntity rolePermissionEntity = new RolePermissionEntity();
                rolePermissionEntity.setId(SnowflakeUtil.generateId());
                rolePermissionEntity.setRoleId(rolePermissionVO.getRoleId());
                rolePermissionEntity.setPermissionId(e);
                rolePermissionEntity.setCreateTime(DateUtils.getTime());
                rolePermissionEntity.setDeleteStatus(0);
                add_list.add(rolePermissionEntity);
            });
            //批量新增角色权限
            rolePermissionMapper.batchInsertAreaRolePermission(add_list);
        }
        return new CommonResult();
    }


    public CommonResult getRolePermissionList(String roleId) {
        return new CommonResult(rolePermissionMapper.getRolePermissionList(roleId));
    }

    @Transactional(rollbackFor = Exception.class)
    public CommonResult delAreaManager(String id) {
        AreaManagerEntity ex = areaManagerMapper.getAreaManagerById(id);
        if (ex.getDeleteStatus().intValue() == 0) {
            areaManagerMapper.delAreaManager(id);
            userRoleMapper.delUserRoleByArea(ex.getAreaId(), ex.getUserId());
            return new CommonResult();
        } else {
            return new CommonResult(-1, "人员已被删除");
        }

    }

    public CommonResult getManagerAreas(String userId) {
        return new CommonResult(areaManagerMapper.getManagerAreas(userId));
    }

    public CommonResult getManagerPermissions(String userId, String area_id) {
        return new CommonResult(userMapper.getUserPermissionDetails(userId, area_id));
    }


}
