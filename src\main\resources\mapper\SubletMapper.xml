<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jkga.wyyjs.mapper.SubletMapper">

    <select id="getSubletHouseByBuildingId" resultType="com.jkga.wyyjs.model.vo.SubletHouseVO">
        select * from (select h.*,
        CASE WHEN sb."id" IS NULL THEN 0
        WHEN sb."id" IS NOT NULL THEN 1
        END AS "is_add",
        sb."max_live_num"
        from "house" h
        left join "sublet_house" sb on h."id" = sb."house_id" and sb."creator_id" = #{userId} and sb."is_deleted" = 0
        where h."parent_id" like concat(#{buildingId},'%') and h."delete_status" = 0)
        temp
        <where>
            <choose>
                <when test="dataType == 0">
                    temp."is_add" = 0
                </when>
                <when test="dataType == 1">
                    temp."is_add" = 1
                </when>
            </choose>
        </where>
    </select>


    <insert id="insertSubletRenter" parameterType="com.jkga.wyyjs.model.vo.SubletRenterRegisterVO">
        insert into "sublet_renter"(
            "id",
            "user_id",
            "user_name",
            "user_phone",
            "user_identity_type",
            "user_identity",
            "user_img_url",
            "status",
            "creator_id",
            "create_time",
            "apply_type"
        )values(
            #{id},
            #{userId},
            #{userName,typeHandler=com.jkga.wyyjs.component.SM4TypeHandler},
            #{userPhone,typeHandler=com.jkga.wyyjs.component.SM4TypeHandler},
            #{userIdentityType},
            #{userIdentity,typeHandler=com.jkga.wyyjs.component.SM4TypeHandler},
            #{userImgUrl},
            0,
            #{creatorId},
            NOW(),
            #{applyType}
        )
    </insert>

    <resultMap id="listNeedHandleSubletRenterMap" type="com.jkga.wyyjs.model.vo.ListNeedHandleSubletRenterVO">
        <result column="user_name" jdbcType="VARCHAR" property="userName"
                typeHandler="com.jkga.wyyjs.component.SM4TypeHandler"/>
        <result column="user_phone" jdbcType="VARCHAR" property="userPhone"
                typeHandler="com.jkga.wyyjs.component.SM4TypeHandler"/>
        <result column="user_identity" jdbcType="VARCHAR" property="userIdentity"
                typeHandler="com.jkga.wyyjs.component.SM4TypeHandler"/>
    </resultMap>

    <select id="listNeedHandleSubletRenter" resultMap="listNeedHandleSubletRenterMap">
        SELECT
        "id",
        "user_id",
        "user_name",
        "user_phone",
        "user_identity_type",
        "user_identity",
        "user_img_url",
        "status",
        "creator_id",
        "create_time",
        "apply_type"
        FROM
        "sublet_renter"
        WHERE
        "creator_id" = #{adminId}
        AND "status" = 0
        <if test="searchParam != null and searchParam != ''">
            AND "user_name" = #{searchParam,typeHandler=com.jkga.wyyjs.component.SM4TypeHandler}
        </if>
        ORDER BY
        "create_time" DESC
        <if test="pageSize != 0">
            LIMIT #{currIndex},#{pageSize}
        </if>
    </select>

    <select id="countNeedHandleSubletRenter" resultType="java.lang.Integer">
        SELECT
        COUNT(1)
        FROM
        "sublet_renter"
        WHERE
        "creator_id" = #{adminId}
        AND "status" = 0
        <if test="searchParam != null and searchParam != ''">
            AND "user_name" = #{searchParam,typeHandler=com.jkga.wyyjs.component.SM4TypeHandler}
        </if>
    </select>

    <select id="listMySubletCommunity" resultType="java.util.Map">
        SELECT DISTINCT
            "area_id",
            "area_location_name"
        FROM
            "sublet_house"
        WHERE
            "creator_id" = #{adminId}
            AND "is_deleted" = 0
    </select>

    <select id="listMySubletRoomByCommunity" resultType="java.util.Map">
        SELECT * FROM(
        SELECT
        h."id",
        h."area_id",
        h."area_location_name",
        h."house_id",
        h."house_name",
        h."max_live_num",
        count( r."id" ) AS "now_live_num"
        FROM
        "sublet_house" h
        LEFT JOIN "sublet_renter" r ON h."id" = r."sublet_house_id"
        AND r."status" = 1
        WHERE
        h."is_deleted" = 0
        and h."creator_id" = #{adminId}
        AND h."area_id" = #{areaId}
        <if test="searchParam != null and searchParam != ''">
            AND h."house_name" LIKE CONCAT('%',#{searchParam},'%')
        </if>
        GROUP BY
        h."id",
        h."area_id",
        h."area_location_name",
        h."house_id",
        h."house_name",
        h."max_live_num"
        ) temp
        <where>
            <choose>
                <when test="dataType == 1">
                    "now_live_num" = 0
                </when>
                <when test="dataType == 2">
                    "now_live_num" &gt; 0
                </when>
            </choose>
        </where>
    </select>

    <resultMap id="getSubletRenterByIdMap" type="com.jkga.wyyjs.model.vo.SubletRenterVO">
        <result column="user_name" jdbcType="VARCHAR" property="userName"
                typeHandler="com.jkga.wyyjs.component.SM4TypeHandler"/>
        <result column="user_phone" jdbcType="VARCHAR" property="userPhone"
                typeHandler="com.jkga.wyyjs.component.SM4TypeHandler"/>
        <result column="user_identity" jdbcType="VARCHAR" property="userIdentity"
                typeHandler="com.jkga.wyyjs.component.SM4TypeHandler"/>
    </resultMap>

    <select id="getSubletRenterById" resultMap="getSubletRenterByIdMap">
        select * from "sublet_renter" where "id" = #{id}
    </select>

    <select id="countSubletRenterInHouseNum" resultType="java.lang.Integer">
        SELECT
        count(1)
        FROM
        "sublet_renter"
        WHERE
        "sublet_house_id" = #{subletHouseId}
        AND "creator_id" = #{adminId}
        AND "status" = 1
        AND "user_identity" IN
        <foreach collection="identityList" item="item" open="(" close=")" separator=",">
            #{item,typeHandler=com.jkga.wyyjs.component.SM4TypeHandler}
        </foreach>
    </select>

    <resultMap id="getSubletHouseByIdMap" type="com.jkga.wyyjs.model.entity.SubletHouseEntity">
        <result column="owner_name" jdbcType="VARCHAR" property="ownerName"
                typeHandler="com.jkga.wyyjs.component.SM4TypeHandler"/>
        <result column="owner_phone" jdbcType="VARCHAR" property="ownerPhone"
                typeHandler="com.jkga.wyyjs.component.SM4TypeHandler"/>
        <result column="owner_identity" jdbcType="VARCHAR" property="ownerIdentity"
                typeHandler="com.jkga.wyyjs.component.SM4TypeHandler"/>
    </resultMap>

    <select id="getSubletHouseById" resultMap="getSubletHouseByIdMap">
        SELECT * FROM "sublet_house" where "id" = #{subletHouseId}
    </select>

    <select id="countSubletRenterHouseAlreadyLiveNum" resultType="java.lang.Integer">
        SELECT
            count(1)
        FROM
            "sublet_renter"
        WHERE
            "sublet_house_id" = #{subletHouseId}
            AND "creator_id" = #{adminId}
            AND "status" = 1
    </select>

    <insert id="insertSubletContract" parameterType="com.jkga.wyyjs.model.entity.SubletContractEntity">
        insert into "sublet_contract"(
            "id",
            "live_start_date",
            "live_end_date",
            "remark",
            "type",
            "creator_id",
            "create_time"
        )values(
            #{id},
            #{liveStartDate},
            #{liveEndDate},
            #{remark},
            #{type},
            #{creatorId},
            NOW()
        )
    </insert>

    <update id="handleMainRenterRoom">
        update "sublet_renter" set
            "sublet_house_id" = #{subletHouseId},
            "status" = 1,
            "contract_id" = #{contractId},
            "operator_id" = #{operatorId},
            "operate_time" = NOW()
        where "id" = #{renterId}
    </update>

    <insert id="batchInsertSubletRenterFull">
        insert into "sublet_renter"(
        "id",
        "sublet_house_id",
        "user_id",
        "user_name",
        "user_phone",
        "user_identity_type",
        "user_identity",
        "user_img_url",
        "status",
        "contract_id",
        "creator_id",
        "create_time",
        "operator_id",
        "operate_time",
        "apply_type"
        )values
        <foreach collection="dataList" item="item" separator=",">
            (
            #{item.id},
            #{item.subletHouseId},
            #{item.userId},
            #{item.userName,typeHandler=com.jkga.wyyjs.component.SM4TypeHandler},
            #{item.userPhone,typeHandler=com.jkga.wyyjs.component.SM4TypeHandler},
            #{item.userIdentityType},
            #{item.userIdentity,typeHandler=com.jkga.wyyjs.component.SM4TypeHandler},
            #{item.userImgUrl},
            #{item.status},
            #{item.contractId},
            #{item.creatorId},
            NOW(),
            #{item.operatorId},
            NOW(),
            #{item.applyType}
            )
        </foreach>
    </insert>

    <insert id="batchInsertSubletContractRelation">
        insert into "sublet_contract_relation"(
        "id",
        "contract_id",
        "file_url"
        )values
        <foreach collection="dataList" item="item" separator=",">
            (
            #{item.id},
            #{item.contractId},
            #{item.fileUrl}
            )
        </foreach>
    </insert>

    <update id="cancelSubletRenterRegister">
        update "sublet_renter" set "status" = 3,"operator_id" = #{adminId},"operate_time" = NOW() where "id" = #{renterId}
    </update>

    <resultMap id="listMySubletRenterMap" type="com.jkga.wyyjs.model.vo.ListMySubletRenterVO">
        <result column="user_name" jdbcType="VARCHAR" property="userName"
                typeHandler="com.jkga.wyyjs.component.SM4TypeHandler"/>
        <result column="user_phone" jdbcType="VARCHAR" property="userPhone"
                typeHandler="com.jkga.wyyjs.component.SM4TypeHandler"/>
        <result column="user_identity" jdbcType="VARCHAR" property="userIdentity"
                typeHandler="com.jkga.wyyjs.component.SM4TypeHandler"/>
    </resultMap>

    <select id="listMySubletRenter" resultMap="listMySubletRenterMap">
        SELECT
        r."id",
        r."sublet_house_id",
        r."user_id",
        r."user_name",
        r."user_phone",
        r."user_identity_type",
        r."user_identity",
        r."user_img_url",
        r."status",
        r."contract_id",
        r."apply_type",
        c."live_start_date",
        c."live_end_date",
        c."remark",
        h."area_id",
        h."area_location_name",
        h."house_id",
        h."house_name",
        h."max_live_num"
        FROM
        "sublet_renter" r
        JOIN "sublet_contract" c ON r."contract_id" = c."id"
        JOIN "sublet_house" h ON r."sublet_house_id" = h."id" AND h."is_deleted" = 0
        WHERE
        r."creator_id" = #{adminId}
        <if test="searchParam != null and searchParam != ''">
            AND r."user_name" = #{searchParam,typeHandler=com.jkga.wyyjs.component.SM4TypeHandler}
        </if>
        <choose>
            <when test="dataType == 0">
                AND
                (
                TIMESTAMPDIFF(DAY,DATE_FORMAT(NOW(),'%Y-%m-%d'),c."live_end_date" ) &gt;= 0
                AND
                TIMESTAMPDIFF(DAY,DATE_FORMAT(NOW(),'%Y-%m-%d'),c."live_end_date" ) &lt;= 30
                AND
                r."status" = 1
                )
            </when>
            <when test="dataType == 1">
                AND
                (
                r."status" = 2
                OR
                (r."status" = 1 AND c."live_end_date" &lt; DATE_FORMAT(NOW(),'%Y-%m-%d'))
                )
            </when>
        </choose>
        <if test="pageSize != 0">
            LIMIT #{currIndex},#{pageSize}
        </if>
    </select>

    <select id="countMySubletRenter" resultType="java.lang.Integer">
        SELECT
        COUNT(1)
        FROM
        "sublet_renter" r
        JOIN "sublet_contract" c ON r."contract_id" = c."id"
        JOIN "sublet_house" h ON r."sublet_house_id" = h."id" AND h."is_deleted" = 0
        WHERE
        r."creator_id" = #{adminId}
        <if test="searchParam != null and searchParam != ''">
            AND r."user_name" = #{searchParam,typeHandler=com.jkga.wyyjs.component.SM4TypeHandler}
        </if>
        <choose>
            <when test="dataType == 0">
                AND
                (
                TIMESTAMPDIFF(DAY,DATE_FORMAT(NOW(),'%Y-%m-%d'),c."live_end_date" ) &gt;= 0
                AND
                TIMESTAMPDIFF(DAY,DATE_FORMAT(NOW(),'%Y-%m-%d'),c."live_end_date" ) &lt;= 30
                AND
                r."status" = 1
                )
            </when>
            <when test="dataType == 1">
                AND
                (
                r."status" = 2
                OR
                (r."status" = 1 AND c."live_end_date" &lt; DATE_FORMAT(NOW(),'%Y-%m-%d'))
                )
            </when>
        </choose>
    </select>

    <update id="leaveSubletRenter">
        update "sublet_renter" set "status" = 2,"operator_id" = #{adminId},"operate_time" = NOW() where "id" = #{renterId}
    </update>

    <select id="continueSubletRenter">
        update "sublet_renter" set
            "status" = 1,
            "contract_id" = #{contractId},
            "operator_id" = #{adminId},
            "operate_time" = NOW()
        where "id" = #{renterId}
    </select>


    <select id="getHouseSubletHouseCountByArea" resultType="map">
        select h."area_id",h."area_location_name",count(h."house_id") "num" from "sublet_house" h left join (SELECT
        r."sublet_house_id",count(1) "renter_num"
        FROM
        "sublet_renter" r
        WHERE
        r."creator_id" = #{user_id}
        AND r."status" = 1
        GROUP BY
        r."sublet_house_id") x on h."id" = x."sublet_house_id"
        where h."creator_id" = #{user_id} and h."is_deleted" = 0 and x."renter_num" > 0
        GROUP BY h."area_id",h."area_location_name"
    </select>

    <select id="getHouseSubletRenterCountByArea" resultType="map">
        SELECT
        h."area_id",h."area_location_name",count(r."id") "renter_num"
        FROM
        "sublet_house" h
        LEFT JOIN "sublet_renter" r ON h."id" = r."sublet_house_id"
        WHERE
        h."creator_id" = #{user_id} AND h."is_deleted" = 0 AND r."status" = 1
        GROUP BY h."area_id",h."area_location_name"
    </select>

    <select id="getUnUsedHouseSubletHouseCountByArea" resultType="map">
        select h."area_id",h."area_location_name",count(h."house_id") "num" from "sublet_house" h left join (SELECT
        r."sublet_house_id",count(1) "renter_num"
        FROM
        "sublet_renter" r
        WHERE
        r."creator_id" = #{user_id}
        AND r."status" = 1
        GROUP BY
        r."sublet_house_id") x on h."id" = x."sublet_house_id"
        where h."creator_id" = #{user_id} and h."is_deleted" = 0 and x."sublet_house_id" is null
        GROUP BY h."area_id",h."area_location_name"
    </select>

    <select id="getHouseSubletRenterList" resultMap="listMySubletRenterMap">
        SELECT
        r."id",
        r."sublet_house_id",
        r."user_id",
        r."user_name",
        r."user_phone",
        r."user_identity_type",
        r."user_identity",
        r."user_img_url",
        r."status",
        r."contract_id",
        r."apply_type",
        c."live_start_date",
        c."live_end_date",
        c."remark",
        h."area_id",
        h."area_location_name",
        h."house_id",
        h."house_name",
        h."max_live_num"
        FROM "sublet_renter" r
        JOIN "sublet_contract" c ON r."contract_id" = c."id"
        JOIN "sublet_house" h ON r."sublet_house_id" = h."id"
        where r."sublet_house_id" = #{sublet_house_id} and r."creator_id" = #{user_id} and r."status" = 1
        <if test="searchParam != null and searchParam != ''">
            AND r."user_name" = #{searchParam,typeHandler=com.jkga.wyyjs.component.SM4TypeHandler}
        </if>
        <if test="pageSize != 0">
            LIMIT #{currIndex},#{pageSize}
        </if>
    </select>

    <select id="countHouseSubletRenter" resultType="int">
        SELECT count(1) FROM "sublet_renter" r where r."sublet_house_id" = #{sublet_house_id} and r."creator_id" =
        #{user_id} and r."status" = 1
        <if test="searchParam != null and searchParam != ''">
            AND r."user_name" = #{searchParam,typeHandler=com.jkga.wyyjs.component.SM4TypeHandler}
        </if>
    </select>

    <select id="getHouseSubletContractImgList" resultType="map">
        select * from "sublet_contract_relation" where "contract_id" = #{contract_id}
    </select>

    <select id="getHouseSubletRenterNum" resultType="int">
        select count(1) from "sublet_renter" where "sublet_house_id" = #{sublet_house_id} and "status" in (0,1)
    </select>

    <update id="delSubletUnusedRoom">
        UPDATE "sublet_house"
        SET
         "is_deleted" = 1
        WHERE
            "id" = #{sublet_house_id}
    </update>

    <select id="getSubletRenterNum" resultType="int">
        <!--去掉合同时间判断
            select count(1) from `sublet_renter` r left join sublet_contract c on r.contract_id = c.id
            where r.creator_id =#{user_id} and r.`status` = 1 and CURDATE() between c.live_start_date and c.live_end_date
        -->
        select count(1) from "sublet_renter" r where r."creator_id" =#{user_id} and r."status" = 1
    </select>

    <select id="getSubletOwnerNum" resultType="int">
        SELECT count(1) FROM "sublet_house" where "creator_id" = #{user_id} and "is_deleted" = 0 and "owner_identity" is not null
    </select>

    <select id="getVacantHouseNum" resultType="int">
        <!--去掉合同时间判断
        select count(1) from sublet_house h left join (select r.house_id,count(1) num from `sublet_renter` r left join sublet_contract c on r.contract_id = c.id
        where r.creator_id = #{user_id} and r.`status` = 1 and CURDATE() between c.live_start_date and c.live_end_date
        GROUP BY r.house_id) x on h.id = x.house_id where h.creator_id = #{user_id} and h.is_deleted = 0 and x.num > 0
        -->
        select count(1) from "sublet_house" h left join (select r."sublet_house_id",count(1) "renter_num" from
        "sublet_renter" r
        where r."creator_id" = #{user_id} and r."status" = 1 GROUP BY r."sublet_house_id") x on h."id" =
        x."sublet_house_id"
        where h."creator_id" = #{user_id} and h."is_deleted" = 0 and x."sublet_house_id" is null
    </select>

    <select id="countMyTotalSubletHouse" resultType="int">
        SELECT count(1) FROM "sublet_house" where "creator_id" = #{user_id} and "is_deleted" = 0
    </select>

    <update id="updateSubletHouseOwner">
        update "sublet_house" set
            "owner_user_id" = #{ownerUserId},
            "owner_name" = #{ownerName,typeHandler=com.jkga.wyyjs.component.SM4TypeHandler},
            "owner_phone" = #{ownerPhone,typeHandler=com.jkga.wyyjs.component.SM4TypeHandler},
            "owner_identity_type" = #{ownerIdentityType},
            "owner_identity" = #{ownerIdentity,typeHandler=com.jkga.wyyjs.component.SM4TypeHandler},
            "owner_img_url" = #{ownerImgUrl},
            "contract_id" = #{contractId},
            "operator_id" = #{operatorId},
            "operate_time" = NOW()
        where "id" = #{subletHouseId}
    </update>

    <resultMap id="listBindSubletHouseOwnerMap" type="com.jkga.wyyjs.model.vo.ListBindSubletHouseOwnerVO">
        <result column="owner_name" jdbcType="VARCHAR" property="ownerName"
                typeHandler="com.jkga.wyyjs.component.SM4TypeHandler"/>
        <result column="owner_phone" jdbcType="VARCHAR" property="ownerPhone"
                typeHandler="com.jkga.wyyjs.component.SM4TypeHandler"/>
        <result column="owner_identity" jdbcType="VARCHAR" property="ownerIdentity"
                typeHandler="com.jkga.wyyjs.component.SM4TypeHandler"/>
    </resultMap>

    <select id="listBindSubletHouseOwner" resultMap="listBindSubletHouseOwnerMap">
        SELECT
        h.*,
        c."live_start_date",
        c."live_end_date",
        c."remark",
        c."type"
        FROM
        "sublet_house" h
        JOIN "sublet_contract" c ON h."contract_id" = c."id"
        WHERE
        h."is_deleted" = 0
        AND h."creator_id" = #{adminId}
        AND h."owner_identity" IS NOT NULL
        <if test="searchParam != null and searchParam != ''">
            AND h."owner_name" = #{searchParam,typeHandler=com.jkga.wyyjs.component.SM4TypeHandler}
        </if>
        <choose>
            <when test="dataType == 1">
                AND
                (
                TIMESTAMPDIFF(DAY,DATE_FORMAT(NOW(),'%Y-%m-%d'),c."live_end_date" ) &gt;= 0
                AND
                TIMESTAMPDIFF(DAY,DATE_FORMAT(NOW(),'%Y-%m-%d'),c."live_end_date" ) &lt;= 30
                )
            </when>
        </choose>
        ORDER BY
        h."operate_time"
        <if test="pageSize != 0">
            LIMIT #{currIndex},#{pageSize}
        </if>
    </select>

    <select id="countBindSubletHouseOwner" resultType="java.lang.Integer">
        SELECT
        COUNT(1)
        FROM
        "sublet_house" h
        JOIN "sublet_contract" c ON h."contract_id" = c."id"
        WHERE
        h."is_deleted" = 0
        AND h."creator_id" = #{adminId}
        AND h."owner_identity" IS NOT NULL
        <if test="searchParam != null and searchParam != ''">
            AND h."owner_name" = #{searchParam,typeHandler=com.jkga.wyyjs.component.SM4TypeHandler}
        </if>
        <choose>
            <when test="dataType == 1">
                AND
                (
                TIMESTAMPDIFF(DAY,DATE_FORMAT(NOW(),'%Y-%m-%d'),c."live_end_date" ) &gt;= 0
                AND
                TIMESTAMPDIFF(DAY,DATE_FORMAT(NOW(),'%Y-%m-%d'),c."live_end_date" ) &lt;= 30
                )
            </when>
        </choose>
    </select>

    <update id="leaveSubletHouseOwner">
        update "sublet_house" set
            "operator_id" = #{adminId},
            "operate_time" = NOW(),
            "is_deleted" = 1
        where "id" = #{subletHouseId}
    </update>

    <update id="continueSubletHouseOwner">
        update "sublet_house" set
            "is_deleted" = 0,
            "contract_id" = #{contractId},
            "operator_id" = #{operatorId},
            "operate_time" = NOW()
        where "id" = #{subletHouseId}
    </update>

    <select id="getNoBindOwnerSubletCommunityList" resultType="java.util.Map">
        SELECT
            "area_id",
            "area_location_name",
            COUNT(1) AS "room_num"
        FROM
            "sublet_house"
        WHERE
            "is_deleted" = 0
            AND "owner_identity" IS NULL
            AND "creator_id" = #{adminId}
        GROUP BY
            "area_id",
            "area_location_name"
    </select>

    <select id="listNoBindOwnerSubletHouseListByCommunity" resultType="com.jkga.wyyjs.model.entity.SubletHouseEntity">
        SELECT
        h.*
        FROM "sublet_house" h where h."is_deleted" = 0 and h."creator_id" = #{adminId} and h."owner_identity" is null
        and h."area_id" = #{areaId}
        <if test="searchParam != null and searchParam != ''">
            AND h."house_name" LIKE CONCAT('%',#{searchParam},'%')
        </if>
    </select>

    <select id="countNoOwnerSubletHouse" resultType="int">
        SELECT count(1) FROM "sublet_house" where "creator_id" = #{user_id} and "is_deleted" = 0 and "owner_identity" is null
    </select>

    <select id="countManagerSubletHouse" resultType="int">
        select count(1) from "sublet_house" h where h."creator_id" = #{adminId} and h."is_deleted" = 0
    </select>

    <select id="getManagerSubletHouseList" resultMap="getSubletHouseByIdMap">
        select h.* from "sublet_house" h where h."creator_id" = #{adminId} and h."is_deleted" = 0 order by
        h."create_time" desc
        <if test="pageSize != 0">
            LIMIT #{currIndex},#{pageSize}
        </if>
    </select>

    <select id="getSubletHouseByHouseId" resultType="com.jkga.wyyjs.model.entity.SubletHouseEntity">
        select h.* from "sublet_house" h where h."house_id" = #{houseId} and h."creator_id" = #{userId} limit 1
    </select>

    <insert id="insertSubletRoom" parameterType="com.jkga.wyyjs.model.entity.SubletHouseEntity">
        insert into "sublet_house"(
        "id",
        "area_id",
        "area_location_name",
        "house_id",
        "house_name",
        "max_live_num",
        "creator_id",
        "create_time",
        "is_deleted"
        ) values(
            #{id},
            #{areaId},
            #{areaLocationName},
            #{houseId},
            #{houseName},
            #{maxLiveNum},
            #{creatorId},
            NOW(),
            0
        )
    </insert>

    <update id="updSubletRoom" parameterType="com.jkga.wyyjs.model.entity.SubletHouseEntity">
            update "sublet_house" set
            "is_deleted" = 0,
            "max_live_num" = #{maxLiveNum}
        where "id" = #{id}
    </update>

    <update id="batchUpdateSubletRenterLeaveStatus">
        UPDATE "sublet_renter" r
        JOIN "sublet_contract" c ON r."contract_id" = c."id"
        SET r."status" = 2
        WHERE
            DATE_FORMAT( now(), '%Y-%m-%d' ) > c."live_end_date"
            AND r."status" = 1
    </update>

    <select id="getExpireSubletHouseOwnerList" resultType="com.jkga.wyyjs.model.entity.SubletHouseEntity">
        SELECT
            h.*
        FROM
            "sublet_house" h
            JOIN "sublet_contract" c ON h."contract_id" = c."id"
            AND h."is_deleted" = 0
        WHERE
            DATE_FORMAT( now(), '%Y-%m-%d' ) &gt; c."live_end_date"
            AND h."owner_identity" IS NOT NULL
    </select>

    <update id="unbindExpireSubletHouseOwner">
        update "sublet_house" set
        "owner_user_id" = NULL,
        "owner_name" = NULL,
        "owner_phone" = NULL,
        "owner_identity_type" = NULL,
        "owner_identity" = NULL,
        "owner_img_url" = NULL,
        "contract_id" = NULL
        where "id" in
        <foreach collection="dataList" item="item" open="(" close=")" separator=",">
            #{item.id}
        </foreach>
    </update>

    <insert id="batchInsertExpireSubletHouseOwnerToHistory">
        insert into "sublet_house_history"(
        "sublet_house_id",
        "area_id",
        "area_location_name",
        "house_id",
        "house_name",
        "max_live_num",
        "owner_user_id",
        "owner_name",
        "owner_phone",
        "owner_identity_type",
        "owner_identity",
        "owner_img_url",
        "contract_id",
        "creator_id",
        "create_time",
        "operator_id",
        "operate_time",
        "is_deleted",
        "insert_time"
        )values
        <foreach collection="dataList" item="item" separator=",">
            (
            #{item.id},
            #{item.areaId},
            #{item.areaLocationName},
            #{item.houseId},
            #{item.houseName},
            #{item.maxLiveNum},
            #{item.ownerUserId},
            #{item.ownerName},
            #{item.ownerPhone},
            #{item.ownerIdentityType},
            #{item.ownerIdentity},
            #{item.ownerImgUrl},
            #{item.contractId},
            #{item.creatorId},
            #{item.createTime},
            #{item.operatorId},
            #{item.operateIime},
            #{item.isDeleted},
            NOW()
            )
        </foreach>
    </insert>


</mapper>