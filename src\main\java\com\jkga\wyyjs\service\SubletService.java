package com.jkga.wyyjs.service;

import com.jkga.wyyjs.constant.TbTypeEnum;
import com.jkga.wyyjs.mapper.SubletMapper;
import com.jkga.wyyjs.mapper.UserMapper;
import com.jkga.wyyjs.model.CommonCode;
import com.jkga.wyyjs.model.CommonListResult;
import com.jkga.wyyjs.model.CommonResult;
import com.jkga.wyyjs.model.dto.AreaCarMqDTO;
import com.jkga.wyyjs.model.dto.FileSaveReturnDTO;
import com.jkga.wyyjs.model.entity.SubletContractEntity;
import com.jkga.wyyjs.model.entity.SubletContractRelationEntity;
import com.jkga.wyyjs.model.entity.SubletHouseEntity;
import com.jkga.wyyjs.model.entity.UserEntity;
import com.jkga.wyyjs.model.vo.*;
import com.jkga.wyyjs.utils.SnowflakeUtil;
import com.jkga.wyyjs.utils.ThirdService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.security.NoSuchAlgorithmException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author：clyde
 * @Date：2025/7/15 17:21
 */
@Slf4j
@Service
public class SubletService {

    @Autowired
    private SubletMapper subletMapper;
    @Autowired
    private UserMapper userMapper;

    @Autowired
    private FileService fileService;

    @Autowired
    private ThirdService thirdService;

    public CommonResult getSubletRoomList(String buildingId, int dataType, String userId) {

        List<SubletHouseVO> list = subletMapper.getSubletHouseByBuildingId(buildingId, dataType, userId);
        List<SubletHouseVO> dy_list = list.stream().filter(subletHouseVO -> subletHouseVO.getHouseType() == 1).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(dy_list)) {
            dy_list.forEach(e -> {
                createHouseTree(e, list);
            });
            return new CommonResult(dy_list);
        } else {
            return new CommonResult(list);
        }
    }

    public CommonResult batchAddSubletRoom(List<SubletHouseEntity> listVO, String userId) {
        listVO.forEach(e -> {
            SubletHouseEntity ex = subletMapper.getSubletHouseByHouseId(e.getHouseId(), userId);
            e.setCreatorId(userId);
            if (ex != null) {
                e.setId(ex.getId());
                subletMapper.updSubletRoom(e);
            } else {
                e.setId(SnowflakeUtil.generateId());
                subletMapper.insertSubletRoom(e);
            }
        });

        return new CommonResult();
    }

    public CommonResult registerSubletRenter(SubletRenterRegisterVO registerVO, String userId) throws IOException, NoSuchAlgorithmException {
        //申报类型
        int applyType = registerVO.getApplyType();
        if (applyType == 0) {//申报类型 0：自己申报 1：代申报
            UserEntity userEntity = userMapper.selectUserById(userId);
            if (userEntity == null || StringUtils.isBlank(userEntity.getIdentity())) {
                return CommonResult.fail_msg("用户不存在或者未实名");
            }
            //租客信息从token取
            registerVO.setUserId(userEntity.getId());
            registerVO.setUserName(userEntity.getName());
            registerVO.setUserPhone(userEntity.getPhone());
            registerVO.setUserIdentityType(userEntity.getIdentityType());
            registerVO.setUserIdentity(userEntity.getIdentity());
            registerVO.setUserImgUrl(userEntity.getFaceImgUrl());
        }

        registerVO.setId(SnowflakeUtil.generateId());
        String faceImgUrl = registerVO.getUserImgUrl();
        //如果前端没传人脸图片url，则保存传过来的人脸图片文件
        if (StringUtils.isBlank(faceImgUrl)) {
            MultipartFile faceImgFile = registerVO.getFaceImgFile();
            if (faceImgFile != null) {
                List<MultipartFile> fileList = Arrays.asList(faceImgFile);
                String tbType = TbTypeEnum.SUBLETRENTER.getValue();
                //这里modulePath（模块文件夹名）直接使用了tbType，也可以自定义该模块的文件夹名
                List<FileSaveReturnDTO> fileSaveReturnList = fileService.saveFiles(userId, fileList, registerVO.getId(), tbType, tbType);
                if (fileSaveReturnList.size() > 0) {
                    registerVO.setUserImgUrl(fileSaveReturnList.get(0).getUrl());
                } else {
                    return CommonResult.fail_msg("文件保存失败");
                }
            } else {
                return new CommonResult(CommonCode.PARAM_ERROR);
            }
        }
        subletMapper.insertSubletRenter(registerVO);
        return new CommonResult();
    }

    public CommonResult getNeedHandleSubletRenterList(int pageNum, int pageSize, String searchParam, String userId) {
        //计算数据开始索引
        int currIndex = (pageNum - 1) * pageSize;

        List<ListNeedHandleSubletRenterVO> dataList = subletMapper.listNeedHandleSubletRenter(currIndex, pageSize, searchParam, userId);
        //总记录数
        int totalNum = subletMapper.countNeedHandleSubletRenter(searchParam, userId);
        //总页数
        int totalPageNum = (totalNum + pageSize - 1) / pageSize;

        return new CommonResult(new CommonListResult<>(totalNum, totalPageNum, dataList));
    }

    public CommonResult getMySubletCommunityList(String adminId) {
        return new CommonResult(subletMapper.listMySubletCommunity(adminId));
    }

    public CommonResult getMySubletRoomListByCommunity(String areaId, int dataType, String searchParam, String adminId) {
        return new CommonResult(subletMapper.listMySubletRoomByCommunity(areaId, adminId, searchParam, dataType));
    }

    @Transactional(rollbackFor = Exception.class)
    public CommonResult handleSubletRoomToRenter(HandleSubletRoomToRenterVO handleVO) throws IOException, NoSuchAlgorithmException {
        MultipartFile[] files = handleVO.getFiles();
        if (files.length == 0) {
            return CommonResult.fail_msg("合同文件不能为空");
        }
        //===============判断人员已在和人员已满===============
        //新增租客
        List<SubletRenterRegisterVO> newRenterList = handleVO.getNewRenterList();
        //原登记租客
        SubletRenterVO renterVO = subletMapper.getSubletRenterById(handleVO.getRenterId());
        if (renterVO == null) {
            return CommonResult.fail_msg("找不到客源记录");
        }

        //所有租客身份证列表
        List<String> identityList = new ArrayList<>();
        identityList.add(renterVO.getUserIdentity());
        if (newRenterList != null && newRenterList.size() > 0) {
            List<String> newRenterIdentityList = newRenterList.stream().map(SubletRenterRegisterVO::getUserIdentity).collect(Collectors.toList());
            identityList.addAll(newRenterIdentityList);
        }
        //判断人员已在房间
        int renterInHouseNum = subletMapper.countSubletRenterInHouseNum(identityList, handleVO.getSubletHouseId(), handleVO.getOperatorId());
        if (renterInHouseNum > 0) {
            return CommonResult.fail_msg("有人员已在该房间，请勿重复分配");
        }
        //>>>>>判断房间人员已满<<<<<
        SubletHouseEntity subletHouseEntity = subletMapper.getSubletHouseById(handleVO.getSubletHouseId());
        //最大居住人数
        int maxLiveNum = subletHouseEntity.getMaxLiveNum();
        //已经入住的人数
        int alreadyLiveNum = subletMapper.countSubletRenterHouseAlreadyLiveNum(handleVO.getSubletHouseId(), handleVO.getOperatorId());
        //将要添加的人数
        int addRenterNum = 1 + (newRenterList == null ? 0 : newRenterList.size());
        if (alreadyLiveNum + addRenterNum > maxLiveNum) {
            return CommonResult.fail_msg("该房间可分配人员数量不足：" + "可住人数：" + maxLiveNum + " 已住人数：" + alreadyLiveNum + " 分配人数：" + addRenterNum);
        }

        //===============新增合同信息===============
        SubletContractEntity subletContractEntity = new SubletContractEntity();
        subletContractEntity.setId(SnowflakeUtil.generateId());
        subletContractEntity.setLiveStartDate(handleVO.getLiveStartDate());
        subletContractEntity.setLiveEndDate(handleVO.getLiveEndDate());
        subletContractEntity.setRemark(handleVO.getRemark());
        subletContractEntity.setType(1);
//        contractDO.setStatus(0);
        subletContractEntity.setCreatorId(handleVO.getOperatorId());
        subletMapper.insertSubletContract(subletContractEntity);

        //合同和文件关系列表
        List<SubletContractRelationEntity> contractFileRelationList = new ArrayList<>();

        //===============更新主要租客信息===============
        handleVO.setContractId(subletContractEntity.getId());
        subletMapper.handleMainRenterRoom(handleVO);

        //===============批量添加新租客===============
        if (newRenterList != null && newRenterList.size() > 0) {
            for (SubletRenterRegisterVO newRenterVO : newRenterList) {
                newRenterVO.setId(SnowflakeUtil.generateId());
                newRenterVO.setSubletHouseId(handleVO.getSubletHouseId());
                newRenterVO.setStatus(1);
                newRenterVO.setContractId(subletContractEntity.getId());
                newRenterVO.setCreatorId(handleVO.getOperatorId());
                newRenterVO.setOperatorId(handleVO.getOperatorId());
                newRenterVO.setApplyType(1);
                String newRenterImgUrl = newRenterVO.getUserImgUrl();
                if (StringUtils.isBlank(newRenterImgUrl)) {
                    MultipartFile faceImgFile = newRenterVO.getFaceImgFile();
                    if (faceImgFile != null) {
                        List<MultipartFile> fileList = Arrays.asList(faceImgFile);
                        String tbType = TbTypeEnum.SUBLETRENTER.getValue();
                        //这里modulePath（模块文件夹名）直接使用了tbType，也可以自定义该模块的文件夹名
                        List<FileSaveReturnDTO> fileSaveReturnList = fileService.saveFiles(handleVO.getOperatorId(), fileList, newRenterVO.getId(), tbType, tbType);
                        if (fileSaveReturnList.size() > 0) {
                            newRenterVO.setUserImgUrl(fileSaveReturnList.get(0).getUrl());
                        } else {
                            return CommonResult.fail_msg("文件保存失败");
                        }
                    }
                }
            }
            subletMapper.batchInsertSubletRenterFull(newRenterList);
        }

        //===============批量添加合同和文件的关系===============
        List<MultipartFile> contractFileList = Arrays.asList(files);
        String tbType = TbTypeEnum.SUBLETCONTRACTRELATION.getValue();
        //这里modulePath（模块文件夹名）直接使用了tbType，也可以自定义该模块的文件夹名
        List<FileSaveReturnDTO> fileSaveReturnList = fileService.saveFiles(handleVO.getOperatorId(), contractFileList, subletContractEntity.getId(), tbType, tbType);
        for (FileSaveReturnDTO fileSaveReturnDTO : fileSaveReturnList) {
            //合同关联文件
            SubletContractRelationEntity relationDO = new SubletContractRelationEntity();
            relationDO.setId(SnowflakeUtil.generateId());
            relationDO.setContractId(subletContractEntity.getId());
            relationDO.setFileUrl(fileSaveReturnDTO.getUrl());
            contractFileRelationList.add(relationDO);
        }
        if (contractFileRelationList.size() > 0) {
            subletMapper.batchInsertSubletContractRelation(contractFileRelationList);
        }

        //推送用户居住信息到mq
        UserEntity operator = userMapper.selectUserById(handleVO.getOperatorId());
        AreaCarMqDTO areaCarMqDTO = new AreaCarMqDTO();
        areaCarMqDTO.setArea_id(subletHouseEntity.getAreaId());
        areaCarMqDTO.setHouse_id(subletHouseEntity.getHouseId());
        areaCarMqDTO.setId_card(renterVO.getUserIdentity());
        areaCarMqDTO.setUser_name(renterVO.getUserName());
        areaCarMqDTO.setPhone(renterVO.getUserPhone());
        areaCarMqDTO.setFace_image_url(renterVO.getUserImgUrl());
        areaCarMqDTO.setType(1);
        areaCarMqDTO.setSub_type(1);
        areaCarMqDTO.setUpdate_user(operator.getName());
        thirdService.sendAreaCarDataToMq(areaCarMqDTO);
        for(SubletRenterRegisterVO newRenterVO : newRenterList){
            AreaCarMqDTO item_areaCarMqDTO = new AreaCarMqDTO();
            item_areaCarMqDTO.setArea_id(subletHouseEntity.getAreaId());
            item_areaCarMqDTO.setHouse_id(subletHouseEntity.getHouseId());
            item_areaCarMqDTO.setId_card(newRenterVO.getUserIdentity());
            item_areaCarMqDTO.setUser_name(newRenterVO.getUserName());
            item_areaCarMqDTO.setPhone(newRenterVO.getUserPhone());
            item_areaCarMqDTO.setFace_image_url(newRenterVO.getUserImgUrl());
            item_areaCarMqDTO.setType(1);
            item_areaCarMqDTO.setSub_type(1);
            item_areaCarMqDTO.setUpdate_user(operator.getName());
            thirdService.sendAreaCarDataToMq(item_areaCarMqDTO);
        }

        return new CommonResult();
    }

    public CommonResult cancelSubletRenterRegister(String renterId, String userId) {
        subletMapper.cancelSubletRenterRegister(renterId, userId);
        return new CommonResult();
    }

    public CommonResult getMySubletRenterList(int pageNum, int pageSize, String searchParam, int dataType, String userId) {
        if (dataType != 0 && dataType != 1) {
            return CommonResult.fail_msg("data_type值错误");
        }
        //计算数据开始索引
        int currIndex = (pageNum - 1) * pageSize;

        List<ListMySubletRenterVO> dataList = subletMapper.listMySubletRenter(currIndex, pageSize, searchParam, userId, dataType);
        //总记录数
        int totalNum = subletMapper.countMySubletRenter(searchParam, userId, dataType);
        //总页数
        int totalPageNum = (totalNum + pageSize - 1) / pageSize;

        return new CommonResult(new CommonListResult<>(totalNum, totalPageNum, dataList));
    }

    @Transactional(rollbackFor = Exception.class)
    public CommonResult changeSubletRenterRoom(HandleSubletRoomToRenterVO handleVO) throws IOException, NoSuchAlgorithmException {
        //===============判断人员已在和人员已满===============
        //原登记租客
        SubletRenterVO renterVO = subletMapper.getSubletRenterById(handleVO.getRenterId());
        if (renterVO == null) {
            return CommonResult.fail_msg("找不到客源记录");
        }
        //所有租客身份证列表
        List<String> identityList = new ArrayList<>();
        identityList.add(renterVO.getUserIdentity());
        //判断人员已在房间
        int renterInHouseNum = subletMapper.countSubletRenterInHouseNum(identityList, handleVO.getSubletHouseId(), handleVO.getOperatorId());
        if (renterInHouseNum > 0) {
            return CommonResult.fail_msg("有人员已在该房间，请勿重复分配");
        }
        //>>>>>判断房间人员已满<<<<<
        SubletHouseEntity subletHouseEntity = subletMapper.getSubletHouseById(handleVO.getSubletHouseId());
        //最大居住人数
        int maxLiveNum = subletHouseEntity.getMaxLiveNum();
        //已经入住的人数
        int alreadyLiveNum = subletMapper.countSubletRenterHouseAlreadyLiveNum(handleVO.getSubletHouseId(), handleVO.getOperatorId());
        if (alreadyLiveNum >= maxLiveNum) {
            return CommonResult.fail_msg("该房间人员已满");
        }

        //===============原租房记录退租===============
        subletMapper.leaveSubletRenter(handleVO.getRenterId(), handleVO.getOperatorId());

        //===============新增合同信息===============
        SubletContractEntity subletContractEntity = new SubletContractEntity();
        subletContractEntity.setId(SnowflakeUtil.generateId());
        subletContractEntity.setLiveStartDate(handleVO.getLiveStartDate());
        subletContractEntity.setLiveEndDate(handleVO.getLiveEndDate());
        subletContractEntity.setRemark(handleVO.getRemark());
        subletContractEntity.setType(1);
        subletContractEntity.setCreatorId(handleVO.getOperatorId());
        subletMapper.insertSubletContract(subletContractEntity);

        //===============新增租房记录===============

        //先获取原租房记录
//        SubletRenterDO oldRenter = companyMapper.getSubletRenterById(handleVO.getRenter_id());

        SubletRenterRegisterVO newRenterDO = new SubletRenterRegisterVO();
        newRenterDO.setId(SnowflakeUtil.generateId());
        newRenterDO.setSubletHouseId(handleVO.getSubletHouseId());
        newRenterDO.setStatus(1);
        newRenterDO.setContractId(subletContractEntity.getId());
        newRenterDO.setCreatorId(handleVO.getOperatorId());
        newRenterDO.setOperatorId(handleVO.getOperatorId());
        newRenterDO.setApplyType(1);
        //补充用户信息
        newRenterDO.setUserId(renterVO.getUserId());
        newRenterDO.setUserName(renterVO.getUserName());
        newRenterDO.setUserPhone(renterVO.getUserPhone());
        newRenterDO.setUserIdentityType(renterVO.getUserIdentityType());
        newRenterDO.setUserIdentity(renterVO.getUserIdentity());
        newRenterDO.setUserImgUrl(renterVO.getUserImgUrl());

        List<SubletRenterRegisterVO> newRenterList = new ArrayList<>();
        newRenterList.add(newRenterDO);
        subletMapper.batchInsertSubletRenterFull(newRenterList);

        //===============批量添加合同和文件的关系===============
        //合同和文件关系列表
        List<SubletContractRelationEntity> contractFileRelationList = new ArrayList<>();

        List<MultipartFile> contractFileList = Arrays.asList(handleVO.getFiles());
        String tbType = TbTypeEnum.SUBLETCONTRACTRELATION.getValue();
        //这里modulePath（模块文件夹名）直接使用了tbType，也可以自定义该模块的文件夹名
        List<FileSaveReturnDTO> fileSaveReturnList = fileService.saveFiles(handleVO.getOperatorId(), contractFileList, subletContractEntity.getId(), tbType, tbType);
        for (FileSaveReturnDTO fileSaveReturnDTO : fileSaveReturnList) {
            //合同关联文件
            SubletContractRelationEntity relationDO = new SubletContractRelationEntity();
            relationDO.setId(SnowflakeUtil.generateId());
            relationDO.setContractId(subletContractEntity.getId());
            relationDO.setFileUrl(fileSaveReturnDTO.getUrl());
            contractFileRelationList.add(relationDO);
        }
        if (contractFileRelationList.size() > 0) {
            subletMapper.batchInsertSubletContractRelation(contractFileRelationList);
        }

        //推送用户居住信息到mq
        UserEntity operator = userMapper.selectUserById(handleVO.getOperatorId());
        AreaCarMqDTO areaCarMqDTO = new AreaCarMqDTO();
        areaCarMqDTO.setArea_id(subletHouseEntity.getAreaId());
        areaCarMqDTO.setHouse_id(subletHouseEntity.getHouseId());
        areaCarMqDTO.setId_card(newRenterDO.getUserIdentity());
        areaCarMqDTO.setUser_name(newRenterDO.getUserName());
        areaCarMqDTO.setPhone(newRenterDO.getUserPhone());
        areaCarMqDTO.setFace_image_url(newRenterDO.getUserImgUrl());
        areaCarMqDTO.setType(1);
        areaCarMqDTO.setSub_type(1);
        areaCarMqDTO.setUpdate_user(operator.getName());
        thirdService.sendAreaCarDataToMq(areaCarMqDTO);

        return new CommonResult();
    }

    public CommonResult leaveSubletRenterRoom(String renterId, String userId) {
        subletMapper.leaveSubletRenter(renterId, userId);
        return new CommonResult();
    }

    @Transactional(rollbackFor = Exception.class)
    public CommonResult continueSubletRenterRoom(HandleSubletRoomToRenterVO handleVO) throws IOException, NoSuchAlgorithmException {
        //===============新增合同信息===============
        SubletContractEntity subletContractEntity = new SubletContractEntity();
        subletContractEntity.setId(SnowflakeUtil.generateId());
        subletContractEntity.setLiveStartDate(handleVO.getLiveStartDate());
        subletContractEntity.setLiveEndDate(handleVO.getLiveEndDate());
        subletContractEntity.setRemark(handleVO.getRemark());
        subletContractEntity.setType(1);
        subletContractEntity.setCreatorId(handleVO.getOperatorId());
        subletMapper.insertSubletContract(subletContractEntity);

        //===============更新原租房记录（新合同号）===============
        subletMapper.continueSubletRenter(handleVO.getRenterId(), subletContractEntity.getId(), handleVO.getOperatorId());

        //===============批量添加合同和文件的关系===============
        //合同和文件关系列表
        List<SubletContractRelationEntity> contractFileRelationList = new ArrayList<>();

        List<MultipartFile> contractFileList = Arrays.asList(handleVO.getFiles());
        String tbType = TbTypeEnum.SUBLETCONTRACTRELATION.getValue();
        //这里modulePath（模块文件夹名）直接使用了tbType，也可以自定义该模块的文件夹名
        List<FileSaveReturnDTO> fileSaveReturnList = fileService.saveFiles(handleVO.getOperatorId(), contractFileList, subletContractEntity.getId(), tbType, tbType);
        for (FileSaveReturnDTO fileSaveReturnDTO : fileSaveReturnList) {
            //合同关联文件
            SubletContractRelationEntity relationDO = new SubletContractRelationEntity();
            relationDO.setId(SnowflakeUtil.generateId());
            relationDO.setContractId(subletContractEntity.getId());
            relationDO.setFileUrl(fileSaveReturnDTO.getUrl());
            contractFileRelationList.add(relationDO);
        }
        if (contractFileRelationList.size() > 0) {
            subletMapper.batchInsertSubletContractRelation(contractFileRelationList);
        }

        return new CommonResult();
    }

    public CommonResult getHouseSubletCountByArea(String userId) {
        List<Map> house_list = subletMapper.getHouseSubletHouseCountByArea(userId);
        List<Map> renter_list = subletMapper.getHouseSubletRenterCountByArea(userId);
        house_list.forEach(x -> {
            renter_list.forEach(y -> {
                if (String.valueOf(x.get("area_id")).equals(String.valueOf(y.get("area_id")))) {
                    x.put("renter_num", y.get("renter_num"));
                }
            });
//            x.put("area_location_name", String.valueOf(x.get("area_location_name")).split("/")[4]);
        });
        return new CommonResult(house_list);
    }

    public CommonResult getUnUsedHouseSubletCountByArea(String userId) {
        return new CommonResult(subletMapper.getUnUsedHouseSubletHouseCountByArea(userId));
    }

    public CommonResult getHouseSubletRenterList(int pageNum, int pageSize, String searchParam, String subletHouseId, String userId) {
        //计算数据开始索引
        int currIndex = (pageNum - 1) * pageSize;

        List<ListMySubletRenterVO> dataList = subletMapper.getHouseSubletRenterList(currIndex, pageSize, searchParam, userId, subletHouseId);
        //总记录数
        int totalNum = subletMapper.countHouseSubletRenter(searchParam, userId, subletHouseId);
        //总页数
        int totalPageNum = (totalNum + pageSize - 1) / pageSize;

        return new CommonResult(new CommonListResult<>(totalNum, totalPageNum, dataList));
    }

    public CommonResult getHouseSubletContractImgList(String contract_id) {
        return new CommonResult(subletMapper.getHouseSubletContractImgList(contract_id));
    }

    @Transactional(rollbackFor = Exception.class)
    public CommonResult addRenterToUnusedRoom(HandleSubletRoomToRenterVO handleSubletRoomToRenterVO) throws IOException, NoSuchAlgorithmException {

        MultipartFile[] files = handleSubletRoomToRenterVO.getFiles();
        if (files.length == 0) {
            return CommonResult.fail_msg("合同文件不能为空");
        }

        //>>>>>判断房间人员已满<<<<<
        SubletHouseEntity subletHouseEntity = subletMapper.getSubletHouseById(handleSubletRoomToRenterVO.getSubletHouseId());
        //最大居住人数
        int maxLiveNum = subletHouseEntity.getMaxLiveNum();
        //已经入住的人数
        int alreadyLiveNum = subletMapper.countSubletRenterHouseAlreadyLiveNum(handleSubletRoomToRenterVO.getSubletHouseId(), handleSubletRoomToRenterVO.getOperatorId());
        //将要添加的人数
        int addRenterNum = (handleSubletRoomToRenterVO.getNewRenterList() == null ? 0 : handleSubletRoomToRenterVO.getNewRenterList().size());
        if (alreadyLiveNum + addRenterNum > maxLiveNum) {
            return CommonResult.fail_msg("该房间可分配人员数量不足：" + "可住人数：" + maxLiveNum + " 已住人数：" + alreadyLiveNum + " 分配人数：" + addRenterNum);
        }

        //===============新增合同信息===============
        SubletContractEntity subletContractEntity = new SubletContractEntity();
        subletContractEntity.setId(SnowflakeUtil.generateId());
        subletContractEntity.setLiveStartDate(handleSubletRoomToRenterVO.getLiveStartDate());
        subletContractEntity.setLiveEndDate(handleSubletRoomToRenterVO.getLiveEndDate());
        subletContractEntity.setRemark(handleSubletRoomToRenterVO.getRemark());
        subletContractEntity.setType(1);
        subletContractEntity.setCreatorId(handleSubletRoomToRenterVO.getOperatorId());
        subletMapper.insertSubletContract(subletContractEntity);


        //===============批量添加租客===============
        List<SubletRenterRegisterVO> renterList = handleSubletRoomToRenterVO.getNewRenterList();
        for (SubletRenterRegisterVO renterDO : renterList) {
            renterDO.setId(SnowflakeUtil.generateId());
            renterDO.setSubletHouseId(handleSubletRoomToRenterVO.getSubletHouseId());
            renterDO.setStatus(1);
            renterDO.setContractId(subletContractEntity.getId());
            renterDO.setCreatorId(handleSubletRoomToRenterVO.getOperatorId());
            renterDO.setOperatorId(handleSubletRoomToRenterVO.getOperatorId());
            renterDO.setApplyType(1);
            String renterImgUrl = renterDO.getUserImgUrl();
            if (StringUtils.isBlank(renterImgUrl)) {
                MultipartFile faceImgFile = renterDO.getFaceImgFile();
                if (faceImgFile != null) {
                    List<MultipartFile> fileList = Arrays.asList(faceImgFile);
                    String tbType = TbTypeEnum.SUBLETRENTER.getValue();
                    //这里modulePath（模块文件夹名）直接使用了tbType，也可以自定义该模块的文件夹名
                    List<FileSaveReturnDTO> fileSaveReturnList = fileService.saveFiles(handleSubletRoomToRenterVO.getOperatorId(), fileList, renterDO.getId(), tbType, tbType);
                    if (fileSaveReturnList.size() > 0) {
                        renterDO.setUserImgUrl(fileSaveReturnList.get(0).getUrl());
                    } else {
                        return CommonResult.fail_msg("文件保存失败");
                    }
                }
            }
        }
        subletMapper.batchInsertSubletRenterFull(renterList);

        //===============添加合同和合同文件的关系===============
        //合同和租客关系列表
        List<SubletContractRelationEntity> contractFileRelationList = new ArrayList<>();
        List<MultipartFile> contractFileList = Arrays.asList(handleSubletRoomToRenterVO.getFiles());
        String tbType = TbTypeEnum.SUBLETCONTRACTRELATION.getValue();
        //这里modulePath（模块文件夹名）直接使用了tbType，也可以自定义该模块的文件夹名
        List<FileSaveReturnDTO> fileSaveReturnList = fileService.saveFiles(handleSubletRoomToRenterVO.getOperatorId(), contractFileList, subletContractEntity.getId(), tbType, tbType);
        for (FileSaveReturnDTO fileSaveReturnDTO : fileSaveReturnList) {
            //合同关联文件
            SubletContractRelationEntity relationDO = new SubletContractRelationEntity();
            relationDO.setId(SnowflakeUtil.generateId());
            relationDO.setContractId(subletContractEntity.getId());
            relationDO.setFileUrl(fileSaveReturnDTO.getUrl());
            contractFileRelationList.add(relationDO);
        }
        if (contractFileRelationList.size() > 0) {
            subletMapper.batchInsertSubletContractRelation(contractFileRelationList);
        }

        //推送用户居住信息到mq
        UserEntity operator = userMapper.selectUserById(handleSubletRoomToRenterVO.getOperatorId());
        for(SubletRenterRegisterVO newRenterVO : renterList){
            AreaCarMqDTO item_areaCarMqDTO = new AreaCarMqDTO();
            item_areaCarMqDTO.setArea_id(subletHouseEntity.getAreaId());
            item_areaCarMqDTO.setHouse_id(subletHouseEntity.getHouseId());
            item_areaCarMqDTO.setId_card(newRenterVO.getUserIdentity());
            item_areaCarMqDTO.setUser_name(newRenterVO.getUserName());
            item_areaCarMqDTO.setPhone(newRenterVO.getUserPhone());
            item_areaCarMqDTO.setFace_image_url(newRenterVO.getUserImgUrl());
            item_areaCarMqDTO.setType(1);
            item_areaCarMqDTO.setSub_type(1);
            item_areaCarMqDTO.setUpdate_user(operator.getName());
            thirdService.sendAreaCarDataToMq(item_areaCarMqDTO);
        }
        return new CommonResult();

    }

    public CommonResult delSubletUnusedRoom(String sublet_house_id) {
        int renter_num = subletMapper.getHouseSubletRenterNum(sublet_house_id);
        if (renter_num > 0) {
            return CommonResult.fail_msg("房间有人或者有待分配的人");
        } else {
            boolean success = subletMapper.delSubletUnusedRoom(sublet_house_id);
            return CommonResult.fail_msg("删除成功");
        }
    }

    public CommonResult getRenterDecryptInfo(String id, Integer type) {
        SubletRenterVO subletRenterVO = subletMapper.getSubletRenterById(id);
        if (subletRenterVO != null) {
            if (type == 1) {
                return new CommonResult(subletRenterVO.getUserPhone());
            } else if (type == 2) {
                return new CommonResult(subletRenterVO.getUserIdentity());
            } else {
                return CommonResult.fail_msg("参数有误");
            }
        } else {
            return CommonResult.fail_msg("查无数据");
        }
    }

    public CommonResult getSubletRoomIndexCount(String userId) {
        int sublet_renter_count = subletMapper.getSubletRenterNum(userId);
        int sublet_owner_count = subletMapper.getSubletOwnerNum(userId);
        int vacant_house_num = subletMapper.getVacantHouseNum(userId);
        int total_house_num = subletMapper.countMyTotalSubletHouse(userId);
        Map map = new HashMap();
        map.put("sublet_renter_count", sublet_renter_count);
        map.put("sublet_owner_count", sublet_owner_count);
        map.put("vacant_house_num", vacant_house_num);
        map.put("total_house_num", total_house_num);
        return new CommonResult(map);
    }

    @Transactional(rollbackFor = Exception.class)
    public CommonResult bindSubletHouseOwner(HandleSubletHouseOwnerVO handleVO) throws IOException, NoSuchAlgorithmException {

        //===============新增合同信息===============
        SubletContractEntity subletContractEntity = new SubletContractEntity();
        subletContractEntity.setId(SnowflakeUtil.generateId());
        subletContractEntity.setLiveStartDate(handleVO.getLiveStartDate());
        subletContractEntity.setLiveEndDate(handleVO.getLiveEndDate());
        subletContractEntity.setRemark(handleVO.getRemark());
        subletContractEntity.setType(0);
        subletContractEntity.setCreatorId(handleVO.getOperatorId());
        subletMapper.insertSubletContract(subletContractEntity);

        //===============更新房屋-业主信息===============
        handleVO.setContractId(subletContractEntity.getId());
        String ownerImgUrl = handleVO.getOwnerImgUrl();
        if (StringUtils.isBlank(ownerImgUrl)) {
            MultipartFile ownerImgFile = handleVO.getOwnerImgFile();
            if (ownerImgFile != null) {
                List<MultipartFile> fileList = Arrays.asList(ownerImgFile);
                String tbType = TbTypeEnum.SUBLETHOUSE.getValue();
                //这里modulePath（模块文件夹名）直接使用了tbType，也可以自定义该模块的文件夹名
                List<FileSaveReturnDTO> fileSaveReturnList = fileService.saveFiles(handleVO.getOperatorId(), fileList, handleVO.getSubletHouseId(), tbType, tbType);
                if (fileSaveReturnList.size() > 0) {
                    handleVO.setOwnerImgUrl(fileSaveReturnList.get(0).getUrl());
                } else {
                    return CommonResult.fail_msg("文件保存失败");
                }
            }
        }
        subletMapper.updateSubletHouseOwner(handleVO);

        //===============批量添加合同和文件的关系===============
        //合同和文件关系列表
        List<SubletContractRelationEntity> contractFileRelationList = new ArrayList<>();

        List<MultipartFile> contractFileList = Arrays.asList(handleVO.getFiles());
        String tbType = TbTypeEnum.SUBLETCONTRACTRELATION.getValue();
        //这里modulePath（模块文件夹名）直接使用了tbType，也可以自定义该模块的文件夹名
        List<FileSaveReturnDTO> fileSaveReturnList = fileService.saveFiles(handleVO.getOperatorId(), contractFileList, subletContractEntity.getId(), tbType, tbType);
        for (FileSaveReturnDTO fileSaveReturnDTO : fileSaveReturnList) {
            //合同关联文件
            SubletContractRelationEntity relationDO = new SubletContractRelationEntity();
            relationDO.setId(SnowflakeUtil.generateId());
            relationDO.setContractId(subletContractEntity.getId());
            relationDO.setFileUrl(fileSaveReturnDTO.getUrl());
            contractFileRelationList.add(relationDO);
        }
        if (contractFileRelationList.size() > 0) {
            subletMapper.batchInsertSubletContractRelation(contractFileRelationList);
        }

        //推送用户居住信息到mq
        UserEntity operator = userMapper.selectUserById(handleVO.getOperatorId());
        SubletHouseEntity subletHouseEntity = subletMapper.getSubletHouseById(handleVO.getSubletHouseId());
        AreaCarMqDTO areaCarMqDTO = new AreaCarMqDTO();
        areaCarMqDTO.setArea_id(subletHouseEntity.getAreaId());
        areaCarMqDTO.setHouse_id(subletHouseEntity.getHouseId());
        areaCarMqDTO.setId_card(handleVO.getOwnerIdentity());
        areaCarMqDTO.setUser_name(handleVO.getOwnerName());
        areaCarMqDTO.setPhone(handleVO.getOwnerPhone());
        areaCarMqDTO.setFace_image_url(handleVO.getOwnerImgUrl());
        areaCarMqDTO.setType(1);
        areaCarMqDTO.setSub_type(1);
        areaCarMqDTO.setUpdate_user(operator.getName());
        thirdService.sendAreaCarDataToMq(areaCarMqDTO);

        return new CommonResult();
    }

    public CommonResult getBindSubletHouseOwnerList(int pageNum, int pageSize, String searchParam, String adminId, int dataType) {
        //计算数据开始索引
        int currIndex = (pageNum - 1) * pageSize;

        List<ListBindSubletHouseOwnerVO> dataList = subletMapper.listBindSubletHouseOwner(currIndex, pageSize, searchParam, adminId, dataType);
        //总记录数
        int totalNum = subletMapper.countBindSubletHouseOwner(searchParam, adminId, dataType);
        //总页数
        int totalPageNum = (totalNum + pageSize - 1) / pageSize;

        return new CommonResult(new CommonListResult<>(totalNum, totalPageNum, dataList));
    }

    public CommonResult leaveSubletHouseOwner(String subletHouseId, String adminId) {
        int alreadyLiveNum = subletMapper.countSubletRenterHouseAlreadyLiveNum(subletHouseId, adminId);
        if (alreadyLiveNum > 0) {
            return CommonResult.fail_msg("房间下还有租客在住，无法操作");
        }
        subletMapper.leaveSubletHouseOwner(subletHouseId, adminId);
        return new CommonResult();
    }

    @Transactional(rollbackFor = Exception.class)
    public CommonResult continueSubletHouseOwner(HandleSubletHouseOwnerVO handleVO) throws IOException, NoSuchAlgorithmException {

        //===============新增合同信息===============
        SubletContractEntity subletContractEntity = new SubletContractEntity();
        subletContractEntity.setId(SnowflakeUtil.generateId());
        subletContractEntity.setLiveStartDate(handleVO.getLiveStartDate());
        subletContractEntity.setLiveEndDate(handleVO.getLiveEndDate());
        subletContractEntity.setRemark(handleVO.getRemark());
        subletContractEntity.setType(0);
        subletContractEntity.setCreatorId(handleVO.getOperatorId());
        subletMapper.insertSubletContract(subletContractEntity);

        //===============更新房源业主合同信息===============
        handleVO.setContractId(subletContractEntity.getId());
        subletMapper.continueSubletHouseOwner(handleVO);

        //===============批量添加合同和文件的关系===============
        //合同和文件关系列表
        List<SubletContractRelationEntity> contractFileRelationList = new ArrayList<>();

        List<MultipartFile> contractFileList = Arrays.asList(handleVO.getFiles());
        String tbType = TbTypeEnum.SUBLETCONTRACTRELATION.getValue();
        //这里modulePath（模块文件夹名）直接使用了tbType，也可以自定义该模块的文件夹名
        List<FileSaveReturnDTO> fileSaveReturnList = fileService.saveFiles(handleVO.getOperatorId(), contractFileList, subletContractEntity.getId(), tbType, tbType);
        for (FileSaveReturnDTO fileSaveReturnDTO : fileSaveReturnList) {
            //合同关联文件
            SubletContractRelationEntity relationDO = new SubletContractRelationEntity();
            relationDO.setId(SnowflakeUtil.generateId());
            relationDO.setContractId(subletContractEntity.getId());
            relationDO.setFileUrl(fileSaveReturnDTO.getUrl());
            contractFileRelationList.add(relationDO);
        }
        if (contractFileRelationList.size() > 0) {
            subletMapper.batchInsertSubletContractRelation(contractFileRelationList);
        }

        return new CommonResult();
    }

    public CommonResult getNoBindOwnerSubletCommunityList(String adminId) {
        return new CommonResult(subletMapper.getNoBindOwnerSubletCommunityList(adminId));
    }

    public CommonResult getNoBindOwnerSubletHouseListByCommunity(String areaId, String searchParam, String adminId) {
        return new CommonResult(subletMapper.listNoBindOwnerSubletHouseListByCommunity(areaId, searchParam, adminId));
    }

    public CommonResult countNoOwnerSubletHouse(String userId) {
        return new CommonResult(subletMapper.countNoOwnerSubletHouse(userId));
    }

    public CommonResult countSubletHouseAdventOwner(String userId) {
        int totalNum = subletMapper.countBindSubletHouseOwner(null, userId, 1);
        return new CommonResult(totalNum);
    }

    public CommonResult getNeedHandleSubletRenterTodoNum(String adminId) {
        int totalNum = subletMapper.countNeedHandleSubletRenter(null, adminId);
        return new CommonResult(totalNum);
    }

    public CommonResult getMySubletRenterWillExpireNum(String adminId) {
        int totalNum = subletMapper.countMySubletRenter(null, adminId, 0);
        return new CommonResult(totalNum);
    }

    public CommonResult addSubletRoom(SubletHouseEntity subletHouseEntity) {
        SubletHouseEntity ex = subletMapper.getSubletHouseByHouseId(subletHouseEntity.getHouseId(), subletHouseEntity.getCreatorId());
        if (ex != null) {
            subletHouseEntity.setId(ex.getId());
            subletMapper.updSubletRoom(subletHouseEntity);
        } else {
            subletHouseEntity.setId(SnowflakeUtil.generateId());
            subletMapper.insertSubletRoom(subletHouseEntity);
        }
        return new CommonResult(subletHouseEntity);
    }

    public CommonResult getManagerSubletRoom(int pageNum, int pageSize, String userId) {
        //计算数据开始索引
        int currIndex = (pageNum - 1) * pageSize;

        List<SubletHouseEntity> dataList = subletMapper.getManagerSubletHouseList(currIndex, pageSize, userId);
        //总记录数
        int totalNum = subletMapper.countManagerSubletHouse(userId);
        //总页数
        int totalPageNum = (totalNum + pageSize - 1) / pageSize;

        return new CommonResult(new CommonListResult<>(totalNum, totalPageNum, dataList));
    }


    public void createHouseTree(SubletHouseVO subletHouseVO, List<SubletHouseVO> list) {
        list.stream().forEach(e -> {
            if (StringUtils.isNotBlank(e.getParentId()) && e.getParentId().equals(subletHouseVO.getId())) {
                if (subletHouseVO.getList() == null) {
                    subletHouseVO.setList(new ArrayList<>());
                }
                createHouseTree(e, list);
                subletHouseVO.getList().add(e);
            }
        });
    }
}
