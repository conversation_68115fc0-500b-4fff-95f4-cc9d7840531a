package com.jkga.wyyjs.service;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

/**
 * UserManagerService processUserInfo方法测试类
 * 
 * @Author：clyde
 * @Date：2025/8/5
 */
@SpringBootTest
@ActiveProfiles("dev")
public class UserManagerServiceProcessUserInfoTest {

    @Autowired
    private UserManagerService userManagerService;

    /**
     * 测试场景1：用户不存在，创建新用户
     */
    @Test
    public void testCreateNewUser() {
        JSONObject data = new JSONObject();
        data.put("type", 1);
        data.put("area_id", "test_place_001");
        data.put("id_card", "330424199001010001");
        data.put("user_name", "测试用户1");
        data.put("phone", "18800000001");
        data.put("position", "管理员");
        data.put("status", "0");
        data.put("update_user", "测试人员");
        data.put("place_name", "测试场所1");

        System.out.println("测试场景1：用户不存在，创建新用户");
        System.out.println("测试数据: " + JSONUtil.toJsonStr(data));
        
        try {
            userManagerService.processUserManagerMessage(data);
            System.out.println("✅ 新用户创建测试成功");
        } catch (Exception e) {
            System.err.println("❌ 新用户创建测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试场景2：用户存在且手机号一致
     */
    @Test
    public void testExistingUserSamePhone() {
        // 先创建一个用户
        JSONObject createData = new JSONObject();
        createData.put("type", 1);
        createData.put("area_id", "test_place_002");
        createData.put("id_card", "330424199001010002");
        createData.put("user_name", "测试用户2");
        createData.put("phone", "18800000002");
        createData.put("position", "管理员");
        createData.put("status", "0");
        createData.put("update_user", "测试人员");
        createData.put("place_name", "测试场所2");

        System.out.println("测试场景2：用户存在且手机号一致");
        System.out.println("第一次创建用户: " + JSONUtil.toJsonStr(createData));
        
        try {
            userManagerService.processUserManagerMessage(createData);
            System.out.println("✅ 第一次创建成功");
            
            // 再次发送相同的消息
            System.out.println("第二次发送相同消息...");
            userManagerService.processUserManagerMessage(createData);
            System.out.println("✅ 用户存在且手机号一致测试成功");
            
        } catch (Exception e) {
            System.err.println("❌ 用户存在且手机号一致测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试场景3：用户存在但手机号不一致
     */
    @Test
    public void testExistingUserDifferentPhone() {
        // 先创建一个用户
        JSONObject createData = new JSONObject();
        createData.put("type", 1);
        createData.put("area_id", "test_place_003");
        createData.put("id_card", "330424199001010003");
        createData.put("user_name", "测试用户3");
        createData.put("phone", "18800000003");
        createData.put("position", "管理员");
        createData.put("status", "0");
        createData.put("update_user", "测试人员");
        createData.put("place_name", "测试场所3");

        System.out.println("测试场景3：用户存在但手机号不一致");
        System.out.println("第一次创建用户: " + JSONUtil.toJsonStr(createData));
        
        try {
            userManagerService.processUserManagerMessage(createData);
            System.out.println("✅ 第一次创建成功");
            
            // 发送相同身份证但不同手机号的消息
            JSONObject updateData = new JSONObject();
            updateData.put("type", 1);
            updateData.put("area_id", "test_place_003");
            updateData.put("id_card", "330424199001010003"); // 相同身份证
            updateData.put("user_name", "测试用户3");
            updateData.put("phone", "18800000033"); // 不同手机号
            updateData.put("position", "高级管理员");
            updateData.put("status", "0");
            updateData.put("update_user", "测试人员");
            updateData.put("place_name", "测试场所3");
            
            System.out.println("第二次发送不同手机号消息: " + JSONUtil.toJsonStr(updateData));
            userManagerService.processUserManagerMessage(updateData);
            System.out.println("✅ 用户存在但手机号不一致测试成功");
            
        } catch (Exception e) {
            System.err.println("❌ 用户存在但手机号不一致测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试场景4：手机号冲突 - identity为null的用户被删除
     */
    @Test
    public void testPhoneConflictWithNullIdentity() {
        System.out.println("测试场景4：手机号冲突 - identity为null的用户被删除");
        
        // 这个测试需要先在数据库中创建一个identity为null但有手机号的用户
        // 然后创建一个新用户使用相同的手机号
        
        JSONObject data = new JSONObject();
        data.put("type", 1);
        data.put("area_id", "test_place_004");
        data.put("id_card", "330424199001010004");
        data.put("user_name", "测试用户4");
        data.put("phone", "18800000004"); // 假设这个手机号已被identity为null的用户使用
        data.put("position", "管理员");
        data.put("status", "0");
        data.put("update_user", "测试人员");
        data.put("place_name", "测试场所4");

        System.out.println("测试数据: " + JSONUtil.toJsonStr(data));
        
        try {
            userManagerService.processUserManagerMessage(data);
            System.out.println("✅ 手机号冲突处理（删除identity为null的用户）测试成功");
        } catch (Exception e) {
            System.err.println("❌ 手机号冲突处理测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试场景5：手机号冲突 - identity不为null的用户清空open_id和phone
     */
    @Test
    public void testPhoneConflictWithNonNullIdentity() {
        System.out.println("测试场景5：手机号冲突 - identity不为null的用户清空open_id和phone");
        
        // 先创建一个有身份证的用户
        JSONObject existingUserData = new JSONObject();
        existingUserData.put("type", 1);
        existingUserData.put("area_id", "test_place_005a");
        existingUserData.put("id_card", "330424199001010005");
        existingUserData.put("user_name", "已存在用户");
        existingUserData.put("phone", "18800000005");
        existingUserData.put("position", "员工");
        existingUserData.put("status", "0");
        existingUserData.put("update_user", "测试人员");
        existingUserData.put("place_name", "测试场所5a");

        System.out.println("先创建已存在用户: " + JSONUtil.toJsonStr(existingUserData));
        
        try {
            userManagerService.processUserManagerMessage(existingUserData);
            System.out.println("✅ 已存在用户创建成功");
            
            // 再创建一个新用户，使用相同的手机号但不同的身份证
            JSONObject newUserData = new JSONObject();
            newUserData.put("type", 1);
            newUserData.put("area_id", "test_place_005b");
            newUserData.put("id_card", "330424199001010055"); // 不同身份证
            newUserData.put("user_name", "新用户");
            newUserData.put("phone", "18800000005"); // 相同手机号
            newUserData.put("position", "管理员");
            newUserData.put("status", "0");
            newUserData.put("update_user", "测试人员");
            newUserData.put("place_name", "测试场所5b");
            
            System.out.println("创建新用户（相同手机号）: " + JSONUtil.toJsonStr(newUserData));
            userManagerService.processUserManagerMessage(newUserData);
            System.out.println("✅ 手机号冲突处理（清空identity不为null的用户字段）测试成功");
            
        } catch (Exception e) {
            System.err.println("❌ 手机号冲突处理测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试场景6：删除用户操作
     */
    @Test
    public void testDeleteUser() {
        // 先创建一个用户
        JSONObject createData = new JSONObject();
        createData.put("type", 1);
        createData.put("area_id", "test_place_006");
        createData.put("id_card", "330424199001010006");
        createData.put("user_name", "待删除用户");
        createData.put("phone", "18800000006");
        createData.put("position", "管理员");
        createData.put("status", "0");
        createData.put("update_user", "测试人员");
        createData.put("place_name", "测试场所6");

        System.out.println("测试场景6：删除用户操作");
        System.out.println("先创建用户: " + JSONUtil.toJsonStr(createData));
        
        try {
            userManagerService.processUserManagerMessage(createData);
            System.out.println("✅ 用户创建成功");
            
            // 发送删除消息
            JSONObject deleteData = new JSONObject();
            deleteData.put("type", 1);
            deleteData.put("area_id", "test_place_006");
            deleteData.put("id_card", "330424199001010006");
            deleteData.put("user_name", "待删除用户");
            deleteData.put("phone", "18800000006");
            deleteData.put("position", "管理员");
            deleteData.put("status", "1"); // status=1表示删除
            deleteData.put("update_user", "测试人员");
            deleteData.put("place_name", "测试场所6");
            
            System.out.println("发送删除消息: " + JSONUtil.toJsonStr(deleteData));
            userManagerService.processUserManagerMessage(deleteData);
            System.out.println("✅ 用户删除测试成功");
            
        } catch (Exception e) {
            System.err.println("❌ 用户删除测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 综合测试：验证所有逻辑
     */
    @Test
    public void testComprehensiveScenarios() {
        System.out.println("=== 综合测试开始 ===");
        
        try {
            // 场景1：创建用户A
            testCreateNewUser();
            Thread.sleep(1000);
            
            // 场景2：相同用户信息再次发送
            testExistingUserSamePhone();
            Thread.sleep(1000);
            
            // 场景3：相同身份证不同手机号
            testExistingUserDifferentPhone();
            Thread.sleep(1000);
            
            System.out.println("=== 综合测试完成 ===");
            
        } catch (Exception e) {
            System.err.println("❌ 综合测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
