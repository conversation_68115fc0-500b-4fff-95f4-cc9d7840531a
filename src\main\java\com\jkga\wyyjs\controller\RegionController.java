package com.jkga.wyyjs.controller;

import com.jkga.wyyjs.constant.UserFunctionEnum;
import com.jkga.wyyjs.model.CommonCode;
import com.jkga.wyyjs.model.CommonResult;
import com.jkga.wyyjs.service.RegionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;


/**
 * 区域管理
 *
 * <AUTHOR>
 * @date 2025/2/6 15:08
 */
@Slf4j
@CrossOrigin
@RestController
@RequestMapping("region")
public class RegionController {

    @Autowired
    private RegionService regionService;

    /**
     * 区域列表
     *
     * @return
     */
    @RequestMapping(value = "list", method = RequestMethod.GET)
    public CommonResult getRegionList(String parentId, int level) {
        return new CommonResult<>(regionService.getRegionList(parentId, level));
    }

    /**
    * 根据社区id获取小区列表
    *
    * @Author：clyde
    * @Date：2025/2/19  9:13
    *
    */
    @RequestMapping(value = "areaList", method = RequestMethod.GET)
    public CommonResult getAreaList(String regionId) {
        return new CommonResult<>(regionService.getAreaList(regionId));
    }

    /**
     * 根据小区获取楼幢
     *
     * @Author：clyde
     * @Date：2025/7/31 8:59
     *
     */
    @RequestMapping(value = "getBuildingByAreaId", method = RequestMethod.GET)
    public CommonResult getBuildingByAreaId(String areaId) {
        try {
            return regionService.getBuildingByAreaId(areaId);
        } catch (Exception e) {
            return new CommonResult(CommonCode.SYSTEM_ERROR);
        }
    }

    /**
    *  根据小区id获取楼幢 单元 房屋
    *
    * @Author：clyde
    * @Date：2025/2/19  10:34
    *
    */
    @RequestMapping(value = "houseList", method = RequestMethod.GET)
    public CommonResult getHouseList(String areaId) {
        return new CommonResult<>(regionService.getHouseList(areaId));
    }
}
