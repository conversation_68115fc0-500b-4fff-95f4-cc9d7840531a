package com.jkga.wyyjs.model.vo;

import com.jkga.wyyjs.anno.PrivacyEncrypt;
import com.jkga.wyyjs.constant.PrivacyTypeEnum;
import lombok.Data;

/**
 * @Author：clyde
 * @Date：2025/7/22 10:01
 */
@Data
public class ListBindSubletHouseOwnerVO {

    private String id;
    private String areaId;
    private String areaLocationName;
    private String houseId;
    private String houseName;
    private Integer maxLiveNum;
    private String ownerUserId;
    private String ownerName;
    @PrivacyEncrypt(type = PrivacyTypeEnum.PHONE)// 脱敏手机号
    private String ownerPhone;
    private String ownerIdentityType;
    @PrivacyEncrypt(type = PrivacyTypeEnum.ID_CARD) // 脱敏证件号
    private String ownerIdentity;
    private String ownerImgUrl;
    private String contractId;
    private String creatorId;
    private String createTime;
    private Integer isDeleted;
    //
    private String liveStartDate;
    private String liveEndDate;
    private String remark;
    private Integer type;
}
