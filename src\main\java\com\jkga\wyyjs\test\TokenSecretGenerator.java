package com.jkga.wyyjs.test;

import java.security.SecureRandom;
import java.util.Base64;

/**
 * token私钥生成
 *
 * <AUTHOR>
 * @date 2025/1/14 14:24
 */
public class TokenSecretGenerator {
    private static final int SECRET_KEY_LENGTH = 32; // 256 bits

    public static String generateTokenSecret() {
        SecureRandom random = new SecureRandom();
        byte[] secretBytes = new byte[SECRET_KEY_LENGTH];
        random.nextBytes(secretBytes);
        return Base64.getUrlEncoder().withoutPadding().encodeToString(secretBytes);
    }

    public static void main(String[] args) {
        System.out.println(generateTokenSecret());
    }
}