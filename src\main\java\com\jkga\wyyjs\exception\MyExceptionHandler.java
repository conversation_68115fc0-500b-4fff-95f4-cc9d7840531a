package com.jkga.wyyjs.exception;

import com.jkga.wyyjs.model.CommonCode;
import com.jkga.wyyjs.model.CommonResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.validation.ObjectError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 全局异常处理
 *
 * <AUTHOR>
 */
@ControllerAdvice
@Slf4j
public class MyExceptionHandler {

    @ResponseBody
    @ExceptionHandler(value = Exception.class)
    public CommonResult errorHandler(HttpServletRequest request, Exception e) {
        if (e instanceof MyException) {
            return ((MyException) e).getCommonResult();
        } else {
            //输出异常堆栈
//            log.error("全局异常捕获: " + request.getRequestURI() + " --> " + e.toString());
//            e.printStackTrace();
            logError(request, e);
            return new CommonResult(CommonCode.SYSTEM_ERROR.getCode(), e.getMessage());
        }
    }

    // 捕获实体参数校验异常
    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseBody
    public CommonResult methodArgumentNotValidExceptionHandler(MethodArgumentNotValidException e) {
        List<ObjectError> allErrors = e.getBindingResult().getAllErrors();
        HashMap<String, Object> map = new HashMap<>();
        allErrors.forEach(error -> {
            FieldError fieldError = (FieldError) error;
            map.put(fieldError.getField(), fieldError.getDefaultMessage());
        });
        log.error("==========参数校验失败==========\n" + map);
        return new CommonResult<>(CommonCode.PARAM_ERROR, "参数校验失败: " + map, null);
    }

    @ExceptionHandler(BindException.class)
    @ResponseBody
    public CommonResult bindExceptionHandler(BindException e) {
        List<FieldError> fieldErrors = e.getBindingResult().getFieldErrors();
        Map<String, Object> map = new HashMap<>();
        for (FieldError fieldError : fieldErrors) {
            //字段名
            String field = fieldError.getField();
            //字段传值错误信息
            String message = fieldError.getDefaultMessage();
            map.put(field, message);
        }
        log.error("==========参数校验失败==========\n" + map);
        return new CommonResult<>(CommonCode.PARAM_ERROR, "参数校验失败: " + map, null);
    }


    /**
     * 详细错误信息打印
     *
     * @param request
     * @param exception
     */
    private void logError(HttpServletRequest request, Exception exception) {
        //换行符
        String lineSeparatorStr = System.getProperty("line.separator");

        StringBuilder exStr = new StringBuilder();
        StackTraceElement[] trace = exception.getStackTrace();
//        获取堆栈信息并输出为打印的形式
        for (StackTraceElement s : trace) {
            exStr.append("\tat " + s + "\r\n");
        }
        //打印error级别的堆栈日志
        log.error("访问地址：" + request.getRequestURL() + ",请求方法：" + request.getMethod() +
                ",远程地址：" + request.getRemoteAddr() + lineSeparatorStr +
                "错误堆栈信息如下:" + exception.toString() + lineSeparatorStr + exStr);
    }

}