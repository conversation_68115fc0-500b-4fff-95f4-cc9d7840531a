package com.jkga.wyyjs.model.vo;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * @Author：clyde
 * @Date：2025/2/10 9:16
 */
@Data
public class AreaManagerVO {

    private String id;
    @NotBlank(message = "小区不能为空")
    private String areaId;
    @NotBlank(message = "人员不能为空")
    private String userId;
    private String userName;
    private String phone;
    private String position;
    private String departmentId;
    private String intime;
    private Integer deleteStatus;

    private List<String> roleIds;
    private String faceImgUrl;

}
