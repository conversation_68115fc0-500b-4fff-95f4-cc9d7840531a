package com.jkga.wyyjs.service;

import cn.hutool.json.JSONUtil;
import com.jkga.wyyjs.mapper.*;
import com.jkga.wyyjs.model.CommonResult;
import com.jkga.wyyjs.model.entity.*;
import com.jkga.wyyjs.model.vo.*;
import com.jkga.wyyjs.utils.DateUtils;
import com.jkga.wyyjs.utils.SnowflakeUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 小区物业信息管理
 *
 * @Author：clyde
 * @Date：2025/2/26 16:18
 */
@Service
@Slf4j
public class PropertyInfoManagementService {

    @Autowired
    private AreaInfoConfigMapper areaInfoConfigMapper;


    public CommonResult editAreaArchives(AreaInfoConfigVO areaInfoConfigVO) {
        AreaInfoConfigEntity ex = areaInfoConfigMapper.getAreaInfoConfigByType(areaInfoConfigVO.getAreaId(), 1);
        String areaArchivesStr = JSONUtil.toJsonStr(areaInfoConfigVO.getAreaArchivesVO());
        if (ex != null) {
            AreaInfoConfigEntity areaInfoConfigEntity = new AreaInfoConfigEntity();
            areaInfoConfigEntity.setId(ex.getId());
            areaInfoConfigEntity.setAreaId(areaInfoConfigVO.getAreaId());
            areaInfoConfigEntity.setOperatorId(areaInfoConfigVO.getOperatorId());
            areaInfoConfigEntity.setOperatorTime(DateUtils.getTime());
            areaInfoConfigEntity.setContent(areaArchivesStr);
            areaInfoConfigMapper.updAreaInfoConfig(areaInfoConfigEntity);
        } else {
            AreaInfoConfigEntity areaInfoConfigEntity = new AreaInfoConfigEntity();
            String id = SnowflakeUtil.generateId();
            areaInfoConfigEntity.setId(id);
            areaInfoConfigEntity.setType(1);
            areaInfoConfigEntity.setAreaId(areaInfoConfigVO.getAreaId());
            areaInfoConfigEntity.setOperatorId(areaInfoConfigVO.getOperatorId());
            areaInfoConfigEntity.setOperatorTime(DateUtils.getTime());
            areaInfoConfigEntity.setContent(areaArchivesStr);
            areaInfoConfigMapper.addAreaInfoConfig(areaInfoConfigEntity);
        }
        return new CommonResult();
    }

    public CommonResult editPropertyArchives(AreaInfoConfigVO areaInfoConfigVO) {
        AreaInfoConfigEntity ex = areaInfoConfigMapper.getAreaInfoConfigByType(areaInfoConfigVO.getAreaId(), 2);
        String propertyArchivesStr = JSONUtil.toJsonStr(areaInfoConfigVO.getPropertyArchivesVO());
        if (ex != null) {
            AreaInfoConfigEntity areaInfoConfigEntity = new AreaInfoConfigEntity();
            areaInfoConfigEntity.setId(ex.getId());
            areaInfoConfigEntity.setAreaId(areaInfoConfigVO.getAreaId());
            areaInfoConfigEntity.setOperatorId(areaInfoConfigVO.getOperatorId());
            areaInfoConfigEntity.setOperatorTime(DateUtils.getTime());
            areaInfoConfigEntity.setContent(propertyArchivesStr);
            areaInfoConfigMapper.updAreaInfoConfig(areaInfoConfigEntity);
        } else {
            AreaInfoConfigEntity areaInfoConfigEntity = new AreaInfoConfigEntity();
            String id = SnowflakeUtil.generateId();
            areaInfoConfigEntity.setId(id);
            areaInfoConfigEntity.setType(2);
            areaInfoConfigEntity.setAreaId(areaInfoConfigVO.getAreaId());
            areaInfoConfigEntity.setOperatorId(areaInfoConfigVO.getOperatorId());
            areaInfoConfigEntity.setOperatorTime(DateUtils.getTime());
            areaInfoConfigEntity.setContent(propertyArchivesStr);
            areaInfoConfigMapper.addAreaInfoConfig(areaInfoConfigEntity);
        }
        return new CommonResult();
    }

    public CommonResult editPropertyFee(AreaInfoConfigVO areaInfoConfigVO) {
        AreaInfoConfigEntity ex = areaInfoConfigMapper.getAreaInfoConfigByType(areaInfoConfigVO.getAreaId(), 3);
        String propertyFeeStr = JSONUtil.toJsonStr(areaInfoConfigVO.getPropertyFeeVOList());
        if (ex != null) {
            AreaInfoConfigEntity areaInfoConfigEntity = new AreaInfoConfigEntity();
            areaInfoConfigEntity.setId(ex.getId());
            areaInfoConfigEntity.setAreaId(areaInfoConfigVO.getAreaId());
            areaInfoConfigEntity.setOperatorId(areaInfoConfigVO.getOperatorId());
            areaInfoConfigEntity.setOperatorTime(DateUtils.getTime());
            areaInfoConfigEntity.setContent(propertyFeeStr);
            areaInfoConfigMapper.updAreaInfoConfig(areaInfoConfigEntity);
        } else {
            AreaInfoConfigEntity areaInfoConfigEntity = new AreaInfoConfigEntity();
            String id = SnowflakeUtil.generateId();
            areaInfoConfigEntity.setId(id);
            areaInfoConfigEntity.setType(3);
            areaInfoConfigEntity.setAreaId(areaInfoConfigVO.getAreaId());
            areaInfoConfigEntity.setOperatorId(areaInfoConfigVO.getOperatorId());
            areaInfoConfigEntity.setOperatorTime(DateUtils.getTime());
            areaInfoConfigEntity.setContent(propertyFeeStr);
            areaInfoConfigMapper.addAreaInfoConfig(areaInfoConfigEntity);
        }
        return new CommonResult();
    }

    public CommonResult getAreaInfoConfig(String areaId) {
        List<AreaInfoConfigEntity> list = areaInfoConfigMapper.getAreaInfoConfig(areaId);
//        AreaInfoConfigVO areaInfoConfigVO = new AreaInfoConfigVO();
//        areaInfoConfigVO.setAreaId(areaId);
//        list.forEach(e -> {
//            if (e.getType() == 1) {
//                AreaArchivesVO areaArchivesVO = JSONUtil.toBean(e.getContent(),AreaArchivesVO.class);
//                areaInfoConfigVO.setAreaArchivesVO(areaArchivesVO);
//            } else if (e.getType() == 2) {
//                PropertyArchivesVO propertyArchivesVO = JSONUtil.toBean(e.getContent(),PropertyArchivesVO.class);
//                areaInfoConfigVO.setPropertyArchivesVO(propertyArchivesVO);
//            } else if (e.getType() == 3) {
//                List<PropertyFeeVO> propertyFeeVOList =JSONUtil.toList(e.getContent(),PropertyFeeVO.class);
//                areaInfoConfigVO.setPropertyFeeVOList(propertyFeeVOList);
//            }
//        });
        return new CommonResult(list);
    }

}
