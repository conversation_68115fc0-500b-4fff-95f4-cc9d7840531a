package com.jkga.wyyjs.test;

import com.jkga.wyyjs.utils.SnowflakeUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;

/**
 * ${description}
 *
 * <AUTHOR>
 * @date 2024/11/21 15:10
 */
@Slf4j
public class Test {

    public static void main(String[] args) throws InterruptedException, ExecutionException {

        ExecutorService executor = Executors.newFixedThreadPool(2);

        Future<String> future1 = executor.submit(() -> {
            // 线程1的任务
            Thread.sleep(1000 * 5);
            System.out.println("Task 1 is finished");
            return "Result 1";
        });

        Future<String> future2 = executor.submit(() -> {
            // 线程2的任务
            Thread.sleep(1000);
            System.out.println("Task 2 is finished");
            return "Result 2";
        });

        System.out.println("=====主进程=====");

        // 获取结果
        String result1 = future1.get();
        String result2 = future2.get();

        // 关闭线程池
        executor.shutdown();

        // 拼接结果并输出
        String finalResult = result1 + " and " + result2;
        System.out.println(finalResult);


//        Thread thread1 = new Thread(() -> {
//            // 线程1的任务
//            System.out.println("Thread 1 is running...");
//            // 模拟耗时操作
//            try {
//                Thread.sleep(1000 * 5);
//            } catch (InterruptedException e) {
//            }
//            System.out.println("Thread 1 finished.");
//        });
//
//        Thread thread2 = new Thread(() -> {
//            // 线程2的任务
//            System.out.println("Thread 2 is running...");
//            // 模拟耗时操作
//            try {
//                Thread.sleep(1000);
//            } catch (InterruptedException e) {
//            }
//            System.out.println("Thread 2 finished.");
//        });
//
//        thread1.start();
//        thread2.start();
//
//        // 主线程等待thread1和thread2完成
//        thread1.join();
//        thread2.join();
//
//        // 拼接结果并输出
//        String result = "Thread 1 and Thread 2 have completed.";
//        System.out.println(result);
    }

}
