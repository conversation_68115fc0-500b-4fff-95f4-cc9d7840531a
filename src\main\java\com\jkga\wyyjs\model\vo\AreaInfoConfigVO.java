package com.jkga.wyyjs.model.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.ArrayList;
import java.util.List;

/**
 * @Author：clyde
 * @Date：2025/2/26 9:01
 */
@Data
public class AreaInfoConfigVO {

    private String id;
    //1小区档案，2物业档案，3物业费收费
    private Integer type;
    @NotBlank(message = "小区不能为空")
    private String areaId;
    private String content;
    private String operatorId;
    private String operatorTime;

    private AreaArchivesVO areaArchivesVO;
    private PropertyArchivesVO propertyArchivesVO;
    private List<PropertyFeeVO> propertyFeeVOList;


}
