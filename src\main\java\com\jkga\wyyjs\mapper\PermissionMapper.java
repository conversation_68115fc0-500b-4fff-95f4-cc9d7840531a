package com.jkga.wyyjs.mapper;

import com.jkga.wyyjs.model.entity.PermissionEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author：clyde
 * @Date：2025/2/7 15:30
 */
@Mapper
public interface PermissionMapper {

    List<PermissionEntity> getPermissionList();

    List<PermissionEntity> getPermissionListByType(@Param("permissionType") Integer permissionType);

}
