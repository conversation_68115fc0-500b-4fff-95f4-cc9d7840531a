package com.jkga.wyyjs.model.vo;

import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 *
 *
 * @Author：clyde
 * @Date：2025/7/17 16:52
 *
 */

@Data
public class HandleSubletRoomToRenterVO {

    private String renterId;
    private String subletHouseId;
    private String liveStartDate;
    private String liveEndDate;
    private String remark;
    private String operatorId;
    private MultipartFile[] files;
    private List<SubletRenterRegisterVO> newRenterList;
    private String contractId;

}
