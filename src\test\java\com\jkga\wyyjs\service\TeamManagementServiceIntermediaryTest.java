package com.jkga.wyyjs.service;

import com.jkga.wyyjs.model.CommonResult;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.List;

/**
 * TeamManagementService中介权限测试类
 * 
 * @Author：clyde
 * @Date：2025/7/18
 */
@SpringBootTest
@ActiveProfiles("dev")
public class TeamManagementServiceIntermediaryTest {

    @Autowired
    private TeamManagementService teamManagementService;

    /**
     * 测试获取用户中介权限
     */
    @Test
    public void testGetUserIntermediaryPermissions() {
        String userId = "test_user_001";
        
        System.out.println("测试获取用户中介权限: userId=" + userId);
        
        try {
            CommonResult result = teamManagementService.getUserIntermediaryPermissions(userId);
            
            System.out.println("返回结果: " + result);
            System.out.println("状态码: " + result.getCode());
            System.out.println("消息: " + result.getMsg());
            
            if (result.getData() != null) {
                @SuppressWarnings("unchecked")
                List<String> placeIds = (List<String>) result.getData();
                System.out.println("中介权限场所数量: " + placeIds.size());
                for (String placeId : placeIds) {
                    System.out.println("场所ID: " + placeId);
                }
            } else {
                System.out.println("该用户没有中介权限");
            }
            
        } catch (Exception e) {
            System.err.println("测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试不存在的用户
     */
    @Test
    public void testGetUserIntermediaryPermissionsForNonExistentUser() {
        String userId = "non_existent_user";
        
        System.out.println("测试不存在用户的中介权限: userId=" + userId);
        
        try {
            CommonResult result = teamManagementService.getUserIntermediaryPermissions(userId);
            
            System.out.println("返回结果: " + result);
            System.out.println("状态码: " + result.getCode());
            System.out.println("消息: " + result.getMsg());
            
            if (result.getData() != null) {
                @SuppressWarnings("unchecked")
                List<String> placeIds = (List<String>) result.getData();
                System.out.println("中介权限场所数量: " + placeIds.size());
            }
            
        } catch (Exception e) {
            System.err.println("测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
