package com.jkga.wyyjs.constant;

/**
 * @Author：clyde
 * @Date：2025/7/14 14:57
 */
public enum UserFunctionEnum {

    UNKNOWN("UNKNOWN", "未知权限"),
    WYFWSH("WYFWSH", "物业房屋审核"),
    WYCLGL("WYCLGL","物业车辆管理"),
    WYTDGL("WYTDGL", "物业团队管理"),
    WYXXWH("WYXXWH", "物业信息维护"),
    WYFKDJ("WYFKDJ", "物业访客登记"),
    WYCLHC("WYCLHC", "物业车辆核查"),
    WYBZDJ("WYBZDJ", "物业帮助登记"),
    WYFWGL("WYFWGL", "物业房屋管理"),
    WYBMGL("WYBMGL", "物业部门管理"),
    WYJSGL("WYJSGL", "物业角色管理"),
    ZJTDGL("ZJTDGL", "中介团队管理"),
    ZJBMGL("ZJBMGL", "中介部门管理"),
    ZJJSGL("ZJJSGL", "中介角色管理"),
    WYRCGL("WYRCGL", "物业人车关联"),
    WYFGL("WYFGL", "物业费管理");

    private String code;

    private String describe;


    UserFunctionEnum(String code, String describe) {
        this.code = code;
        this.describe = describe;
    }


    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDescribe() {
        return describe;
    }

    public void setDescribe(String describe) {
        this.describe = describe;
    }

}
