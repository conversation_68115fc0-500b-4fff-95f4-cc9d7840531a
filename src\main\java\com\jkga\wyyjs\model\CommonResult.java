package com.jkga.wyyjs.model;

import lombok.Data;

/**
 * ${description}
 *
 * <AUTHOR>
 */
@Data
public class CommonResult<T> {

    private int code;
    private String msg;
    private T data;

    public CommonResult() {
        this(CommonCode.OK.getCode(), CommonCode.OK.getMessage(), null);
    }

    public CommonResult(T data) {
        this(CommonCode.OK.getCode(), CommonCode.OK.getMessage(), data);
    }

    public CommonResult(CommonCode code) {
        this(code.getCode(), code.getMessage(), null);
    }

    public CommonResult(CommonCode code, String msg, T data) {
        this(code.getCode(), msg, data);
    }

    public CommonResult(int code, String msg) {
        this(code, msg, null);
    }

    private CommonResult(int code, String msg, T data) {
        this.code = code;
        this.msg = msg;
        this.data = data;
    }

    public static CommonResult fail_msg(String msg) {
        return new CommonResult(CommonCode.FAIL.getCode(), msg, null);
    }
}
