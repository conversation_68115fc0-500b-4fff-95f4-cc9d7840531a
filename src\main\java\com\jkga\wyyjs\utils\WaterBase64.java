package com.jkga.wyyjs.utils;

import lombok.extern.slf4j.Slf4j;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.geom.Rectangle2D;
import java.awt.image.BufferedImage;
import java.io.*;
import java.util.Base64;

@Slf4j
public class WaterBase64 {

    // 水印透明度 
    private static float alpha = 0.9f;
    // 水印店铺名称字体
    private static Font shop_name_font = new Font("宋体", Font.PLAIN, 18);
    // 水印店铺二维码名称字体
    private static Font qrcode_name_font = new Font("宋体", Font.PLAIN, 16);
    // 水印企业名称字体
    private static Font company_name_font = new Font("宋体", Font.PLAIN, 30);
    // 水印企业二维码名称字体
    private static Font company_qrcode_name_font = new Font("宋体", Font.PLAIN, 28);
    // 提醒文字字体
    private static Font tip_font = new Font("宋体", Font.BOLD, 28);
    // 水印文字颜色
    private static Color color = Color.BLACK;
    private static Color tip_color = Color.RED;

    // 派出所名称字体
    private static Font police_name_font = new Font("宋体", Font.PLAIN, 28);
    // 派出所二维码名称字体
    private static Font police_qrcode_name_font = new Font("宋体", Font.PLAIN, 26);

    /**
     *      * 
     *      * @param alpha 
     *      *          水印透明度
     *      * @param positionWidth 
     *      *          水印横向位置
     *      * @param positionHeight 
     *      *          水印纵向位置
     *      * @param font 
     *      *          水印文字字体
     *      * @param color 
     *      *          水印文字颜色
     *      
     */
    public static void setImageMarkOptions(float alpha, Font shop_name_font, Font qrcode_name_font, Color color) {
        if (alpha != 0.0f) {
            WaterBase64.alpha = alpha;
        }
        if (shop_name_font != null) {
            WaterBase64.shop_name_font = shop_name_font;
        }
        if (qrcode_name_font != null) {
            WaterBase64.qrcode_name_font = qrcode_name_font;
        }
        if (color != null) {
            WaterBase64.color = color;
        }
    }

    public static String markImageByText(String shop_name_text, String qrcode_name_text, byte[] byteArr, String targerPath) {
        InputStream is = null;
        OutputStream os = null;
        try {
            is = new ByteArrayInputStream(byteArr);
            BufferedImage srcImg = ImageIO.read(is);
            //原始二维码图片的宽高
            int width = srcImg.getWidth();
            int height = srcImg.getHeight();
            //新图片的大小
            BufferedImage buffImg = new BufferedImage(width, height + 80, BufferedImage.TYPE_INT_RGB);

            // 1、得到画笔对象
            Graphics2D g = buffImg.createGraphics();
            //新图片空白处填充
            g.setPaint(Color.WHITE);
            g.fill(new Rectangle2D.Double(0, 0, width, 80));

            // 2、设置对线段的锯齿状边缘处理
            g.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
            // 3、设置文字抗锯齿
            g.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
            // 4、把二维码图片画进新图片
            g.drawImage(srcImg.getScaledInstance(width, height, Image.SCALE_SMOOTH), 0, 80, null);
            // 5、设置水印文字颜色
            g.setColor(color);
            // 6、设置水印文字Font
            g.setFont(shop_name_font);
            // 7、设置水印文字透明度
            g.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_ATOP, alpha));

            //8、计算店铺名字水印文字宽度
            FontMetrics fm = g.getFontMetrics(shop_name_font);
            int shop_name_textWidth = fm.stringWidth(shop_name_text);
            int shop_name_positionWidth = (width - shop_name_textWidth) / 2;
            // 9、写入水印文字 第一参数->设置的内容，后面两个参数->文字在图片上的坐标位置(x,y)
            g.drawString(shop_name_text, shop_name_positionWidth, 30);
            // 10、计算二维码名字水印文字宽度
            FontMetrics fm1 = g.getFontMetrics(qrcode_name_font);
            int qrcode_name_textWidth = fm1.stringWidth(qrcode_name_text);
            int qrcode_name_positionWidth = (width - qrcode_name_textWidth) / 2;
            // 11、设置水印文字Font
            g.setFont(qrcode_name_font);
            // 12、写入水印文字 第一参数->设置的内容，后面两个参数->文字在图片上的坐标位置(x,y)
            g.drawString(qrcode_name_text, qrcode_name_positionWidth, 60);

            // 13、释放资源
            g.dispose();

            //创建文件目录
//            File file = new File(targerPath);
//            File dir = file.getParentFile();
//            if (!dir.exists()) {
//                dir.mkdirs();
//            }
            //生成图片
//            ImageIO.write(buffImg, "PNG", file);

            //把图片转换为字节
            ByteArrayOutputStream bot = new ByteArrayOutputStream();

            //将二进制数据以png格式写进ByteArrayOutputStream
            ImageIO.write(buffImg, "PNG", bot);

            //将二进制数据以jpg格式写进ByteArrayOutputStream
//            JPEGImageEncoder encoder = JPEGCodec.createJPEGEncoder(bot);
//            encoder.encode(buffImg);

            byte[] rstByte = bot.toByteArray();
            String srcImgPath = new String(Base64.getEncoder().encode(rstByte));
            //log.info("图片完成添加水印图片");
            return srcImgPath;

        } catch (Exception e) {

        } finally {
            try {
                if (null != os) {
                    os.close();
                }
                if (null != is) {
                    is.close();
                }
            } catch (Exception e) {

            }
        }
        return targerPath;
    }

    public static String markImageByText(String shop_name_text, String qrcode_name_text, String base64img, String targerPath) {
        InputStream is = null;
        OutputStream os = null;
        byte[] b;
        try {
            b = Base64.getDecoder().decode(base64img);
            is = new ByteArrayInputStream(b);
            BufferedImage srcImg = ImageIO.read(is);
            //原始二维码图片的宽高
            int width = srcImg.getWidth();
            int height = srcImg.getHeight();
            //新图片的大小
            BufferedImage buffImg = new BufferedImage(width, height + 80, BufferedImage.TYPE_INT_RGB);

            // 1、得到画笔对象
            Graphics2D g = buffImg.createGraphics();
            //新图片空白处填充
            g.setPaint(Color.WHITE); // new Color(0,0,0,111);
            g.fill(new Rectangle2D.Double(0, 0, width, 80));

            // 2、设置对线段的锯齿状边缘处理
            g.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
            // 3、设置文字抗锯齿
            g.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
            //4把二维码图片画进新图片
            g.drawImage(srcImg.getScaledInstance(width, height, Image.SCALE_SMOOTH), 0, 80, null);
            // 5、设置水印文字颜色
            g.setColor(color);
            // 6、设置水印文字Font
            g.setFont(shop_name_font);
            // 7、设置水印文字透明度
            g.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_ATOP, alpha));

            //8、计算店铺名字水印文字宽度
            FontMetrics fm = g.getFontMetrics(shop_name_font);
            int shop_name_textWidth = fm.stringWidth(shop_name_text);
            int shop_name_positionWidth = (width - shop_name_textWidth) / 2;
            // 9、写入水印文字 第一参数->设置的内容，后面两个参数->文字在图片上的坐标位置(x,y)
            g.drawString(shop_name_text, shop_name_positionWidth, 30);

            // 10、计算二维码名字水印文字宽度
            FontMetrics fm1 = g.getFontMetrics(qrcode_name_font);
            int qrcode_name_textWidth = fm1.stringWidth(qrcode_name_text);
            int qrcode_name_positionWidth = (width - qrcode_name_textWidth) / 2;
            //11设置二维码名字水印文字样式
            g.setFont(qrcode_name_font);
            // 12、写入水印文字 第一参数->设置的内容，后面两个参数->文字在图片上的坐标位置(x,y)
            g.drawString(qrcode_name_text, qrcode_name_positionWidth, 60);

            // 13、释放资源
            g.dispose();

            //创建文件目录
//            File file = new File(targerPath);
//            File dir = file.getParentFile();
//            if (!dir.exists()) {
//                dir.mkdirs();
//            }
            //生成图片
//            ImageIO.write(buffImg, "PNG", file);

            //把图片转换为字节
            ByteArrayOutputStream bot = new ByteArrayOutputStream();

            //将二进制数据以png格式写进ByteArrayOutputStream
            ImageIO.write(buffImg, "PNG", bot);

            //将二进制数据以jpg格式写进ByteArrayOutputStream
//            JPEGImageEncoder encoder = JPEGCodec.createJPEGEncoder(bot);
//            encoder.encode(buffImg);

            byte[] rstByte = bot.toByteArray();
            String srcImgPath = Base64.getEncoder().encodeToString(rstByte);
            //log.info("图片完成添加水印图片");
            return srcImgPath;

        } catch (Exception e) {

        } finally {
            try {
                if (null != os) {
                    os.close();
                }
                if (null != is) {
                    is.close();
                }
            } catch (Exception e) {

            }
        }
        return targerPath;
    }


    public static String markImageByTextAndBackground(String shop_name_text, String qrcode_name_text, byte[] byteArr, String targerPath, String backgroudPath) {
        InputStream is = null;
        InputStream is2 = null;
        OutputStream os = null;
        try {
            //原二维码
            is = new ByteArrayInputStream(byteArr);
            BufferedImage srcImg = ImageIO.read(is);
            //原始二维码图片的宽高
            int width = srcImg.getWidth();
            int height = srcImg.getHeight();
            //新图片的大小
            BufferedImage buffImg = new BufferedImage(width, height + 80, BufferedImage.TYPE_INT_RGB);

            //新二维码图标
            File f2 = new File(backgroudPath);
            is2 = new FileInputStream(f2);
            BufferedImage srcImg2 = ImageIO.read(is2);

            // 1、得到画笔对象
            Graphics2D g = buffImg.createGraphics();
            //新图片空白处填充
            g.setPaint(Color.WHITE);
            g.fill(new Rectangle2D.Double(0, 0, width, 80));

            // 2、设置对线段的锯齿状边缘处理
            g.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
            // 3、设置文字抗锯齿
            g.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
            // 4、把二维码图片画进新图片
            g.drawImage(srcImg.getScaledInstance(width, height, Image.SCALE_SMOOTH), 0, 80, null);
            // 4、把图标覆盖原来图标
            g.drawImage(srcImg2.getScaledInstance(138, 138, Image.SCALE_SMOOTH), 71, 151, null);
            // 5、设置水印文字颜色
            g.setColor(color);
            // 6、设置水印文字Font
            g.setFont(shop_name_font);
            // 7、设置水印文字透明度
            g.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_ATOP, alpha));

            //8、计算店铺名字水印文字宽度
            FontMetrics fm = g.getFontMetrics(shop_name_font);
            int shop_name_textWidth = fm.stringWidth(shop_name_text);
            int shop_name_positionWidth = (width - shop_name_textWidth) / 2;
            // 9、写入水印文字 第一参数->设置的内容，后面两个参数->文字在图片上的坐标位置(x,y)
            g.drawString(shop_name_text, shop_name_positionWidth, 30);
            // 10、计算二维码名字水印文字宽度
            FontMetrics fm1 = g.getFontMetrics(qrcode_name_font);
            int qrcode_name_textWidth = fm1.stringWidth(qrcode_name_text);
            int qrcode_name_positionWidth = (width - qrcode_name_textWidth) / 2;
            // 11、设置水印文字Font
            g.setFont(qrcode_name_font);
            // 12、写入水印文字 第一参数->设置的内容，后面两个参数->文字在图片上的坐标位置(x,y)
            g.drawString(qrcode_name_text, qrcode_name_positionWidth, 60);

            // 13、释放资源
            g.dispose();


            //创建文件目录
//            File file = new File(targerPath);
//            File dir = file.getParentFile();
//            if (!dir.exists()) {
//                dir.mkdirs();
//            }
            //生成图片
//            ImageIO.write(buffImg, "PNG", file);

            //把图片转换为字节
            ByteArrayOutputStream bot = new ByteArrayOutputStream();

            //将二进制数据以png格式写进ByteArrayOutputStream
            ImageIO.write(buffImg, "PNG", bot);

            //将二进制数据以jpg格式写进ByteArrayOutputStream
//            JPEGImageEncoder encoder = JPEGCodec.createJPEGEncoder(bot);
//            encoder.encode(buffImg);

            byte[] rstByte = bot.toByteArray();
            String srcImgPath = new String(Base64.getEncoder().encode(rstByte));
            //log.info("图片完成添加水印图片");
            return srcImgPath;

        } catch (Exception e) {

        } finally {
            try {
                if (null != os) {
                    os.close();
                }
                if (null != is) {
                    is.close();
                }
            } catch (Exception e) {

            }
        }
        return targerPath;
    }

    public static String markImageByTextWithTip(String shop_name_text, String qrcode_name_text, byte[] byteArr, String targerPath, String tips) {
        InputStream is = null;
        OutputStream os = null;
        try {
            is = new ByteArrayInputStream(byteArr);
            BufferedImage srcImg = ImageIO.read(is);
            //原始二维码图片的宽高
            int width = srcImg.getWidth();
            int height = srcImg.getHeight();
            //新图片的大小
            BufferedImage buffImg = new BufferedImage(width, height + 240, BufferedImage.TYPE_INT_RGB);

            // 1、得到画笔对象
            Graphics2D g = buffImg.createGraphics();
            //新图片空白处填充
            g.setPaint(Color.WHITE);
            g.fill(new Rectangle2D.Double(0, 0, width, 120));
            g.fill(new Rectangle2D.Double(0, 120 + height, width, 120));

            // 2、设置对线段的锯齿状边缘处理
            g.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
            // 3、设置文字抗锯齿
            g.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
            // 4、把二维码图片画进新图片
            g.drawImage(srcImg.getScaledInstance(width, height, Image.SCALE_SMOOTH), 0, 120, null);
            // 5、设置水印文字颜色
            g.setColor(color);
            // 6、设置水印文字Font
            g.setFont(company_name_font);
            // 7、设置水印文字透明度
            g.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_ATOP, alpha));

            //8、计算店铺名字水印文字宽度
            FontMetrics fm = g.getFontMetrics(company_name_font);
            int shop_name_textWidth = fm.stringWidth(shop_name_text);
            int shop_name_positionWidth = (width - shop_name_textWidth) / 2;
            // 9、写入水印文字 第一参数->设置的内容，后面两个参数->文字在图片上的坐标位置(x,y)
            g.drawString(shop_name_text, shop_name_positionWidth, 40);
            // 10、计算二维码名字水印文字宽度
            FontMetrics fm1 = g.getFontMetrics(company_qrcode_name_font);
            int qrcode_name_textWidth = fm1.stringWidth(qrcode_name_text);
            int qrcode_name_positionWidth = (width - qrcode_name_textWidth) / 2;
            // 11、设置水印文字Font
            g.setFont(company_qrcode_name_font);
            // 12、写入水印文字 第一参数->设置的内容，后面两个参数->文字在图片上的坐标位置(x,y)
            g.drawString(qrcode_name_text, qrcode_name_positionWidth, 80);

            // 13、计算提醒文字1水印文字宽度
            FontMetrics fm2 = g.getFontMetrics(tip_font);

            String tip1_text = "经开公安提醒您";
            int tip1_textWidth = fm2.stringWidth(tip1_text);
            int tip1_positionWidth = (width - tip1_textWidth) / 2;
            int tip1_height = 120 + height + 40;

            String tip2_text = tips;
            int tip2_textWidth = fm2.stringWidth(tip2_text);
            int tip2_positionWidth = (width - tip2_textWidth) / 2;
            int tip2_height = 120 + height + 80;

            g.setPaint(tip_color);
            g.setFont(tip_font);

            // 14、写入水印文字 第一参数->设置的内容，后面两个参数->文字在图片上的坐标位置(x,y)
            g.drawString(tip1_text, tip1_positionWidth, tip1_height);
            g.drawString(tip2_text, tip2_positionWidth, tip2_height);

            // 13、释放资源
            g.dispose();

            //创建文件目录
//            File file = new File(targerPath);
//            File dir = file.getParentFile();
//            if (!dir.exists()) {
//                dir.mkdirs();
//            }
            //生成图片
//            ImageIO.write(buffImg, "PNG", file);

            //把图片转换为字节
            ByteArrayOutputStream bot = new ByteArrayOutputStream();

            //将二进制数据以png格式写进ByteArrayOutputStream
            ImageIO.write(buffImg, "PNG", bot);

            //将二进制数据以jpg格式写进ByteArrayOutputStream
//            JPEGImageEncoder encoder = JPEGCodec.createJPEGEncoder(bot);
//            encoder.encode(buffImg);

            byte[] rstByte = bot.toByteArray();
            String srcImgPath = new String(Base64.getEncoder().encode(rstByte));
            //log.info("图片完成添加水印图片");
            return srcImgPath;

        } catch (Exception e) {

        } finally {
            try {
                if (null != os) {
                    os.close();
                }
                if (null != is) {
                    is.close();
                }
            } catch (Exception e) {

            }
        }
        return targerPath;
    }

    public static void batchMarkImageByTextWithTip(String shop_name_text, String qrcode_name_text, byte[] byteArr, String targerPath, String tips) {
        InputStream is = null;
        OutputStream os = null;
        try {
            is = new ByteArrayInputStream(byteArr);
            BufferedImage srcImg = ImageIO.read(is);
            //原始二维码图片的宽高
            int width = srcImg.getWidth();
            int height = srcImg.getHeight();
            //新图片的大小
            BufferedImage buffImg = new BufferedImage(width, height + 240, BufferedImage.TYPE_INT_RGB);

            // 1、得到画笔对象
            Graphics2D g = buffImg.createGraphics();
            //新图片空白处填充
            g.setPaint(Color.WHITE);
            g.fill(new Rectangle2D.Double(0, 0, width, 120));
            g.fill(new Rectangle2D.Double(0, 120 + height, width, 120));

            // 2、设置对线段的锯齿状边缘处理
            g.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
            // 3、设置文字抗锯齿
            g.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
            // 4、把二维码图片画进新图片
            g.drawImage(srcImg.getScaledInstance(width, height, Image.SCALE_SMOOTH), 0, 120, null);
            // 5、设置水印文字颜色
            g.setColor(color);
            // 6、设置水印文字Font
            g.setFont(company_name_font);
            // 7、设置水印文字透明度
            g.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_ATOP, alpha));

            //8、计算店铺名字水印文字宽度
            FontMetrics fm = g.getFontMetrics(company_name_font);
            int shop_name_textWidth = fm.stringWidth(shop_name_text);
            int shop_name_positionWidth = (width - shop_name_textWidth) / 2;
            // 9、写入水印文字 第一参数->设置的内容，后面两个参数->文字在图片上的坐标位置(x,y)
            g.drawString(shop_name_text, shop_name_positionWidth, 40);
            // 10、计算二维码名字水印文字宽度
            FontMetrics fm1 = g.getFontMetrics(company_qrcode_name_font);
            int qrcode_name_textWidth = fm1.stringWidth(qrcode_name_text);
            int qrcode_name_positionWidth = (width - qrcode_name_textWidth) / 2;
            // 11、设置水印文字Font
            g.setFont(company_qrcode_name_font);
            // 12、写入水印文字 第一参数->设置的内容，后面两个参数->文字在图片上的坐标位置(x,y)
            g.drawString(qrcode_name_text, qrcode_name_positionWidth, 80);

            // 13、计算提醒文字1水印文字宽度
            FontMetrics fm2 = g.getFontMetrics(tip_font);

            String tip1_text = "经开公安提醒您";
            int tip1_textWidth = fm2.stringWidth(tip1_text);
            int tip1_positionWidth = (width - tip1_textWidth) / 2;
            int tip1_height = 120 + height + 40;

            String tip2_text = tips;
            int tip2_textWidth = fm2.stringWidth(tip2_text);
            int tip2_positionWidth = (width - tip2_textWidth) / 2;
            int tip2_height = 120 + height + 80;

            g.setPaint(tip_color);
            g.setFont(tip_font);

            // 14、写入水印文字 第一参数->设置的内容，后面两个参数->文字在图片上的坐标位置(x,y)
            g.drawString(tip1_text, tip1_positionWidth, tip1_height);
            g.drawString(tip2_text, tip2_positionWidth, tip2_height);

            // 13、释放资源
            g.dispose();

            //创建文件目录
            File file = new File(targerPath);
            File dir = file.getParentFile();
            if (!dir.exists()) {
                dir.mkdirs();
            }
//            生成图片
            ImageIO.write(buffImg, "PNG", file);



        } catch (Exception e) {

        } finally {
            try {
                if (null != os) {
                    os.close();
                }
                if (null != is) {
                    is.close();
                }
            } catch (Exception e) {

            }
        }
    }


    public static String markImageByTextTemp(String shop_name_text, String qrcode_name_text, byte[] byteArr, String targerPath) {
        InputStream is = null;
        OutputStream os = null;
        try {
            is = new ByteArrayInputStream(byteArr);
            BufferedImage srcImg = ImageIO.read(is);
            //原始二维码图片的宽高
            int width = srcImg.getWidth();
            int height = srcImg.getHeight();
            //新图片的大小
            BufferedImage buffImg = new BufferedImage(width, height + 120, BufferedImage.TYPE_INT_RGB);

            // 1、得到画笔对象
            Graphics2D g = buffImg.createGraphics();
            //新图片空白处填充
            g.setPaint(Color.WHITE);
            g.fill(new Rectangle2D.Double(0, 0, width, 120));

            // 2、设置对线段的锯齿状边缘处理
            g.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
            // 3、设置文字抗锯齿
            g.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
            // 4、把二维码图片画进新图片
            g.drawImage(srcImg.getScaledInstance(width, height, Image.SCALE_SMOOTH), 0, 120, null);
            // 5、设置水印文字颜色
            g.setColor(color);
            // 6、设置水印文字Font
            g.setFont(police_name_font);
            // 7、设置水印文字透明度
            g.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_ATOP, alpha));

            //8、计算店铺名字水印文字宽度
            FontMetrics fm = g.getFontMetrics(police_name_font);
            int shop_name_textWidth = fm.stringWidth(shop_name_text);
            int shop_name_positionWidth = (width - shop_name_textWidth) / 2;
            // 9、写入水印文字 第一参数->设置的内容，后面两个参数->文字在图片上的坐标位置(x,y)
            g.drawString(shop_name_text, shop_name_positionWidth, 50);
            // 10、计算二维码名字水印文字宽度
            FontMetrics fm1 = g.getFontMetrics(police_qrcode_name_font);
            int qrcode_name_textWidth = fm1.stringWidth(qrcode_name_text);
            int qrcode_name_positionWidth = (width - qrcode_name_textWidth) / 2;
            // 11、设置水印文字Font
            g.setFont(police_qrcode_name_font);
            // 12、写入水印文字 第一参数->设置的内容，后面两个参数->文字在图片上的坐标位置(x,y)
            g.drawString(qrcode_name_text, qrcode_name_positionWidth, 90);

            // 13、释放资源
            g.dispose();

            //创建文件目录
            File file = new File(targerPath);
            File dir = file.getParentFile();
            if (!dir.exists()) {
                dir.mkdirs();
            }
            //生成图片
            ImageIO.write(buffImg, "PNG", file);

            //把图片转换为字节
            ByteArrayOutputStream bot = new ByteArrayOutputStream();

            //将二进制数据以png格式写进ByteArrayOutputStream
            ImageIO.write(buffImg, "PNG", bot);

            //将二进制数据以jpg格式写进ByteArrayOutputStream
//            JPEGImageEncoder encoder = JPEGCodec.createJPEGEncoder(bot);
//            encoder.encode(buffImg);

            byte[] rstByte = bot.toByteArray();
            String srcImgPath = new String(Base64.getEncoder().encode(rstByte));
            //log.info("图片完成添加水印图片");
            return srcImgPath;

        } catch (Exception e) {

        } finally {
            try {
                if (null != os) {
                    os.close();
                }
                if (null != is) {
                    is.close();
                }
            } catch (Exception e) {

            }
        }
        return targerPath;
    }

    public static void saveImgTemp(byte[] byteArr, String targerPath) {
        try {
            // 创建BufferedImage对象
            BufferedImage image = ImageIO.read(new ByteArrayInputStream(byteArr));

            //创建文件目录
            File file = new File(targerPath);
            File dir = file.getParentFile();
            if (!dir.exists()) {
                dir.mkdirs();
            }

            // 将BufferedImage对象写入到文件中
            ImageIO.write(image, "PNG", file);

        } catch (IOException e) {
            e.printStackTrace();
        }

    }


}
