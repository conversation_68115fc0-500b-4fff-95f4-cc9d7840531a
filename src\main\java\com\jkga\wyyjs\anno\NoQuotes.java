package com.jkga.wyyjs.anno;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 特殊字符判断
 *
 * <AUTHOR>
 */
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = NoQuotesValidator.class)
public @interface NoQuotes {
    String message() default "不能包含特殊字符";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}