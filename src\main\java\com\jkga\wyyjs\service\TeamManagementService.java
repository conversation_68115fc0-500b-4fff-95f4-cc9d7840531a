package com.jkga.wyyjs.service;

import com.jkga.wyyjs.mapper.*;
import com.jkga.wyyjs.model.CommonCode;
import com.jkga.wyyjs.model.CommonResult;
import com.jkga.wyyjs.model.entity.*;
import com.jkga.wyyjs.utils.SnowflakeUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 团队管理服务类
 * 
 * @Author：clyde
 * @Date：2025/7/18
 */
@Service
@Slf4j
public class TeamManagementService {

    @Autowired
    private AdminManagerMapper adminManagerMapper;

    @Autowired
    private AdminDepartmentMapper adminDepartmentMapper;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private RoleMapper roleMapper;

    @Autowired
    private UserRoleMapper userRoleMapper;

    @Autowired
    private RolePermissionMapper rolePermissionMapper;

    @Autowired
    private PermissionMapper permissionMapper;

    /**
     * 获取当前团队人员
     */
    public CommonResult getTeamMembers(String managerId, Integer type) {
        try {
            // 获取团队成员列表
            List<AdminManagerEntity> members = adminManagerMapper.getTeamMembers(managerId, type);

            // 获取部门列表
            List<AdminDepartmentEntity> departments = adminDepartmentMapper.getDepartmentList(managerId, type);

            // 按部门分组
            Map<String, List<Map<String, Object>>> departmentGroups = new HashMap<>();

            // 处理有部门的成员
            for (AdminManagerEntity member : members) {
                String departmentId = member.getDepartmentId();
                String departmentName;

                if (departmentId != null) {
                    // 查找部门名称
                    Optional<AdminDepartmentEntity> dept = departments.stream()
                            .filter(d -> departmentId.equals(d.getId()))
                            .findFirst();
                    departmentName = dept.map(AdminDepartmentEntity::getName).orElse("未知部门");
                } else {
                    departmentName = "无归属部门";
                }

                // 获取用户详细信息
                UserEntity user = userMapper.selectUserById(member.getUserId());

                Map<String, Object> memberInfo = new HashMap<>();
                if (user != null) {
                    memberInfo.put("face_img_url", user.getFaceImgUrl());
                    memberInfo.put("name", user.getName());
                    memberInfo.put("phone", user.getPhone());
                }
                memberInfo.put("user_id", member.getUserId());
                memberInfo.put("position", member.getPosition());
                memberInfo.put("department_id", member.getDepartmentId());
                if (type == 0) {
                    memberInfo.put("manager_id", member.getAreaId());
                } else {
                    memberInfo.put("manager_id", member.getPlaceId());
                }
                departmentGroups.computeIfAbsent(departmentName, k -> new ArrayList<>()).add(memberInfo);
            }

            // 构建返回结果
            List<Map<String, Object>> result = new ArrayList<>();
            for (Map.Entry<String, List<Map<String, Object>>> entry : departmentGroups.entrySet()) {
                Map<String, Object> departmentGroup = new HashMap<>();
                departmentGroup.put("department_name", entry.getKey());
                departmentGroup.put("members", entry.getValue());

                // 查找部门ID
                if (!"无归属部门".equals(entry.getKey())) {
                    departments.stream()
                            .filter(d -> entry.getKey().equals(d.getName()))
                            .findFirst()
                            .ifPresent(d -> {
                                departmentGroup.put("department_id", d.getId());
                                departmentGroup.put("department_name", d.getName());
                            });
                }

                result.add(departmentGroup);
            }

            return new CommonResult(result);

        } catch (Exception e) {
            log.error("获取团队成员失败", e);
            return new CommonResult(CommonCode.SYSTEM_ERROR, "获取团队成员失败: " + e.getMessage(), null);
        }
    }

    /**
     * 新增部门
     */
    @Transactional
    public CommonResult addDepartment(String managerId, Integer type, String name) {
        try {
            // 检查部门名称是否已存在
            String areaId = (type == 0) ? managerId : null;
            String placeId = (type == 1) ? managerId : null;

            AdminDepartmentEntity existingDept = adminDepartmentMapper.getDepartmentByName(name, areaId, placeId, type);
            if (existingDept != null) {
                return new CommonResult(CommonCode.DATA_EXIST, "部门名称已存在", null);
            }

            // 创建新部门
            AdminDepartmentEntity newDept = new AdminDepartmentEntity();
            newDept.setId(SnowflakeUtil.generateId());
            newDept.setName(name);
            newDept.setType(type);
            newDept.setDeleteStatus(0);

            if (type == 0) {
                newDept.setAreaId(managerId);
            } else {
                newDept.setPlaceId(managerId);
            }

            adminDepartmentMapper.insertAdminDepartment(newDept);

            return new CommonResult(newDept);

        } catch (Exception e) {
            log.error("新增部门失败", e);
            return new CommonResult(CommonCode.SYSTEM_ERROR, "新增部门失败: " + e.getMessage(), null);
        }
    }

    /**
     * 修改部门名称
     */
    @Transactional
    public CommonResult updateDepartmentName(String departmentId, String name) {
        try {
            // 获取部门信息
            AdminDepartmentEntity department = adminDepartmentMapper.getDepartmentById(departmentId);
            if (department == null) {
                return new CommonResult(CommonCode.PARAM_ERROR, "部门不存在", null);
            }

            // 检查新名称是否已存在
            AdminDepartmentEntity existingDept = adminDepartmentMapper.getDepartmentByName(
                    name, department.getAreaId(), department.getPlaceId(), department.getType());
            if (existingDept != null && !departmentId.equals(existingDept.getId())) {
                return new CommonResult(CommonCode.DATA_EXIST, "部门名称已存在", null);
            }

            // 更新部门名称
            adminDepartmentMapper.updateDepartmentName(departmentId, name);

            return new CommonResult(CommonCode.OK);

        } catch (Exception e) {
            log.error("修改部门名称失败", e);
            return new CommonResult(CommonCode.SYSTEM_ERROR, "修改部门名称失败: " + e.getMessage(), null);
        }
    }

    /**
     * 删除部门
     */
    @Transactional
    public CommonResult deleteDepartment(String departmentId) {
        try {
            // 删除部门
            adminDepartmentMapper.delDepartment(departmentId);

            // 将该部门下的员工的department_id设置为null
            adminManagerMapper.clearDepartmentByDepartmentId(departmentId);

            return new CommonResult(CommonCode.OK);

        } catch (Exception e) {
            log.error("删除部门失败", e);
            return new CommonResult(CommonCode.SYSTEM_ERROR, "删除部门失败: " + e.getMessage(), null);
        }
    }

    /**
     * 获取部门列表
     */
    public CommonResult getDepartmentList(String managerId, Integer type) {
        try {
            List<AdminDepartmentEntity> departments = adminDepartmentMapper.getDepartmentList(managerId, type);

            return new CommonResult(departments);

        } catch (Exception e) {
            log.error("获取部门列表失败", e);
            return new CommonResult(CommonCode.SYSTEM_ERROR, "获取部门列表失败: " + e.getMessage(), null);
        }
    }

    /**
     * 获取角色列表
     */
    public CommonResult getRoleList(String managerId, Integer type) {
        try {
            List<RoleEntity> roles = roleMapper.getRoleListByCondition(managerId, type);

            return new CommonResult(roles);

        } catch (Exception e) {
            log.error("获取角色列表失败", e);
            return new CommonResult(CommonCode.SYSTEM_ERROR, "获取角色列表失败: " + e.getMessage(), null);
        }
    }

    /**
     * 修改用户的角色、部门、职位
     */
    @Transactional
    public CommonResult updateMember(String managerId, Integer type, String userId,
                                     String roleId, String departmentId, String position) {
        try {
            // 更新admin_manager表中的部门和职位
            String areaId = (type == 0) ? managerId : null;
            String placeId = (type == 1) ? managerId : null;

            AdminManagerEntity manager = adminManagerMapper.getAdminManagerByCondition(userId, areaId, placeId, type);
            if (manager != null) {
                manager.setDepartmentId(departmentId);
                manager.setPosition(position);
                adminManagerMapper.updateAdminManager(manager);
            }

            // 更新user_role表中的角色
            userRoleMapper.updateUserRole(userId, managerId, type, roleId);

            return new CommonResult(CommonCode.OK);

        } catch (Exception e) {
            log.error("修改用户信息失败", e);
            return new CommonResult(CommonCode.SYSTEM_ERROR, "修改用户信息失败: " + e.getMessage(), null);
        }
    }

    /**
     * 删除人员
     */
    @Transactional
    public CommonResult deleteMember(String managerId, Integer type, String userId) {
        try {
            // 删除admin_manager表中的记录
            String areaId = (type == 0) ? managerId : null;
            String placeId = (type == 1) ? managerId : null;

            adminManagerMapper.delAdminManagerByCondition(userId, areaId, placeId, type);

            // 删除user_role表中的记录
            userRoleMapper.delUserRoleByCondition(userId, managerId, type);

            return new CommonResult(CommonCode.OK);

        } catch (Exception e) {
            log.error("删除人员失败", e);
            return new CommonResult(CommonCode.SYSTEM_ERROR, "删除人员失败: " + e.getMessage(), null);
        }
    }

    /**
     * 获取当前用户的中介权限
     */
    public CommonResult getUserIntermediaryPermissions(String userId) {
        try {
            // 查询用户在admin_manager表中type=1且delete_status=0的记录
            List<AdminManagerEntity> intermediaryManagers = adminManagerMapper.getIntermediaryManagersByUserId(userId);

            // 提取place_id列表
            List<String> placeIds = new ArrayList<>();
            for (AdminManagerEntity manager : intermediaryManagers) {
                if (manager.getPlaceId() != null) {
                    placeIds.add(manager.getPlaceId());
                }
            }

            return new CommonResult(placeIds);

        } catch (Exception e) {
            log.error("获取用户中介权限失败", e);
            return new CommonResult(CommonCode.SYSTEM_ERROR, "获取用户中介权限失败: " + e.getMessage(), null);
        }
    }

    /**
     * 新增角色
     */
    @Transactional
    public CommonResult addRole(String roleName, Integer roleType, String manageId) {
        try {
            // 检查角色名称是否已存在
            String areaId = (roleType == 0) ? manageId : null;
            String placeId = (roleType == 1) ? manageId : null;

            RoleEntity existingRole = roleMapper.getRoleByCondition(roleName, areaId, placeId, roleType);
            if (existingRole != null) {
                return new CommonResult(CommonCode.DATA_EXIST, "角色名称已存在", null);
            }

            // 创建新角色
            RoleEntity newRole = new RoleEntity();
            newRole.setId(SnowflakeUtil.generateId());
            newRole.setRoleName(roleName);
            newRole.setRoleType(roleType);
            newRole.setDeleteStatus(0);

            if (roleType == 0) {
                newRole.setAreaId(manageId);
            } else {
                newRole.setPlaceId(manageId);
            }

            roleMapper.insertAreaRole(newRole);

            return new CommonResult(newRole);

        } catch (Exception e) {
            log.error("新增角色失败", e);
            return new CommonResult(CommonCode.SYSTEM_ERROR, "新增角色失败: " + e.getMessage(), null);
        }
    }

    /**
     * 删除角色
     */
    @Transactional
    public CommonResult deleteRole(String id) {
        try {
            roleMapper.deleteRole(id);
            return new CommonResult(CommonCode.OK);

        } catch (Exception e) {
            log.error("删除角色失败", e);
            return new CommonResult(CommonCode.SYSTEM_ERROR, "删除角色失败: " + e.getMessage(), null);
        }
    }

    /**
     * 获取角色权限
     */
    public CommonResult getRolePermissions(String roleId) {
        try {
            List<Map<String, Object>> rolePermissions = rolePermissionMapper.getRolePermissionsWithDetails(roleId);
            return new CommonResult(rolePermissions);

        } catch (Exception e) {
            log.error("获取角色权限失败", e);
            return new CommonResult(CommonCode.SYSTEM_ERROR, "获取角色权限失败: " + e.getMessage(), null);
        }
    }

    /**
     * 获取所有功能权限
     */
    public CommonResult getAllPermissions(Integer type) {
        try {
            List<PermissionEntity> permissions = permissionMapper.getPermissionListByType(type);
            return new CommonResult(permissions);

        } catch (Exception e) {
            log.error("获取所有功能权限失败", e);
            return new CommonResult(CommonCode.SYSTEM_ERROR, "获取所有功能权限失败: " + e.getMessage(), null);
        }
    }

    /**
     * 保存当前角色的权限
     */
    @Transactional
    public CommonResult saveRolePermissions(List<Map<String, String>> permissions) {
        try {
            if (permissions == null || permissions.isEmpty()) {
                return new CommonResult(CommonCode.PARAM_ERROR, "权限列表不能为空", null);
            }

            String roleId = permissions.get(0).get("role_id");
            if (roleId == null) {
                return new CommonResult(CommonCode.PARAM_ERROR, "角色ID不能为空", null);
            }

            // 获取当前角色已有的权限
            List<RolePermissionEntity> existingPermissions = rolePermissionMapper.getRolePermissionList(roleId);

            // 提取新权限列表中的permission_id
            Set<String> newPermissionIds = new HashSet<>();
            for (Map<String, String> permission : permissions) {
                String permissionId = permission.get("permission_id");
                if (permissionId != null) {
                    newPermissionIds.add(permissionId);
                }
            }

            // 删除不在新列表中的权限
            for (RolePermissionEntity existing : existingPermissions) {
                if (!newPermissionIds.contains(existing.getPermissionId())) {
                    rolePermissionMapper.deleteRolePermission(existing.getId());
                }
            }

            // 添加新的权限
            Set<String> existingPermissionIds = new HashSet<>();
            for (RolePermissionEntity existing : existingPermissions) {
                existingPermissionIds.add(existing.getPermissionId());
            }

            for (Map<String, String> permission : permissions) {
                String permissionId = permission.get("permission_id");
                if (permissionId != null && !existingPermissionIds.contains(permissionId)) {
                    RolePermissionEntity newRolePermission = new RolePermissionEntity();
                    newRolePermission.setId(SnowflakeUtil.generateId());
                    newRolePermission.setRoleId(roleId);
                    newRolePermission.setPermissionId(permissionId);
                    newRolePermission.setDeleteStatus(0);

                    rolePermissionMapper.insertRolePermission(newRolePermission);
                }
            }

            return new CommonResult(CommonCode.OK);

        } catch (Exception e) {
            log.error("保存角色权限失败", e);
            return new CommonResult(CommonCode.SYSTEM_ERROR, "保存角色权限失败: " + e.getMessage(), null);
        }
    }

    /**
     * 获取当前用户的角色信息
     */
    public CommonResult getUserRoles(Integer type, String userId) {
        try {
            List<Map<String, Object>> userRoles = userRoleMapper.getUserRolesWithDetails(type, userId);
            return new CommonResult(userRoles);

        } catch (Exception e) {
            log.error("获取用户角色信息失败", e);
            return new CommonResult(CommonCode.SYSTEM_ERROR, "获取用户角色信息失败: " + e.getMessage(), null);
        }
    }
}
