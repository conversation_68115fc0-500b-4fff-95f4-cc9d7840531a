<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jkga.wyyjs.mapper.RoleMapper">

    <resultMap id="AreaRoleResultMap" type="com.jkga.wyyjs.model.entity.RoleEntity">
        <id     property="id"     column="id"     />
        <result property="roleName"   column="role_name"   />
        <result property="roleDesc"  column="role_desc"   />
        <result property="createTime"   column="create_time"   />
        <result property="updateTime"   column="update_time"   />
        <result property="deleteStatus"   column="delete_status"   />
        <result property="areaId"   column="area_id"   />
    </resultMap>

    <select id="getAreaRoleByName" resultMap="AreaRoleResultMap">
        SELECT * from "role" where "area_id" = #{areaId} and "role_name" = #{roleName} and "delete_status" = 0
    </select>

    <insert id="insertAreaRole" parameterType="com.jkga.wyyjs.model.entity.RoleEntity">
        insert into "role"(
            "id",
            "role_name",
            "role_desc",
            "create_time",
            "delete_status",
            "area_id"
            )values(
            #{id},
            #{roleName},
            #{roleDesc},
            #{createTime},
            #{deleteStatus},
            #{areaId}
        )
    </insert>

    <select id="getAreaRoleList" resultMap="AreaRoleResultMap">
        SELECT * from "role" where "area_id" = #{areaId}  and "delete_status" = 0
    </select>

    <select id="getRoleById" resultMap="AreaRoleResultMap">
        SELECT * from "role" where "id" = #{roleId} and "delete_status" = 0
    </select>

</mapper>