<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jkga.wyyjs.mapper.UserRoleMapper">

    <resultMap id="UserRoleResultMap" type="com.jkga.wyyjs.model.entity.UserRoleEntity">
        <id     property="id"     column="id"     />
        <result property="areaId"   column="area_id"   />
        <result property="userId"   column="user_id"   />
        <result property="roleId"   column="role_id"   />
        <result property="createTime"   column="create_time"   />
        <result property="deleteStatus"   column="delete_status"   />
        <result property="placeId"   column="place_id"   />
        <result property="type"   column="type"   />
    </resultMap>

    <insert id="batchInsertUserRole">
        insert into "user_role"("id", "area_id", "user_id", "role_id", "delete_status", "create_time") VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.id}, #{item.areaId}, #{item.userId}, #{item.roleId}, #{item.deleteStatus}, #{item.createTime})
        </foreach>
    </insert>

    <delete id="delUserRoleByArea">
        delete from "user_role" where "area_id" = #{areaId} and "user_id" = #{userId}
    </delete>

    <select id="getAreaManagerUserRole" resultMap="UserRoleResultMap">
        select * from "user_role" where "area_id" = #{areaId} and "user_id" = #{userId} and "delete_status" = 0
    </select>

    <insert id="insertUserRole" parameterType="com.jkga.wyyjs.model.entity.UserRoleEntity">
        insert into "user_role"(
            "id",
            "user_id",
            "role_id",
            "delete_status",
            "create_time",
            "area_id",
            "place_id",
            "type"
            )values(
            #{id},
            #{userId},
            #{roleId},
            #{deleteStatus},
            NOW(),
            #{areaId},
            #{placeId},
            #{type}
        )
    </insert>

    <update id="delUserRole">
        update "user_role"
        set
        "delete_status" = 1
        where "id" = #{id}
    </update>

    <select id="getUserRoleByUserId" resultMap="UserRoleResultMap">
        select * from "user_role" where "user_id" = #{userId} and "delete_status" = 0
    </select>

    <select id="getUserRoleByCondition" resultMap="UserRoleResultMap">
        select * from "user_role"
        where "user_id" = #{userId}
        and "type" = #{type}
        and "delete_status" = 0
        <if test="type == 0">
            AND "area_id" = #{managerId}
        </if>
        <if test="type == 1">
            AND "place_id" = #{managerId}
        </if>
    </select>

    <update id="delUserRoleByCondition">
        update "user_role"
        set "delete_status" = 1
        where "user_id" = #{userId}
        and "type" = #{type}
        <if test="type == 0">
            AND "area_id" = #{managerId}
        </if>
        <if test="type == 1">
            AND "place_id" = #{managerId}
        </if>
    </update>

    <update id="updateUserRole">
        update "user_role"
        set "role_id" = #{roleId}
        where "user_id" = #{userId}
        and "type" = #{type}
        and "delete_status" = 0
        <if test="type == 0">
            AND "area_id" = #{managerId}
        </if>
        <if test="type == 1">
            AND "place_id" = #{managerId}
        </if>
    </update>

</mapper>