package com.jkga.wyyjs.model.vo;

import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotNull;

/**
 * ${description}
 *
 * <AUTHOR>
 * @date 2025/2/12 13:43
 */
@Data
public class DeclareForHouseMemberVO {

    private String userId;
    private String name;
    //    private String phone;
//    private String identity;
//    private String identityType;
//    private MultipartFile faceImgFile;
    private String faceImgUrl;
    private String creatorId;
    private String areaId;
    private String houseId;
    private String buildingName;
    private Integer type;
    private String rentStartTime;
    private String rentEndTime;

}
