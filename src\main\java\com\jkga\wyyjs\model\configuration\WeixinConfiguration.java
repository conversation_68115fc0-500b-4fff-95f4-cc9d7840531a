package com.jkga.wyyjs.model.configuration;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * ${description}
 *
 * <AUTHOR>
 */
@Component
@ConfigurationProperties(prefix = "weixin")
@Data
public class WeixinConfiguration {

    private String appId;
    private String appSecret;
    private String openidUrl;
    private String tokenUrl;
    private String unlimitedUrl;

}
