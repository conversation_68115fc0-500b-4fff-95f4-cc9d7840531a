package com.jkga.wyyjs.mapper;

import com.jkga.wyyjs.model.entity.RolePermissionEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author：clyde
 * @Date：2025/2/10 11:09
 */
@Mapper
public interface RolePermissionMapper {

    boolean delAreaRolePermission(@Param("roleId") String roleId);

    boolean batchInsertAreaRolePermission(@Param("list") List<RolePermissionEntity> list);

    List<RolePermissionEntity> getRolePermissionList(@Param("roleId") String roleId);

    boolean insertRolePermission(RolePermissionEntity rolePermissionEntity);
}
