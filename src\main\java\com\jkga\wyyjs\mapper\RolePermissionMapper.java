package com.jkga.wyyjs.mapper;

import com.jkga.wyyjs.model.entity.RolePermissionEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * @Author：clyde
 * @Date：2025/2/10 11:09
 */
@Mapper
public interface RolePermissionMapper {

    boolean delAreaRolePermission(@Param("roleId") String roleId);

    boolean batchInsertAreaRolePermission(@Param("list") List<RolePermissionEntity> list);

    List<RolePermissionEntity> getRolePermissionList(@Param("roleId") String roleId);

    boolean insertRolePermission(RolePermissionEntity rolePermissionEntity);

    /**
     * 获取角色权限详情（包含权限信息）
     */
    List<Map<String, Object>> getRolePermissionsWithDetails(@Param("roleId") String roleId);

    /**
     * 删除角色权限
     */
    boolean deleteRolePermission(@Param("id") String id);
}
