package com.jkga.wyyjs.service;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

/**
 * UserManagerService测试类
 * 
 * @Author：clyde
 * @Date：2025/7/18
 */
@SpringBootTest
@ActiveProfiles("dev")
public class UserManagerServiceTest {

    @Autowired
    private UserManagerService userManagerService;

    /**
     * 测试物业管理员添加
     */
    @Test
    public void testAddPropertyManager() {
        JSONObject data = new JSONObject();
        data.put("type", 0);
        data.put("area_id", "123456");
        data.put("id_card", "330400199001011234");
        data.put("user_name", "TEST");
        data.put("phone", "18812341234");
        data.put("position", "管理员");
        data.put("status", "0");
        data.put("update_user", "处理人");

        System.out.println("测试数据: " + JSONUtil.toJsonStr(data));
        
        try {
            userManagerService.processUserManagerMessage(data);
            System.out.println("物业管理员添加测试成功");
        } catch (Exception e) {
            System.err.println("物业管理员添加测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试中介管理员添加
     */
    @Test
    public void testAddIntermediaryManager() {
        JSONObject data = new JSONObject();
        data.put("type", 1);
        data.put("id_card", "330400199001011235");
        data.put("user_name", "TEST2");
        data.put("phone", "18812341235");
        data.put("position", "管理员");
        data.put("status", "0");
        data.put("update_user", "处理人");

        System.out.println("测试数据: " + JSONUtil.toJsonStr(data));
        
        try {
            userManagerService.processUserManagerMessage(data);
            System.out.println("中介管理员添加测试成功");
        } catch (Exception e) {
            System.err.println("中介管理员添加测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试物业管理员删除
     */
    @Test
    public void testDeletePropertyManager() {
        JSONObject data = new JSONObject();
        data.put("type", 0);
        data.put("area_id", "123456");
        data.put("id_card", "330400199001011234");
        data.put("user_name", "TEST");
        data.put("phone", "18812341234");
        data.put("position", "管理员");
        data.put("status", "1");
        data.put("update_user", "处理人");

        System.out.println("测试数据: " + JSONUtil.toJsonStr(data));
        
        try {
            userManagerService.processUserManagerMessage(data);
            System.out.println("物业管理员删除测试成功");
        } catch (Exception e) {
            System.err.println("物业管理员删除测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试中介管理员删除
     */
    @Test
    public void testDeleteIntermediaryManager() {
        JSONObject data = new JSONObject();
        data.put("type", 1);
        data.put("id_card", "330400199001011235");
        data.put("user_name", "TEST2");
        data.put("phone", "18812341235");
        data.put("position", "管理员");
        data.put("status", "1");
        data.put("update_user", "处理人");

        System.out.println("测试数据: " + JSONUtil.toJsonStr(data));
        
        try {
            userManagerService.processUserManagerMessage(data);
            System.out.println("中介管理员删除测试成功");
        } catch (Exception e) {
            System.err.println("中介管理员删除测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
