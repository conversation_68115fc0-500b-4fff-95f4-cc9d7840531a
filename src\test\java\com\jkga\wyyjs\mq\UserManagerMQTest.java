package com.jkga.wyyjs.mq;

import cn.hutool.json.JSONUtil;
import com.rabbitmq.client.Channel;
import com.rabbitmq.client.Connection;
import com.rabbitmq.client.ConnectionFactory;
import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.Map;

/**
 * 用户管理MQ测试类
 * 用于测试发送MQ消息到QUEUE_JQL_WYYJSADMIN队列
 * 
 * @Author：clyde
 * @Date：2025/7/18
 */
public class UserManagerMQTest {

    private static final String EXCHANGE_NAME = "EXCHANGE_QCB";
    private static final String ROUTING_KEY = "ROUTING_KEY_JQL_WYYJSADMIN";
    private static final String QUEUE_NAME = "QUEUE_JQL_WYYJSADMIN";

    /**
     * 测试发送物业管理员添加消息
     */
    @Test
    public void testSendPropertyManagerAddMessage() throws Exception {
        Map<String, Object> message = new HashMap<>();
        message.put("type", 0);
        message.put("area_id", "123456");
        message.put("id_card", "330400199001011234");
        message.put("user_name", "TEST");
        message.put("phone", "***********");
        message.put("position", "管理员");
        message.put("status", "0");
        message.put("update_user", "处理人");

        sendMessage(message);
        System.out.println("物业管理员添加消息发送成功");
    }

    /**
     * 测试发送中介管理员添加消息
     */
    @Test
    public void testSendIntermediaryManagerAddMessage() throws Exception {
        Map<String, Object> message = new HashMap<>();
        message.put("type", 1);
        message.put("id_card", "330400199001011235");
        message.put("user_name", "TEST2");
        message.put("phone", "18812341235");
        message.put("position", "管理员");
        message.put("status", "0");
        message.put("update_user", "处理人");

        sendMessage(message);
        System.out.println("中介管理员添加消息发送成功");
    }

    /**
     * 测试发送物业管理员删除消息
     */
    @Test
    public void testSendPropertyManagerDeleteMessage() throws Exception {
        Map<String, Object> message = new HashMap<>();
        message.put("type", 0);
        message.put("area_id", "123456");
        message.put("id_card", "330400199001011234");
        message.put("user_name", "TEST");
        message.put("phone", "***********");
        message.put("position", "管理员");
        message.put("status", "1");
        message.put("update_user", "处理人");

        sendMessage(message);
        System.out.println("物业管理员删除消息发送成功");
    }

    /**
     * 测试发送中介管理员删除消息
     */
    @Test
    public void testSendIntermediaryManagerDeleteMessage() throws Exception {
        Map<String, Object> message = new HashMap<>();
        message.put("type", 1);
        message.put("id_card", "330400199001011235");
        message.put("user_name", "TEST2");
        message.put("phone", "18812341235");
        message.put("position", "管理员");
        message.put("status", "1");
        message.put("update_user", "处理人");

        sendMessage(message);
        System.out.println("中介管理员删除消息发送成功");
    }

    /**
     * 发送消息到RabbitMQ
     */
    private void sendMessage(Map<String, Object> message) throws Exception {
        ConnectionFactory factory = new ConnectionFactory();
        factory.setHost("127.0.0.1");
        factory.setPort(5672);
        factory.setUsername("clyde");
        factory.setPassword("admin123");

        try (Connection connection = factory.newConnection();
             Channel channel = connection.createChannel()) {

            // 声明交换机
            channel.exchangeDeclare(EXCHANGE_NAME, "direct", true);

            // 声明队列
            channel.queueDeclare(QUEUE_NAME, true, false, false, null);

            // 绑定队列到交换机
            channel.queueBind(QUEUE_NAME, EXCHANGE_NAME, ROUTING_KEY);

            // 发送消息
            String messageJson = JSONUtil.toJsonStr(message);
            channel.basicPublish(EXCHANGE_NAME, ROUTING_KEY, null, messageJson.getBytes("UTF-8"));

            System.out.println("发送消息: " + messageJson);
        }
    }
}
