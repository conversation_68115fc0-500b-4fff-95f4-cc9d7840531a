<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jkga.wyyjs.mapper.AreaDepartmentMapper">

    <resultMap id="AreaDepartmentResultMap" type="com.jkga.wyyjs.model.entity.AreaDepartmentEntity">
        <id     property="id"     column="id"     />
        <result property="areaId"   column="area_id"   />
        <result property="name"   column="name"   />
        <result property="deleteStatus"   column="delete_status"   />
    </resultMap>

    <select id="getAreaDepartmentByName" resultMap="AreaDepartmentResultMap">
        SELECT * from "area_department" where "area_id" = #{areaId} and "name" = #{departName} and "delete_status" = 0
    </select>

    <insert id="insertAreaDepartment" parameterType="com.jkga.wyyjs.model.entity.AreaDepartmentEntity">
        insert into "area_department"(
            "id",
            "area_id",
            "name",
            "delete_status"
            )values(
            #{id},
            #{areaId},
            #{name},
            #{deleteStatus}
        )
    </insert>

    <select id="getAreaDepartmentList" resultMap="AreaDepartmentResultMap">
        SELECT * from "area_department" where "area_id" = #{areaId} and "delete_status" = 0
    </select>

</mapper>