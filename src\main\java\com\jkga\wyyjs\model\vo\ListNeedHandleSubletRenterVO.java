package com.jkga.wyyjs.model.vo;

import com.jkga.wyyjs.anno.PrivacyEncrypt;
import com.jkga.wyyjs.constant.PrivacyTypeEnum;
import lombok.Data;

/**
 * @Author：clyde
 * @Date：2025/7/17 14:20
 */
@Data
public class ListNeedHandleSubletRenterVO {

    private String id;
    private String userId;
    private String userName;
    @PrivacyEncrypt(type = PrivacyTypeEnum.PHONE)// 脱敏手机号
    private String userPhone;
    private String userIdentityType;
    @PrivacyEncrypt(type = PrivacyTypeEnum.ID_CARD) // 脱敏证件号
    private String userIdentity;
    private String userImgUrl;
    private Integer status;
    private String creatorId;
    private String createTime;
    private Integer applyType;

}
