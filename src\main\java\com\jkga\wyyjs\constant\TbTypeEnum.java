package com.jkga.wyyjs.constant;

/**
 * 存file表需要用到的业务类型（可以理解为自定义的业务表名）
 *
 * <AUTHOR>
 * @date 2025/1/17 11:11
 */
public enum TbTypeEnum {

    USER("user"),
    // 后续实体类型扩展
    VISITRECORD("visit_record"),
    SUBLETRENTER("sublet_renter"),
    SUBLETCONTRACTRELATION("sublet_contract_relation"),
    SUBLETHOUSE("sublet_house")
    ;

    private final String value;

    TbTypeEnum(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    /**
     * 根据字符串值查找对应的枚举实例
     */
    public static TbTypeEnum fromValue(String value) {
        for (TbTypeEnum type : TbTypeEnum.values()) {
            if (type.getValue().equalsIgnoreCase(value)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown owner type: " + value);
    }

    /**
     * 重写toString方法，返回枚举的实际值
     */
    @Override
    public String toString() {
        return this.value;
    }
}
