package com.jkga.wyyjs.controller;

import com.jkga.wyyjs.aspect.RecordLog;
import com.jkga.wyyjs.constant.UserFunctionEnum;
import com.jkga.wyyjs.model.CommonCode;
import com.jkga.wyyjs.model.CommonResult;
import com.jkga.wyyjs.model.vo.VisitRecordVO;
import com.jkga.wyyjs.service.VisitService;
import com.jkga.wyyjs.utils.PermissionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

/**
 * 访客管理
 *
 * @Author：clyde
 * @Date：2025/6/4 10:22
 */
@Slf4j
@CrossOrigin
@RestController
@RequestMapping("visit")
public class VisitRecordController {


    @Autowired
    private VisitService visitService;

    @Autowired
    HttpServletRequest request;

    @Autowired
    private PermissionUtils permissionUtils;

    /**
     * 添加访客记录
     *
     * @Author：clyde
     * @Date：2025/6/4 10:23
     */
    @RecordLog(optModel = "访客管理", optDesc = "添加访客记录")
    @RequestMapping(value = "addRecord", method = RequestMethod.POST)
    public CommonResult addRecord(@Valid VisitRecordVO visitRecordVO) {
        //权限控制
        if (!permissionUtils.judeUserPermission(request.getHeader("uid"), request.getHeader("area_id"), UserFunctionEnum.WYFKDJ, request.getRequestURI())) {
            return new CommonResult(CommonCode.NO_PERMISSION);
        }
        try {
            String userId = request.getHeader("uid");
            return visitService.addRecord(visitRecordVO, userId);
        } catch (Exception e) {
            return new CommonResult(CommonCode.SYSTEM_ERROR);
        }
    }


    /**
     * 获取访客记录
     *
     * @Author：clyde
     * @Date：2025/6/4 14:30
     */
    @RequestMapping(value = "getRecordList", method = RequestMethod.GET)
    public CommonResult getRecordList(@RequestParam(value = "areaId", required = true) String areaId,
                                      @RequestParam(value = "type", required = false) Integer type,
                                      String searchParam,
                                      @RequestParam(defaultValue = "1") int pageNum,
                                      @RequestParam(defaultValue = "10") int pageSize) {
        //权限控制
        if (!permissionUtils.judeUserPermission(request.getHeader("uid"), request.getHeader("area_id"), UserFunctionEnum.WYFKDJ, request.getRequestURI())) {
            return new CommonResult(CommonCode.NO_PERMISSION);
        }
        try {
            return visitService.getRecordList(areaId, type, searchParam, pageNum, pageSize);
        } catch (Exception e) {
            return new CommonResult(CommonCode.SYSTEM_ERROR);
        }
    }


    /**
     * 识别车辆照片
     *
     * @Author：clyde
     * @Date：2025/6/5 14:03
     */
    @RequestMapping(value = "identifyCarNo", method = RequestMethod.POST)
    public CommonResult identifyCarNo(MultipartFile car_file) {
        //权限控制
        if (!permissionUtils.judeUserPermission(request.getHeader("uid"), request.getHeader("area_id"), UserFunctionEnum.WYFKDJ, request.getRequestURI())) {
            return new CommonResult(CommonCode.NO_PERMISSION);
        }
        try {
            return visitService.identifyCarNo(car_file);
        } catch (Exception e) {
            return new CommonResult(CommonCode.SYSTEM_ERROR);
        }
    }


}
