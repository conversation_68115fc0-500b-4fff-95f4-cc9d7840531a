package com.jkga.wyyjs.mapper;

import com.jkga.wyyjs.model.entity.IntermediaryManagerEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 中介管理人员Mapper接口
 * 
 * @Author：clyde
 * @Date：2025/7/18
 */
@Mapper
public interface IntermediaryManagerMapper {

    /**
     * 插入中介管理人员
     */
    boolean insertIntermediaryManager(IntermediaryManagerEntity intermediaryManagerEntity);

    /**
     * 根据用户ID查询中介管理人员
     */
    IntermediaryManagerEntity getIntermediaryManagerByUserId(@Param("userId") String userId);

    /**
     * 根据用户ID删除中介管理人员（逻辑删除）
     */
    boolean delIntermediaryManagerByUserId(@Param("userId") String userId);

}
