package com.jkga.wyyjs.controller;


import com.jkga.wyyjs.constant.UserFunctionEnum;
import com.jkga.wyyjs.model.CommonCode;
import com.jkga.wyyjs.model.CommonResult;
import com.jkga.wyyjs.model.dto.LoginDTO;
import com.jkga.wyyjs.model.vo.DeclareForUserVO;
import com.jkga.wyyjs.model.vo.UserAuthVO;
import com.jkga.wyyjs.model.vo.UserLoginVO;
import com.jkga.wyyjs.service.UserService;
import com.jkga.wyyjs.utils.PermissionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.io.IOException;
import java.security.NoSuchAlgorithmException;

/**
 * 用户管理
 *
 * <AUTHOR>
 */
@Slf4j
@CrossOrigin
@RestController
@RequestMapping("user")
public class UserController {

    @Autowired
    private UserService userService;

    @Autowired
    HttpServletRequest request;

    @Autowired
    private PermissionUtils permissionUtils;


    /**
     * 微信小程序用户登录
     *
     * @param
     * @return
     */
    @RequestMapping(value = "login", method = RequestMethod.POST)
    public CommonResult login(@RequestBody UserLoginVO loginVO) throws Exception {
//        String ip = CusAccessObjectUtil.getIpAddress(request);
        LoginDTO loginDTO = userService.login(loginVO.getJsCode(), loginVO.getEncryptedData(), loginVO.getIv());
        return new CommonResult<>(loginDTO);
    }

    /**
     * 通过id获取单个用户信息
     *
     * @return
     */
    @RequestMapping(value = "getUserDetail", method = RequestMethod.GET)
    public CommonResult getUserDetail() {
//        //测试自定义异常抛出
//        try {
//            int a = 1 / 0;
//        } catch (Exception e) {
//            throw new MyException(new CommonResult(CommonCode.PARAM_ERROR));
//        }
        String uid = request.getHeader("uid");
        return new CommonResult<>(userService.getUserById(uid));
    }

    /**
     * 用户认证（名义认证，自己上传姓名、证件等信息）
     *
     * @param authVO
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "auth", method = RequestMethod.POST, consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public CommonResult auth(@Valid UserAuthVO authVO) throws IOException, NoSuchAlgorithmException {
        String userId = request.getHeader("uid");
        authVO.setUserId(userId);
        userService.auth(authVO);
        return new CommonResult<>();
    }

    /**
     * 查询人员是否已实名认证
     *
     * @return
     */
    @RequestMapping(value = "getUserAuthInfo", method = RequestMethod.GET)
    public CommonResult getUserAuthInfo(String name, String identity) {
        return userService.getUserAuthInfo(name, identity);
    }

    /**
     * 帮助用户申报
     *
     * @return
     */
    @RequestMapping(value = "declareForUser", method = RequestMethod.POST, consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public CommonResult declareForUser(@Valid DeclareForUserVO declareVO) throws IOException, NoSuchAlgorithmException {
        //权限控制
        if (!permissionUtils.judeUserPermission(request.getHeader("uid"), request.getHeader("area_id"), UserFunctionEnum.WYBZDJ, request.getRequestURI())) {
            return new CommonResult(CommonCode.NO_PERMISSION);
        }
        String creatorId = request.getHeader("uid");
        declareVO.setCreatorId(creatorId);
        userService.declareForUser(declareVO);
        return new CommonResult<>();
    }

    /**
     * 腾讯云人脸核身detectAuth
     *
     * @Author：clyde
     * @Date：2025/2/27 14:00
     */
    @RequestMapping(value = "/face/detectAuth", method = RequestMethod.POST)
    public CommonResult detectAuth(String IdCard, String Name) {
        try {
            return userService.detectAuth(IdCard, Name);
        } catch (Exception e) {
            return new CommonResult(CommonCode.SYSTEM_ERROR);
        }
    }

    /**
     * 腾讯云获取核身信息getDetectInfo
     *
     * @Author：clyde
     * @Date：2025/2/27 14:00
     */
    @RequestMapping(value = "/face/getDetectInfo", method = RequestMethod.POST)
    public CommonResult getDetectInfo(String bizToken) {
        try {
            String userId = request.getHeader("uid");
            return userService.getDetectInfo(bizToken, userId);
        } catch (Exception e) {
            return new CommonResult(CommonCode.SYSTEM_ERROR);
        }
    }


    /**
     * 微信小程序人脸核身
     *
     * @Author：clyde
     * @Date：2025/4/9 14:20
     */
    @RequestMapping(value = "/face/getuseridkey", method = RequestMethod.POST)
    public CommonResult getuseridkey(String IdCard, String Name) {
        try {
            return userService.getuseridkey(IdCard, Name);
        } catch (Exception e) {
            return new CommonResult(CommonCode.SYSTEM_ERROR);
        }
    }

    /**
     * 微信小程序身份校验
     *
     * @Author：clyde
     * @Date：2025/4/9 14:20
     */
    @RequestMapping(value = "/face/getinfo", method = RequestMethod.POST)
    public CommonResult getinfo(String IdCard, String Name, String verify_result) {
        try {
            String creatorId = request.getHeader("uid");
            return userService.getinfo(creatorId, IdCard, Name, verify_result);
        } catch (Exception e) {
            return new CommonResult(CommonCode.SYSTEM_ERROR);
        }
    }


}
