package com.jkga.wyyjs.task;

import cn.hutool.json.JSONObject;
import com.jkga.wyyjs.mapper.HouseMapper;
import com.jkga.wyyjs.model.vo.XqConfigTableVO;
import com.jkga.wyyjs.service.HouseService;
import com.jkga.wyyjs.utils.ThirdService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Author：clyde
 * @Date：2025/5/23 8:51
 */
@Slf4j
@Component
public class CarTableTask {

    @Autowired
    private ThirdService thirdService;

    @Autowired
    private HouseService houseService;


    @Scheduled(cron = "0 0 8 * * ?")     //凌晨8点执行一次
    public void syncCarTableData() {
        List<XqConfigTableVO> list = thirdService.getXqConfigTable();
        list.forEach(e -> {
            JSONObject jsonObject = thirdService.getCarList(e.getArea_location_id(), 1);
            houseService.handleCarTableData(jsonObject);
            for (int i = 2; i < jsonObject.getInt("totalPageNum") + 1; i++) {
                JSONObject item = thirdService.getCarList(e.getArea_location_id(), i);
                houseService.handleCarTableData(item);
            }
        });
    }


}
