package com.jkga.wyyjs.model.entity;

import lombok.Data;

/**
 * user表实体类（字段严格按照数据库表的字段）
 *
 * <AUTHOR>
 */
@Data
public class UserEntity {

    private String id;
    private String openId;
    private String name;
    private String phone;
    private String identityType;
    private String identity;
    private String faceImgUrl;
    private String creatorId;
    private String createTime;
    private String updaterId;
    private String updateTime;
    private Integer deleteStatus;
    private String lastLoginTime;
    private String dossierId;

}
