package com.jkga.wyyjs.anno;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

/**
 * 特殊字符判断
 *
 * <AUTHOR>
 */
public class NoQuotesValidator implements ConstraintValidator<NoQuotes, String> {

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        if (value == null) {
            return true;
        }
        return !value.contains("'") && !value.contains("\"");
    }
}