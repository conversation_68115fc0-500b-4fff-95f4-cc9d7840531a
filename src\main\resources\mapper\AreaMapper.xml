<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jkga.wyyjs.mapper.AreaMapper">

    <resultMap id="AreaResultMap" type="com.jkga.wyyjs.model.entity.AreaEntity">
        <id     property="id"     column="id"     />
        <result property="name"   column="name"   />
        <result property="regionId"   column="region_id"   />
    </resultMap>


    <select id="getAreaListByRegionId" resultMap="AreaResultMap">
        select * from "area" where "region_id" = #{regionId} and "delete_status" = 0
    </select>



</mapper>