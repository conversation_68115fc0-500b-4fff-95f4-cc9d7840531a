package com.jkga.wyyjs.service;

import com.jkga.wyyjs.mapper.AreaMapper;
import com.jkga.wyyjs.mapper.HouseMapper;
import com.jkga.wyyjs.mapper.RegionMapper;
import com.jkga.wyyjs.model.entity.AreaEntity;
import com.jkga.wyyjs.model.entity.HouseEntity;
import com.jkga.wyyjs.model.entity.RegionEntity;
import com.jkga.wyyjs.model.vo.HouseVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * ${description}
 *
 * <AUTHOR>
 * @date 2025/2/6 15:20
 */

@Slf4j
@Service
public class RegionService {

    @Autowired
    private RegionMapper regionMapper;

    @Autowired
    private AreaMapper areaMapper;

    @Autowired
    private HouseMapper houseMapper;

    public List<RegionEntity> getRegionList(String parentId, int level) {
        return regionMapper.listRegionByLevel(parentId, level);
    }

    public List<AreaEntity> getAreaList(String regionId) {
        return areaMapper.getAreaListByRegionId(regionId);
    }

    public List<HouseVO> getHouseList(String areaId) {
        List<HouseEntity> list = houseMapper.getHouseListByAreaId(areaId);
        List<HouseVO> house_list = list.stream().map(houseEntity -> {
            HouseVO houseVO = new HouseVO();
            BeanUtils.copyProperties(houseEntity, houseVO);
            return houseVO;
        }).collect(Collectors.toList());
        List<HouseVO> result_list = house_list.stream().filter(item -> item.getHouseType() == 0).collect(Collectors.toList());
        result_list.forEach(e -> {
            createHouseTree(e, house_list);
        });
        return result_list;
    }

    public void createHouseTree(HouseVO houseVO, List<HouseVO> list) {
        list.stream().forEach(e -> {
            if (StringUtils.isNotBlank(e.getParentId()) && e.getParentId().equals(houseVO.getId())) {
                if (houseVO.getList() == null) {
                    houseVO.setList(new ArrayList<>());
                }
                createHouseTree(e, list);
                houseVO.getList().add(e);
            }
        });
    }

}
