package com.jkga.wyyjs.utils;

import lombok.extern.slf4j.Slf4j;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;
import java.security.*;
import java.security.spec.InvalidParameterSpecException;
import java.util.Arrays;
import java.util.Base64;

/**
 * 微信API调用工具类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class WeixinUtil {

    @Autowired
    private RedisUtil redisUtil;

    /**
     * 获取access_token
     *
     * @return
     */
    public String getAccessToken() {
        //从redis中获取access_token
        return String.valueOf(redisUtil.get("access_token"));
    }

    /**
     * 获取用户信息
     *
     * @param
     * @return
     */
    public String getUserInfoStr(String encryptedData, String sessionkey, String iv) throws InvalidKeyException, NoSuchProviderException {
        log.info("开始解密：encryptedData=" + encryptedData + ", sessionkey=" + sessionkey + ",iv=" + iv);
        // 被加密的数据
        byte[] dataByte = Base64.getDecoder().decode(encryptedData);
        // 加密秘钥
        byte[] keyByte = Base64.getDecoder().decode(sessionkey);
        // 偏移量
        byte[] ivByte = Base64.getDecoder().decode(iv);
        try {
            // 如果密钥不足16位，那么就补足.  这个if 中的内容很重要
            int base = 16;
            if (keyByte.length % base != 0) {
                int groups = keyByte.length / base + (keyByte.length % base != 0 ? 1 : 0);
                byte[] temp = new byte[groups * base];
                Arrays.fill(temp, (byte) 0);
                System.arraycopy(keyByte, 0, temp, 0, keyByte.length);
                keyByte = temp;
            }
            // 初始化
            Security.addProvider(new BouncyCastleProvider());
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS7Padding", "BC");
            SecretKeySpec spec = new SecretKeySpec(keyByte, "AES");
            AlgorithmParameters parameters = AlgorithmParameters.getInstance("AES");
            parameters.init(new IvParameterSpec(ivByte));
            cipher.init(Cipher.DECRYPT_MODE, spec, parameters);// 初始化
            byte[] resultByte = cipher.doFinal(dataByte);
            if (null != resultByte && resultByte.length > 0) {
                return new String(resultByte, "UTF-8");
            }
        } catch (NoSuchAlgorithmException |
                UnsupportedEncodingException |
                InvalidAlgorithmParameterException |
                BadPaddingException |
                IllegalBlockSizeException |
                InvalidParameterSpecException |
                NoSuchPaddingException e) {
            log.info(e.getMessage());
        }
        return null;
    }

}
