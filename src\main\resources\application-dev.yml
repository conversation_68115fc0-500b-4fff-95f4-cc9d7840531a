spring:
  datasource:
    #url: jdbc:dm://127.0.0.1:5236/wyyjs
    url: jdbc:dm://***********:5236/wyyjs
    username: SYSDBA
    password: <PERSON><PERSON><PERSON>@2025
    driver-class-name: dm.jdbc.driver.DmDriver
    type: com.zaxxer.hikari.HikariDataSource
  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 100MB
  redis:
    # Redis数据库索引（默认0）
    database: 1
    host: localhost
    port: 6179
    password: Jkga@Qzlq903()#
    jedis:
      pool:
        # 连接池最大连接数（负值代表没有限制）默认8
        max-active: 8
        # 连接池最大阻塞等待时间（负值代表没有限制）默认-1
        max-wait: -1s
        # 连接池中的最大空闲连接 默认8
        max-idle: 8
        # 连接池中的最小空闲连接 默认0
        min-idle: 0
    timeout: 3s

  #配置rabbitMq 服务器
  rabbitmq:
    mq1:
      host: 127.0.0.1
      port: 5672
      username: clyde
      password: admin123
      listener:
        simple:
          acknowledge-mode: manual #设置手动确认
          prefetch: 100

mybatis:
  type-aliases-package: com.jkga.wyyjs.model
  mapper-locations: classpath:mapper/*.xml
  configuration:
    # 返回Map时保留为空的字段
    call-setters-on-nulls: true
    # 下划线格式数据库表字段映射驼峰格式模型类属性
    map-underscore-to-camel-case: true
    # 打印sql语句
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

weixin:
  app-id: wx4f790ec51307645b
  app-secret: 413cfd29ccad7acb475c1d505a07c2a4
  openid-url: https://api.weixin.qq.com/sns/jscode2session
  token-url: https://api.weixin.qq.com/cgi-bin/token
  unlimited-url: https://api.weixin.qq.com/wxa/getwxacodeunlimit

#人脸核身api
face-api:
  bizToken-url: https://faceid.tencentcloudapi.com
  secret-id: AKIDnfS6cyLNr6GZaSRuR17axELHevRrvOZC
  secret-key: iimstfrknTCC7ZvBjkkaFB2iKqsjToHn

#公安api
jkga-api:
  storemgmt-url: https://zzsbapi.jxjkga.cn/storemgmt

file:
  root-path: F:/wyyjs

log:
  path: /root/wyyjs/logs/

server:
  port: 5432
