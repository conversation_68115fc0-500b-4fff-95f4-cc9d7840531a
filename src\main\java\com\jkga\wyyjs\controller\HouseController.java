package com.jkga.wyyjs.controller;


import com.jkga.wyyjs.constant.UserFunctionEnum;
import com.jkga.wyyjs.model.CommonCode;
import com.jkga.wyyjs.model.CommonResult;
import com.jkga.wyyjs.model.dto.UserHouseDTO;
import com.jkga.wyyjs.model.entity.CarManageEntity;
import com.jkga.wyyjs.model.vo.*;
import com.jkga.wyyjs.service.HouseService;
import com.jkga.wyyjs.aspect.RecordLog;
import com.jkga.wyyjs.utils.PermissionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.security.NoSuchAlgorithmException;
import java.util.List;
import java.util.Map;

/**
 * 房屋申报相关
 *
 * <AUTHOR>
 * @date 2025/2/10 10:51
 */

@Slf4j
@CrossOrigin
@RestController
@RequestMapping("house")
public class HouseController {

    @Autowired
    private HouseService houseService;
    @Autowired
    HttpServletRequest request;
    @Autowired
    private PermissionUtils permissionUtils;

    /**
     * 房屋成员列表
     *
     * @return
     */
    @RequestMapping(value = "getHouseMemberList", method = RequestMethod.GET)
    public CommonResult getHouseMemberList(@RequestParam(defaultValue = "1") int pageNum,
                                           @RequestParam(defaultValue = "10") int pageSize,
                                           @RequestParam(value = "area_id", required = true) String area_id,
                                           Integer status) {
        //权限控制
        if (!permissionUtils.judeUserPermission(request.getHeader("uid"), request.getHeader("area_id"), UserFunctionEnum.WYFWSH, request.getRequestURI())) {
            return new CommonResult(CommonCode.NO_PERMISSION);
        }
        return new CommonResult<>(houseService.getHouseMemberList(pageNum, pageSize, status, area_id));
    }

    /**
     * 帮助房屋成员申报
     *
     * @return
     */
    @RequestMapping(value = "declareForHouseMember", method = RequestMethod.POST)
    public CommonResult declareForHouseMember(@RequestBody DeclareForHouseMemberVO declareVO) throws IOException, NoSuchAlgorithmException {
        //权限控制
        if (!permissionUtils.judeUserPermission(request.getHeader("uid"), request.getHeader("area_id"), UserFunctionEnum.WYBZDJ, request.getRequestURI())) {
            return new CommonResult(CommonCode.NO_PERMISSION);
        }
        String creatorId = request.getHeader("uid");
        declareVO.setCreatorId(creatorId);
        houseService.declareForHouseMember(declareVO);
        return new CommonResult<>();
    }

    /**
     * 审核房屋成员申报
     *
     * @return
     */
    @RequestMapping(value = "reviewHouseMember", method = RequestMethod.POST)
    public CommonResult reviewHouseMember(@RequestBody Map<String, Object> paramMap) {

        //权限控制
        if (!permissionUtils.judeUserPermission(request.getHeader("uid"), request.getHeader("area_id"), UserFunctionEnum.WYFWSH, request.getRequestURI())) {
            return new CommonResult(CommonCode.NO_PERMISSION);
        }
        String adminId = request.getHeader("uid");
        String houseMemberId = (String) paramMap.get("houseMemberId");
        int status = (int) paramMap.get("status");
        houseService.reviewHouseMember(adminId, houseMemberId, status);
        return new CommonResult<>();
    }

    /**
     * 用户自主申报
     *
     * @Author：clyde
     * @Date：2025/2/20 9:11
     */
    @RecordLog(optModel = "房屋申报", optDesc = "用户自主申报")
    @RequestMapping(value = "applyHouseMember", method = RequestMethod.POST)
    public CommonResult applyHouseMember(@RequestBody ApplyHouseMemberVO applyHouseMemberVO) {
        try {
            String creatorId = request.getHeader("uid");
            applyHouseMemberVO.setUserId(creatorId);
            applyHouseMemberVO.setCreator(creatorId);
            return houseService.applyHouseMember(applyHouseMemberVO);
        } catch (Exception e) {
            return new CommonResult(CommonCode.SYSTEM_ERROR);
        }
    }

    /**
     * 获取用户关联的房屋列表
     *
     * @return
     */
    @RequestMapping(value = "getUserHouseList", method = RequestMethod.GET)
    public CommonResult getUserHouseList() {
        String userId = request.getHeader("uid");
        List<UserHouseDTO> houseList = houseService.getUserHouseList(userId);
        return new CommonResult<>(houseList);
    }

    /**
     * 获取小区房屋 人员统计
     *
     * @Author：clyde
     * @Date：2025/2/27 14:00
     */
    @RequestMapping(value = "countHouseAndMemberByArea", method = RequestMethod.GET)
    public CommonResult countHouseAndMemberByArea(String areaId) {

        //权限控制
        if (!permissionUtils.judeUserPermission(request.getHeader("uid"), request.getHeader("area_id"), UserFunctionEnum.WYFWGL, request.getRequestURI())) {

            return new CommonResult(CommonCode.NO_PERMISSION);
        }
        try {
            return houseService.countHouseAndMemberByArea(areaId);
        } catch (Exception e) {
            return new CommonResult(CommonCode.SYSTEM_ERROR);
        }
    }

    /**
     * 获取小区下的楼幢
     *
     * @Author：clyde
     * @Date：2025/2/27 14:00
     */
    @RequestMapping(value = "getBuildingByAreaId", method = RequestMethod.GET)
    public CommonResult getBuildingByAreaId(String areaId) {
        //权限控制
        if (!permissionUtils.judeUserPermission(request.getHeader("uid"), request.getHeader("area_id"), UserFunctionEnum.WYFWGL, request.getRequestURI())) {
            return new CommonResult(CommonCode.NO_PERMISSION);
        }
        try {
            return houseService.getBuildingByAreaId(areaId);
        } catch (Exception e) {
            return new CommonResult(CommonCode.SYSTEM_ERROR);
        }
    }

    /**
     * 获取楼幢下的单元 房屋
     *
     * @Author：clyde
     * @Date：2025/2/27 14:00
     */
    @RequestMapping(value = "getHouseByBuildingId", method = RequestMethod.GET)
    public CommonResult getHouseByBuildingId(String buildingId) {
        //权限控制
        if (!permissionUtils.judeUserPermission(request.getHeader("uid"), request.getHeader("area_id"), UserFunctionEnum.WYFWGL, request.getRequestURI())) {
            return new CommonResult(CommonCode.NO_PERMISSION);
        }
        try {
            return houseService.getHouseByBuildingId(buildingId);
        } catch (Exception e) {
            return new CommonResult(CommonCode.SYSTEM_ERROR);
        }
    }

    /**
     * 根据房屋id获取房间成员
     *
     * @Author：clyde
     * @Date：2025/2/27 14:00
     */
    @RequestMapping(value = "getHouseMemberListByHouseId", method = RequestMethod.GET)
    public CommonResult getHouseMemberListByHouseId(String houseId) {
        try {
            return houseService.getHouseMemberListByHouseId(houseId);
        } catch (Exception e) {
            return new CommonResult(CommonCode.SYSTEM_ERROR);
        }
    }

    /**
     * excel导入房屋车辆关联人员
     *
     * @Author：clyde
     * @Date：2025/5/15 14:19
     */
    @RequestMapping(value = "importCar", method = RequestMethod.POST)
    public CommonResult importCar(@RequestParam(value = "file") MultipartFile file) {
        //权限控制
        if (!permissionUtils.judeUserPermission(request.getHeader("uid"), request.getHeader("area_id"), UserFunctionEnum.WYRCGL, request.getRequestURI())) {
            return new CommonResult(CommonCode.NO_PERMISSION);
        }
        try {
            String userId = request.getHeader("uid");
            return houseService.importCar(file, userId);
        } catch (Exception e) {
            e.printStackTrace();
            return new CommonResult(500, e.getMessage());
        }
    }


    /***
     *获取待绑定车辆列表
     * @param area_location_id
     * @return
     */
    @RequestMapping(value = "getToBindingCarListByAreaId", method = RequestMethod.GET)
    public CommonResult getToBindingCarListByAreaId(String area_location_id,
                                                    String car_no,
                                                    @RequestParam(defaultValue = "1") int pageNum,
                                                    @RequestParam(defaultValue = "10") int pageSize) {

        //权限控制
        if (!permissionUtils.judeUserPermission(request.getHeader("uid"), request.getHeader("area_id"), UserFunctionEnum.WYRCGL, request.getRequestURI())) {
            return new CommonResult(CommonCode.NO_PERMISSION);
        }
        try {
            return new CommonResult(houseService.getToBindingCarListByAreaId(area_location_id, car_no, pageNum, pageSize));
        } catch (Exception e) {
            return new CommonResult(CommonCode.SYSTEM_ERROR);
        }
    }

    /***
     *获取小区车辆管理列表
     * @param area_location_id
     * @return
     */
    @RequestMapping(value = "getCarManagerListByAreaId", method = RequestMethod.GET)
    public CommonResult getCarManagerListByAreaId(String area_location_id, String param, @RequestParam(defaultValue = "1") int pageNum,

                                                  @RequestParam(defaultValue = "10") int pageSize) {
        //权限控制
        if (!permissionUtils.judeUserPermission(request.getHeader("uid"), request.getHeader("area_id"), UserFunctionEnum.WYCLGL, request.getRequestURI())) {
            return new CommonResult(CommonCode.NO_PERMISSION);
        }
        try {
            return new CommonResult(houseService.getCarManagerListByAreaId(area_location_id, param, pageNum, pageSize));
        } catch (Exception e) {
            return new CommonResult(CommonCode.SYSTEM_ERROR);
        }
    }

    /***
     * 修改车辆信息
     * @param carManageEntity
     * @return
     */
    @RequestMapping(value = "UpdateCarManagerById", method = RequestMethod.POST)
    public CommonResult UpdateCarManagerById(@RequestBody CarManageEntity carManageEntity) {

        //权限控制
        if (!permissionUtils.judeUserPermission(request.getHeader("uid"), request.getHeader("area_id"), UserFunctionEnum.WYCLGL, request.getRequestURI())) {

            return new CommonResult(CommonCode.NO_PERMISSION);
        }
        try {
            return houseService.UpdateCarManagerById(carManageEntity);
        } catch (Exception e) {
            e.printStackTrace();
            return new CommonResult(CommonCode.SYSTEM_ERROR);
        }
    }


    /**
     * 添加小区车辆管理
     *
     * @Author：clyde
     * @Date：2025/5/19 8:54
     */
    @RequestMapping(value = "addCarManage", method = RequestMethod.POST)
    public CommonResult addCarManage(@RequestBody CarManageVO carManageVO) {
        //权限控制
        if (!permissionUtils.judeUserPermission(request.getHeader("uid"), request.getHeader("area_id"), UserFunctionEnum.WYCLGL, request.getRequestURI())) {
            return new CommonResult(CommonCode.NO_PERMISSION);
        }
        try {
            return houseService.addCarManage(carManageVO);
        } catch (Exception e) {
            return new CommonResult(CommonCode.SYSTEM_ERROR);
        }
    }

    /**
     * 人房车辆关联不在提醒
     *
     * @Author：clyde
     * @Date：2025/5/19 8:54
     */
    @RequestMapping(value = "noRemind", method = RequestMethod.GET)
    public CommonResult noRemind(Integer id) {
        //权限控制
        if (!permissionUtils.judeUserPermission(request.getHeader("uid"), request.getHeader("area_id"), UserFunctionEnum.WYRCGL, request.getRequestURI())) {
            return new CommonResult(CommonCode.NO_PERMISSION);
        }
        try {
            return houseService.noRemind(id);
        } catch (Exception e) {
            return new CommonResult(CommonCode.SYSTEM_ERROR);
        }
    }

    /**
     * 手动同步车辆 人员数据数据
     *
     * @Author：clyde
     * @Date：2025/6/3 14:14
     */
    @RequestMapping(value = "handSyncCarTable", method = RequestMethod.POST)
    public CommonResult handSyncCarTable() {

        //权限控制
        if (!permissionUtils.judeUserPermission(request.getHeader("uid"), request.getHeader("area_id"), UserFunctionEnum.WYRCGL, request.getRequestURI())) {
            return new CommonResult(CommonCode.NO_PERMISSION);
        }
        try {
            houseService.handSyncCarTable();
            return new CommonResult(CommonCode.OK);
        } catch (Exception e) {
            return new CommonResult(CommonCode.SYSTEM_ERROR);
        }
    }

    /***
     * 绑定车辆到房屋列表
     * @param carHouseVO
     * @return
     */
    @RequestMapping(value = "bindingHouseByCar", method = RequestMethod.POST)
    public CommonResult bindingHouseByCar(@RequestBody CarHouseVO carHouseVO) {

        //权限控制
        if (!permissionUtils.judeUserPermission(request.getHeader("uid"), request.getHeader("area_id"), UserFunctionEnum.WYRCGL, request.getRequestURI())) {
            return new CommonResult(CommonCode.NO_PERMISSION);
        }
        try {
            String userId = request.getHeader("uid");
            return houseService.bindingHouseByCar(carHouseVO, userId);
        } catch (Exception ex) {
            return new CommonResult(CommonCode.SYSTEM_ERROR);
        }
    }

    /**
     * 处理清查宝未登记车辆
     *
     * @Author：clyde
     * @Date：2025/6/18 15:54
     */
    @RequestMapping(value = "updAlarmQcb", method = RequestMethod.POST)
    public CommonResult updAlarmQcb(@RequestBody AlarmQcbVO alarmQcbVO) {

        //权限控制
        if (!permissionUtils.judeUserPermission(request.getHeader("uid"), request.getHeader("area_id"), UserFunctionEnum.WYRCGL, request.getRequestURI())) {
            return new CommonResult(CommonCode.NO_PERMISSION);
        }
        try {
            String userId = request.getHeader("uid");
            return houseService.updAlarmQcb(alarmQcbVO, userId);
        } catch (Exception ex) {
            ex.printStackTrace();
            return new CommonResult(CommonCode.SYSTEM_ERROR);
        }
    }

    /**
     * 根据小区id查询清查宝同步车辆
     *
     * @Author：clyde
     * @Date：2025/6/19 16:28
     */
    @RequestMapping(value = "getAlarmQcbListByAreaId", method = RequestMethod.GET)
    public CommonResult getAlarmQcbListByAreaId(String area_location_id,
                                                @RequestParam(required = false) Integer status,
                                                @RequestParam(required = false) String searchParam,
                                                @RequestParam(defaultValue = "1") int pageNum,
                                                @RequestParam(defaultValue = "10") int pageSize) {

        //权限控制
        if (!permissionUtils.judeUserPermission(request.getHeader("uid"), request.getHeader("area_id"), UserFunctionEnum.WYRCGL, request.getRequestURI())) {
            return new CommonResult(CommonCode.NO_PERMISSION);
        }
        try {
            String userId = request.getHeader("uid");
            return houseService.getAlarmQcbListByAreaId(area_location_id, status, searchParam, pageNum, pageSize);
        } catch (Exception ex) {
            return new CommonResult(CommonCode.SYSTEM_ERROR);
        }
    }


}
