package com.jkga.wyyjs.service;

import cn.hutool.json.JSONObject;
import com.jkga.wyyjs.constant.TbTypeEnum;
import com.jkga.wyyjs.mapper.VisitRecordMapper;
import com.jkga.wyyjs.model.CommonCode;
import com.jkga.wyyjs.model.CommonListResult;
import com.jkga.wyyjs.model.CommonResult;
import com.jkga.wyyjs.model.dto.FileSaveReturnDTO;
import com.jkga.wyyjs.model.entity.CarTableEntity;
import com.jkga.wyyjs.model.entity.VisitRecordEntity;
import com.jkga.wyyjs.model.vo.VisitRecordVO;
import com.jkga.wyyjs.utils.SnowflakeUtil;
import com.jkga.wyyjs.utils.ThirdService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.security.NoSuchAlgorithmException;
import java.util.Arrays;
import java.util.Base64;
import java.util.List;

/**
 * @Author：clyde
 * @Date：2025/6/4 10:31
 */
@Slf4j
@Service
public class VisitService {

    @Autowired
    private VisitRecordMapper visitRecordMapper;
    @Autowired
    private FileService fileService;
    @Autowired
    private ThirdService thirdService;

    public CommonResult addRecord(VisitRecordVO visitRecordVO, String creatorId) throws IOException, NoSuchAlgorithmException {

        VisitRecordEntity visitRecordEntity = new VisitRecordEntity();
        BeanUtils.copyProperties(visitRecordVO, visitRecordEntity);
        //雪花id
        String recordId = SnowflakeUtil.generateId();
        visitRecordEntity.setId(recordId);

        if (visitRecordVO.getCar_file() != null) {
            List<MultipartFile> fileList = Arrays.asList(visitRecordVO.getCar_file());
            String tbType = TbTypeEnum.VISITRECORD.getValue();
            //这里modulePath（模块文件夹名）直接使用了tbType，也可以自定义该模块的文件夹名
            List<FileSaveReturnDTO> fileSaveReturnList = fileService.saveFiles(creatorId, fileList, recordId, tbType, tbType);
            if (fileSaveReturnList.size() > 0) {
                visitRecordEntity.setCarImage(fileSaveReturnList.get(0).getUrl());
            }
        }
        if (visitRecordVO.getPeople_file() != null) {
            List<MultipartFile> fileList = Arrays.asList(visitRecordVO.getPeople_file());
            String tbType = TbTypeEnum.VISITRECORD.getValue();
            //这里modulePath（模块文件夹名）直接使用了tbType，也可以自定义该模块的文件夹名
            List<FileSaveReturnDTO> fileSaveReturnList = fileService.saveFiles(creatorId, fileList, recordId, tbType, tbType);
            if (fileSaveReturnList.size() > 0) {
                visitRecordEntity.setPeopleImage(fileSaveReturnList.get(0).getUrl());
            }
        }

        visitRecordMapper.insertVisitRecord(visitRecordEntity);
        return new CommonResult(CommonCode.OK);
    }


    public CommonResult getRecordList(String areaId, Integer type, String searchParam, int pageNum, int pageSize) {
        //计算数据开始索引
        int currIndex = (pageNum - 1) * pageSize;

        int totalNum = visitRecordMapper.countRecord(areaId, type, searchParam);
        //总页数
        int totalPageNum = (totalNum + pageSize - 1) / pageSize;

        List<VisitRecordEntity> list = visitRecordMapper.getRecordList(areaId, type, searchParam, currIndex, pageSize);

        return new CommonResult(new CommonListResult(totalNum, totalPageNum, list));
    }

    public CommonResult identifyCarNo(MultipartFile car_file) throws IOException {
        String type = car_file.getContentType();
        byte[] fileBytes = car_file.getBytes();
        String base64String = "data:image/jpeg;base64," + Base64.getEncoder().encodeToString(fileBytes);

        JSONObject result = thirdService.identifyCarNo(base64String, type);
        return new CommonResult(result);
    }

}
