package com.jkga.wyyjs.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * @Author：clyde
 * @Date：2025/4/24 14:48
 */
@Configuration
public class ExecutorConfig {

    /**
     * 自定义线程池
     */
    @Bean("wyyjsExecutor")
    public Executor taskExecutor() {

        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        //核心线程数目
        //当提交一个任务到线程池时，线程池会创建一个线程来执行任务，即使其他空闲的基本线程能够执行新任务也会创建线程，等到需要执行的任务数大于核心线程的数量时就不再创建。如果调用了线程池的prestartAllCoreThreads方法，线程池会提前创建并启动所有基本线程。
        executor.setCorePoolSize(30);
        //指定最大线程数
        //线程池允许创建的最大线程数。如果队列满了，并且已创建的线程数小于最大线程数，则线程池会再创建新的线程执行任务。值得注意的是如果使用了无界的任务队列这个参数就没什么效果。
        executor.setMaxPoolSize(60);
        //队列中最大的数目
        //当核心线程数达到最大时，新任务会放在队列中排队等待执行
        executor.setQueueCapacity(650);
        //线程空闲后的最大存活时间
        //线程池的工作线程空闲后（指大于核心又小于max的那部分线程），保持存活的时间。所以如果任务很多，并且每个任务执行的时间比较短，可以调大这个时间，让空闲线程多活一会，提高线程的利用率。
        executor.setKeepAliveSeconds(60);
        //线程名称前缀
        executor.setThreadNamePrefix("@wyyjs@Executor_");
        /**
            两种情况会拒绝处理任务：
            当线程数已经达到maxPoolSize，且队列已满，会拒绝新任务。
            当线程池被调用shutdown()后，会等待线程池里的任务执行完毕再shutdown。如果在调用shutdown()和线程池真正shutdown之间提交任务，会拒绝新任务。
            线程池会调用rejectedExecutionHandler来处理这个任务。如果没有设置默认是AbortPolicy，会抛出异常。
            ThreadPoolExecutor 采用了策略的设计模式来处理拒绝任务的几种场景。
            这几种策略模式都实现了RejectedExecutionHandler接口。

            1.AbortPolicy 丢弃任务，抛运行时异常。
            2.CallerRunsPolicy 执行任务。
            3.DiscardPolicy 忽视，什么都不会发生。
            4.DiscardOldestPolicy 从队列中踢出最先进入队列（最后一个执行）的任务。
         */
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        //加载
        executor.initialize();
        return executor;

    }

}
