package com.jkga.wyyjs.utils;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.jkga.wyyjs.model.configuration.Mq1Properties;
import com.jkga.wyyjs.model.dto.AreaCarMqDTO;
import com.jkga.wyyjs.model.dto.UserTransformDTO;
import com.jkga.wyyjs.model.vo.XqConfigTableVO;
import com.rabbitmq.client.Channel;
import com.rabbitmq.client.Connection;
import com.rabbitmq.client.ConnectionFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Author：clyde
 * @Date：2025/4/7 11:14
 */
@Service
@Slf4j
public class ThirdService {

    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private RedisUtil redisUtil;

    @Value("${jkga-api.storemgmt-url}")
    private String storemgmtUrl;

    @Resource
    @Qualifier("primaryRabbitMQProperties")
    Mq1Properties primaryRabbitMQProperties;

    public JSONObject getuseridkey(String IdCard, String Name) {
        Map<String, Object> map = new HashMap<>();
        map.put("access_token", redisUtil.get("access_token"));

        JSONObject jsonParam = new JSONObject();
        jsonParam.set("name", Name);
        jsonParam.set("id_card_number", IdCard);

        HttpEntity<JSONObject> requestEntity = new HttpEntity<>(jsonParam, null);
        ResponseEntity<JSONObject> responseEntity = restTemplate.exchange("https://api.weixin.qq.com/cityservice/face/identify/getuseridkey?access_token={access_token}",
                HttpMethod.POST, requestEntity, JSONObject.class, map);
        JSONObject resultJo = responseEntity.getBody();
        log.info("getuseridkey:" + resultJo.toString());
        return resultJo;
    }


    public JSONObject getinfo(String verify_result) {
        Map<String, Object> map = new HashMap<>();
        map.put("access_token", redisUtil.get("access_token"));

        JSONObject jsonParam = new JSONObject();
        jsonParam.set("verify_result", verify_result);

        HttpEntity<JSONObject> requestEntity = new HttpEntity<>(jsonParam, null);
        ResponseEntity<JSONObject> responseEntity = restTemplate.exchange("https://api.weixin.qq.com/cityservice/face/identify/getinfo?access_token={access_token}",
                HttpMethod.POST, requestEntity, JSONObject.class, map);
        JSONObject resultJo = responseEntity.getBody();
        log.info("getinfo:" + resultJo.toString());
        return resultJo;
    }

    @Async("wyyjsExecutor")
    public void pushUserToStoremgmt(UserTransformDTO userTransformDTO) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        HttpEntity<UserTransformDTO> requestEntity = new HttpEntity<>(userTransformDTO, headers);
//        ResponseEntity<JSONObject> responseEntity = restTemplate.exchange(storemgmtUrl + "/OpenApi/syncWyyjsUser",
//                HttpMethod.POST, requestEntity, JSONObject.class);
//        JSONObject resultJo = responseEntity.getBody();
//        if (resultJo.containsKey("code") && resultJo.getIntValue("code") == 200) {
//
//        } else {
//
//        }
    }

    @Async("wyyjsExecutor")
    public void sendAreaCarDataToMq(AreaCarMqDTO areaCarMqDTO) {


        log.info("========推送了一条数据：" + JSONUtil.parseObj(areaCarMqDTO,false).toString());
        String queueName = "QUEUE_JQL_UNRELATED";
        String exchangeName = "EXCHANGE_QCB";
        String routingKey = "ROUTING_KEY_JQL_UNRELATED";

        ConnectionFactory factory = new ConnectionFactory();
        factory.setHost(primaryRabbitMQProperties.getHost());
        factory.setUsername(primaryRabbitMQProperties.getUsername());
        factory.setPassword(primaryRabbitMQProperties.getPassword());

        try (Connection connection = factory.newConnection();

             Channel channel = connection.createChannel()) {
            // 声明队列
            channel.queueDeclare(queueName, true, false, false, null);
            // 声明交换器
            channel.exchangeDeclare(exchangeName, "direct", true);
            // 绑定队列和交换器
            channel.queueBind(queueName, exchangeName, routingKey);

            // 发布消息
            String message = JSONUtil.parseObj(areaCarMqDTO, false).toString();
            channel.basicPublish(exchangeName, routingKey, new com.rabbitmq.client.AMQP.BasicProperties.Builder()
                    .contentType("text/plain")
                    .deliveryMode(2) // 2 表示持久化消息
                    .build(), message.getBytes(StandardCharsets.UTF_8));
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    /**
     * 获取车档小区配置信息
     *
     * @Author：clyde
     * @Date：2025/5/23 10:06
     */
    public List<XqConfigTableVO> getXqConfigTable() {

        HttpEntity<JSONObject> requestEntity = new HttpEntity<>(null, null);
        ResponseEntity<JSONObject> responseEntity = restTemplate.exchange("http://172.30.1.200:8314/openApi/getXqConfig",
                HttpMethod.GET, requestEntity, JSONObject.class);
        JSONObject resultJo = responseEntity.getBody();
        if (resultJo.getInt("success") == 1) {
            JSONArray jsonArray = resultJo.getJSONArray("data");
            if (jsonArray != null && jsonArray.size() > 0) {
                return JSONUtil.toList(jsonArray, XqConfigTableVO.class);
            } else {
                return null;
            }
        } else {
            return null;
        }

    }

    /**
     * 获取小区车辆档案信息
     *
     * @Author：clyde
     * @Date：2025/5/23 10:23
     */
    public JSONObject getCarList(String area_location_id, int page_num) {

        HttpHeaders headers = new HttpHeaders();

        Map param = new HashMap();
        param.put("area_location_id", area_location_id);
        param.put("page_num", page_num);
        param.put("page_size", 100);

        HttpEntity requestEntity = new HttpEntity<>(null, headers);
        ResponseEntity<JSONObject> responseEntity = restTemplate.exchange("http://172.30.1.200:8314/openApi/getCarList?area_location_id={area_location_id}&page_num={page_num}&page_size={page_size}",
                HttpMethod.GET, requestEntity, JSONObject.class, param);
        JSONObject resultJo = responseEntity.getBody();
        if (resultJo.getInt("success") == 1) {
            return resultJo.getJSONObject("data");
        } else {
            return null;
        }
    }

    public JSONObject identifyCarNo(String base64Img, String type) {

        JSONObject jsonParam = new JSONObject();
        jsonParam.set("data", base64Img);
        jsonParam.set("type", type);

        HttpEntity<JSONObject> requestEntity = new HttpEntity<>(jsonParam, null);
        ResponseEntity<JSONObject> responseEntity = restTemplate.exchange("http://172.30.1.200:8314/api/queryCarInfo",
                HttpMethod.POST, requestEntity, JSONObject.class);
        JSONObject resultJo = responseEntity.getBody();
        log.info("getinfo:" + resultJo.toString());
        return resultJo;
    }


}
