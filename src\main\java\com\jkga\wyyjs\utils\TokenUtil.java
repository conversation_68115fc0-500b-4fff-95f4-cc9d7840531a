package com.jkga.wyyjs.utils;

import cn.hutool.jwt.JWT;
import cn.hutool.jwt.JWTUtil;

import java.util.HashMap;
import java.util.Map;

/**
 * JWT 工具类
 *
 * <AUTHOR>
 */
public class TokenUtil {

    /**
     * 过期时间一周
     */
    private static final long EXPIRE_TIME = 7 * 24 * 60 * 60 * 1000;
//    private static final long EXPIRE_TIME = 60 * 1000;

    /**
     * token私钥
     */
    private static final String TOKEN_SECRET = "NzvPvs_KTUS3SkZIoQ61U8TyU1iHrArZIA4vV-Akq-I";


    /**
     * 生成json web token
     *
     * @return
     */
    public static String generate(String userId) {
        Map<String, Object> map = new HashMap<String, Object>() {
            private static final long serialVersionUID = 1L;

            {
                put("uid", userId);
                put("expire_time", System.currentTimeMillis() + EXPIRE_TIME);
            }
        };
        return JWTUtil.createToken(map, TOKEN_SECRET.getBytes());
    }

    /**
     * 验证token
     *
     * @param token
     * @return
     */
    public static boolean isTokenValid(String token) {
        boolean verifyResult = JWTUtil.verify(token, TOKEN_SECRET.getBytes());
        if (verifyResult) {
            long expireTimeMillis = (long) getPayload(token, "expire_time");
            return expireTimeMillis >= System.currentTimeMillis();
        } else {
            return false;
        }
    }


    /**
     * 获取携带值
     *
     * @param key
     * @return
     */
    public static Object getPayload(String token, String key) {
        final JWT jwt = JWTUtil.parseToken(token);
        return jwt.getPayload(key);
    }


    /**
     * 测试token生成
     *
     * @param userId
     * @return
     */
    public static String testTokenGenerate(String userId) {
        Map<String, Object> map = new HashMap<String, Object>() {
            private static final long serialVersionUID = 1L;

            {
                put("uid", userId);
                put("expire_time", System.currentTimeMillis() + 24 * 24 * 60 * 60 * 1000);
            }
        };
        return JWTUtil.createToken(map, TOKEN_SECRET.getBytes());
    }

}