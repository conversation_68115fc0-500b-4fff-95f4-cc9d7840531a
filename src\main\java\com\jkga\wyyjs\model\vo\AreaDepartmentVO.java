package com.jkga.wyyjs.model.vo;

import com.jkga.wyyjs.anno.NoQuotes;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * @Author：clyde
 * @Date：2025/2/6 15:50
 */
@Data
public class AreaDepartmentVO {

    private String id;
    @NotBlank(message = "小区不能为空")
    private String areaId;
    @NotBlank(message = "部门名称不能为空")
    @NoQuotes
    private String name;
    private Integer deleteStatus;

    private List<AreaManagerVO> areaManagerList;

}
