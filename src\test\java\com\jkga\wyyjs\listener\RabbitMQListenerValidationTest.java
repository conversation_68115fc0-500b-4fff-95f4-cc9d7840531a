package com.jkga.wyyjs.listener;

import cn.hutool.json.JSONUtil;
import com.rabbitmq.client.Channel;
import com.rabbitmq.client.Connection;
import com.rabbitmq.client.ConnectionFactory;
import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.Map;

/**
 * RabbitMQ监听器数据校验测试类
 * 用于测试各种无效数据的处理
 * 
 * @Author：clyde
 * @Date：2025/7/18
 */
public class RabbitMQListenerValidationTest {

    private static final String EXCHANGE_NAME = "EXCHANGE_QCB";
    private static final String ROUTING_KEY = "ROUTING_KEY_JQL_WYYJSADMIN";
    private static final String QUEUE_NAME = "QUEUE_JQL_WYYJSADMIN";

    /**
     * 测试发送无效JSON格式的消息
     */
    @Test
    public void testSendInvalidJsonMessage() throws Exception {
        String invalidJson = "{invalid json format";
        sendRawMessage(invalidJson);
        System.out.println("发送无效JSON消息: " + invalidJson);
    }

    /**
     * 测试发送缺少type字段的消息
     */
    @Test
    public void testSendMissingTypeMessage() throws Exception {
        Map<String, Object> message = new HashMap<>();
        message.put("area_id", "123456");
        message.put("id_card", "330400199001011234");
        message.put("user_name", "TEST");
        message.put("phone", "***********");
        message.put("position", "管理员");
        message.put("status", "0");
        message.put("update_user", "处理人");
        // 缺少type字段

        sendMessage(message);
        System.out.println("发送缺少type字段的消息");
    }

    /**
     * 测试发送无效type值的消息
     */
    @Test
    public void testSendInvalidTypeMessage() throws Exception {
        Map<String, Object> message = new HashMap<>();
        message.put("type", 2); // 无效的type值
        message.put("area_id", "123456");
        message.put("id_card", "330400199001011234");
        message.put("user_name", "TEST");
        message.put("phone", "***********");
        message.put("position", "管理员");
        message.put("status", "0");
        message.put("update_user", "处理人");

        sendMessage(message);
        System.out.println("发送无效type值的消息");
    }

    /**
     * 测试发送无效身份证格式的消息
     */
    @Test
    public void testSendInvalidIdCardMessage() throws Exception {
        Map<String, Object> message = new HashMap<>();
        message.put("type", 0);
        message.put("area_id", "123456");
        message.put("id_card", "123456789"); // 无效的身份证格式
        message.put("user_name", "TEST");
        message.put("phone", "***********");
        message.put("position", "管理员");
        message.put("status", "0");
        message.put("update_user", "处理人");

        sendMessage(message);
        System.out.println("发送无效身份证格式的消息");
    }

    /**
     * 测试发送缺少必要字段的消息
     */
    @Test
    public void testSendMissingRequiredFieldsMessage() throws Exception {
        Map<String, Object> message = new HashMap<>();
        message.put("type", 0);
        message.put("area_id", "123456");
        message.put("id_card", "330400199001011234");
        // 缺少user_name, phone, status, update_user字段

        sendMessage(message);
        System.out.println("发送缺少必要字段的消息");
    }

    /**
     * 测试物业管理员缺少area_id字段的消息
     */
    @Test
    public void testSendPropertyManagerMissingAreaIdMessage() throws Exception {
        Map<String, Object> message = new HashMap<>();
        message.put("type", 0); // 物业管理员
        // 缺少area_id字段
        message.put("id_card", "330400199001011234");
        message.put("user_name", "TEST");
        message.put("phone", "***********");
        message.put("position", "管理员");
        message.put("status", "0");
        message.put("update_user", "处理人");

        sendMessage(message);
        System.out.println("发送物业管理员缺少area_id字段的消息");
    }

    /**
     * 测试发送无效status值的消息
     */
    @Test
    public void testSendInvalidStatusMessage() throws Exception {
        Map<String, Object> message = new HashMap<>();
        message.put("type", 0);
        message.put("area_id", "123456");
        message.put("id_card", "330400199001011234");
        message.put("user_name", "TEST");
        message.put("phone", "***********");
        message.put("position", "管理员");
        message.put("status", "2"); // 无效的status值
        message.put("update_user", "处理人");

        sendMessage(message);
        System.out.println("发送无效status值的消息");
    }

    /**
     * 测试中介管理员不需要area_id字段的正常消息
     */
    @Test
    public void testSendValidIntermediaryManagerMessage() throws Exception {
        Map<String, Object> message = new HashMap<>();
        message.put("type", 1); // 中介管理员
        // 中介管理员不需要area_id字段
        message.put("id_card", "330400199001011235");
        message.put("user_name", "TEST2");
        message.put("phone", "18812341235");
        message.put("position", "管理员");
        message.put("status", "0");
        message.put("update_user", "处理人");

        sendMessage(message);
        System.out.println("发送中介管理员正常消息（不包含area_id）");
    }

    /**
     * 发送消息到RabbitMQ
     */
    private void sendMessage(Map<String, Object> message) throws Exception {
        String messageJson = JSONUtil.toJsonStr(message);
        sendRawMessage(messageJson);
    }

    /**
     * 发送原始字符串消息到RabbitMQ
     */
    private void sendRawMessage(String message) throws Exception {
        ConnectionFactory factory = new ConnectionFactory();
        factory.setHost("127.0.0.1");
        factory.setPort(5672);
        factory.setUsername("clyde");
        factory.setPassword("admin123");

        try (Connection connection = factory.newConnection();
             Channel channel = connection.createChannel()) {

            // 声明交换机
            channel.exchangeDeclare(EXCHANGE_NAME, "direct", true);

            // 声明队列
            channel.queueDeclare(QUEUE_NAME, true, false, false, null);

            // 绑定队列到交换机
            channel.queueBind(QUEUE_NAME, EXCHANGE_NAME, ROUTING_KEY);

            // 发送消息
            channel.basicPublish(EXCHANGE_NAME, ROUTING_KEY, null, message.getBytes("UTF-8"));

            System.out.println("发送消息: " + message);
        }
    }
}
