package com.jkga.wyyjs.utils;

import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Test;

/**
 * 身份证校验测试类
 * 
 * @Author：clyde
 * @Date：2025/7/18
 */
public class IdCardValidationTest {

    /**
     * 身份证号码格式校验
     * @param idCard 身份证号码
     * @return 格式是否正确
     */
    private boolean isValidIdCard(String idCard) {
        if (StringUtils.isBlank(idCard)) {
            return false;
        }

        // 18位身份证号码正则表达式
        String regex18 = "^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$";
        // 15位身份证号码正则表达式
        String regex15 = "^[1-9]\\d{5}\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}$";

        return idCard.matches(regex18) || idCard.matches(regex15);
    }

    @Test
    public void testValidIdCards() {
        // 测试有效的18位身份证
        String[] validIds18 = {
            "330400199001011234",
            "110101199003077896",
            "******************",
            "51010119900101123X",
            "510101199001011234"
        };

        System.out.println("测试有效的18位身份证:");
        for (String id : validIds18) {
            boolean result = isValidIdCard(id);
            System.out.println(id + " -> " + result);
            assert result : "应该是有效的身份证: " + id;
        }

        // 测试有效的15位身份证
        String[] validIds15 = {
            "330400900101123",
            "110101900307789",
            "***************"
        };

        System.out.println("\n测试有效的15位身份证:");
        for (String id : validIds15) {
            boolean result = isValidIdCard(id);
            System.out.println(id + " -> " + result);
            assert result : "应该是有效的身份证: " + id;
        }
    }

    @Test
    public void testInvalidIdCards() {
        // 测试无效的身份证
        String[] invalidIds = {
            "",                    // 空字符串
            null,                  // null值
            "123456789",          // 长度不够
            "12345678901234567890", // 长度过长
            "000000199001011234",  // 以0开头
            "330400199013011234",  // 无效月份
            "330400199001321234",  // 无效日期
            "330400199001011234A", // 最后一位不是数字或X
            "330400179001011234",  // 无效年份
            "abcdef199001011234"   // 包含字母
        };

        System.out.println("测试无效的身份证:");
        for (String id : invalidIds) {
            boolean result = isValidIdCard(id);
            System.out.println((id == null ? "null" : id) + " -> " + result);
            assert !result : "应该是无效的身份证: " + id;
        }
    }

    @Test
    public void testBorderCases() {
        // 测试边界情况
        String[] borderCases = {
            "330400200012311234", // 12月31日
            "330400200002291234", // 2月29日（闰年）
            "330400199002281234", // 2月28日（非闰年）
            "33040020001231123x", // 小写x
            "33040020001231123X"  // 大写X
        };

        System.out.println("测试边界情况:");
        for (String id : borderCases) {
            boolean result = isValidIdCard(id);
            System.out.println(id + " -> " + result);
        }
    }
}
