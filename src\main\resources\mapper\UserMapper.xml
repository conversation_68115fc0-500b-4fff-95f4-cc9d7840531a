<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jkga.wyyjs.mapper.UserMapper">

    <resultMap id="returnUserMap" type="com.jkga.wyyjs.model.entity.UserEntity">
        <result column="name" jdbcType="VARCHAR" property="name"
                typeHandler="com.jkga.wyyjs.component.SM4TypeHandler"/>
        <result column="phone" jdbcType="VARCHAR" property="phone"
                typeHandler="com.jkga.wyyjs.component.SM4TypeHandler"/>
        <result column="identity" jdbcType="VARCHAR" property="identity"
                typeHandler="com.jkga.wyyjs.component.SM4TypeHandler"/>
    </resultMap>

    <select id="selectUserById" resultMap="returnUserMap">
        select * from "user" where "id" = #{id}
    </select>

    <insert id="insertLoginUser">
        insert into "user"(
        "id",
        "open_id",
        "phone",
        "creator_id",
        "create_time",
        "delete_status",
        "last_login_time"
        )values(
        #{id},
        #{openId},
        #{phone,typeHandler=com.jkga.wyyjs.component.SM4TypeHandler},
        #{creatorId},
        NOW(),
        0,
        NOW()
        )
    </insert>

    <select id="selectUserByOpenid" resultMap="returnUserMap">
        select * from "user" where "open_id" = #{openid} and "delete_status" = 0
    </select>

    <update id="updateUserPhone">
        update "user" set
        "phone" = #{newPhone,typeHandler=com.jkga.wyyjs.component.SM4TypeHandler},
        "update_time" = #{nowDateTimeStr},
        "last_login_time" = #{nowDateTimeStr}
        where "id" = #{id}
    </update>

    <update id="updateUserLoginTime">
        update "user" set
        "last_login_time" = #{nowDateTimeStr}
        where "id" = #{id}
    </update>

    <update id="updateUserAuthInfo">
        update "user" set
        "name" = #{userName,typeHandler=com.jkga.wyyjs.component.SM4TypeHandler},
        "identity_type" = #{identityType},
        "identity" = #{identity,typeHandler=com.jkga.wyyjs.component.SM4TypeHandler},
        "updater_id" = #{userId},
        "update_time" = NOW()
        where "id" = #{userId}
    </update>

    <update id="updateUserFaceImg">
        update "user" set
        "face_img_url" = #{url}
        where "id" = #{userId}
    </update>

    <select id="countUserAuth" resultType="java.lang.Integer">
        select count(1) from "user" where "name" = #{name,typeHandler=com.jkga.wyyjs.component.SM4TypeHandler}
        and "identity" = #{identity,typeHandler=com.jkga.wyyjs.component.SM4TypeHandler}
        and "delete_status" = 0
    </select>

    <insert id="insertDeclareUser">
        insert into "user"(
        "id",
        "name",
        "phone",
        "identity_type",
        "identity",
        "creator_id",
        "create_time",
        "delete_status"
        )values(
        #{id},
        #{name,typeHandler=com.jkga.wyyjs.component.SM4TypeHandler},
        #{phone,typeHandler=com.jkga.wyyjs.component.SM4TypeHandler},
        #{identityType},
        #{identity,typeHandler=com.jkga.wyyjs.component.SM4TypeHandler},
        #{creatorId},
        NOW(),
        0
        )
    </insert>

    <select id="getDeclareUserByPhone" resultMap="returnUserMap">
        select * from "user" where "open_id" is null and "phone" = #{phone,typeHandler=com.jkga.wyyjs.component.SM4TypeHandler}
        and "delete_status" = 0
    </select>

    <update id="updateLoginUserOpenid">
        update "user" set
        "open_id" = #{openid},
        "updater_id" = #{id},
        "update_time" = NOW(),
        "last_login_time" = NOW()
        where "id" = #{id}
    </update>

    <select id="getUserByPhone" resultMap="returnUserMap">
        select * from "user" where "phone" = #{phone,typeHandler=com.jkga.wyyjs.component.SM4TypeHandler}
        and "delete_status" = 0 limit 1
    </select>

    <select id="countUserIdentityIsAuth" resultType="java.lang.Integer">
        select count(1) from "user" where "identity" = #{identity,typeHandler=com.jkga.wyyjs.component.SM4TypeHandler}
        and "id" != #{userId} and "delete_status" = 0
    </select>

    <select id="getUserByIdentity" resultMap="returnUserMap">
        select * from "user" where "identity" = #{identity,typeHandler=com.jkga.wyyjs.component.SM4TypeHandler}
        and "delete_status" = 0
    </select>

    <select id="getUserByIdentityOrPhone" resultMap="returnUserMap">
        select * from "user" where ("identity" = #{identity,typeHandler=com.jkga.wyyjs.component.SM4TypeHandler}
        or "phone" = #{phone,typeHandler=com.jkga.wyyjs.component.SM4TypeHandler})
        and "delete_status" = 0
    </select>

    <update id="smrzUser" parameterType="com.jkga.wyyjs.model.entity.UserEntity">
        update "user" set
        "name" = #{name,typeHandler=com.jkga.wyyjs.component.SM4TypeHandler},
        "identity_type" = #{identityType},
        "identity" = #{identity,typeHandler=com.jkga.wyyjs.component.SM4TypeHandler},
        "face_img_url" = #{faceImgUrl}
        where "id" = #{id}
    </update>

    <insert id="syncAddStoremgmtUser" parameterType="com.jkga.wyyjs.model.entity.UserEntity">
        insert into "user"(
        "id",
        "name",
        "phone",
        "identity_type",
        "identity",
        "face_img_url",
        "create_time",
        "delete_status"
        )values(
        #{id},
        #{name,typeHandler=com.jkga.wyyjs.component.SM4TypeHandler},
        #{phone,typeHandler=com.jkga.wyyjs.component.SM4TypeHandler},
        #{identityType},
        #{identity,typeHandler=com.jkga.wyyjs.component.SM4TypeHandler},
        #{faceImgUrl},
        NOW(),
        0
        )
    </insert>

    <update id="syncUpdStoremgmtUser"  parameterType="com.jkga.wyyjs.model.entity.UserEntity">
        update "user" set
        "name" = #{name,typeHandler=com.jkga.wyyjs.component.SM4TypeHandler},
        "identity_type" = #{identityType},
        "identity" = #{identity,typeHandler=com.jkga.wyyjs.component.SM4TypeHandler},
        "face_img_url" = #{faceImgUrl}
        where "id" = #{id}
    </update>

    <update id="updateUserDossierId">
        update "user" set
        "dossier_id" = #{dossierId}
        where "id" = #{id}
    </update>

    <insert id="addImportCarUser" parameterType="com.jkga.wyyjs.model.entity.UserEntity">
        insert into "user"(
        "id",
        "name",
        "phone",
        "identity_type",
        "identity",
        "face_img_url",
        "create_time",
        "delete_status",
         "dossier_id"
        )values(
        #{id},
        #{name,typeHandler=com.jkga.wyyjs.component.SM4TypeHandler},
        #{phone,typeHandler=com.jkga.wyyjs.component.SM4TypeHandler},
        #{identityType},
        #{identity,typeHandler=com.jkga.wyyjs.component.SM4TypeHandler},
        #{faceImgUrl},
        NOW(),
        0,
         #{dossierId}
        )
    </insert>

    <insert id="syncAddQcbUser" parameterType="com.jkga.wyyjs.model.entity.UserEntity">
        insert into "user"(
        "id",
        "name",
        "phone",
        "identity_type",
        "identity",
        "face_img_url",
        "create_time",
        "delete_status",
        "dossier_id"
        )values(
        #{id},
        #{name,typeHandler=com.jkga.wyyjs.component.SM4TypeHandler},
        #{phone,typeHandler=com.jkga.wyyjs.component.SM4TypeHandler},
        #{identityType},
        #{identity,typeHandler=com.jkga.wyyjs.component.SM4TypeHandler},
        #{faceImgUrl},
        NOW(),
        0,
         #{dossierId}
        )
    </insert>

    <update id="syncUpdQcbUser"  parameterType="com.jkga.wyyjs.model.entity.UserEntity">
        update "user" set
        "name" = #{name,typeHandler=com.jkga.wyyjs.component.SM4TypeHandler},
        "identity_type" = #{identityType},
        "identity" = #{identity,typeHandler=com.jkga.wyyjs.component.SM4TypeHandler},
        "face_img_url" = #{faceImgUrl},
        "dossier_id" = #{dossierId}
        where "id" = #{id}
    </update>

    <select id="getUserByDossierId" resultMap="returnUserMap">
        SELECT * FROM "user" where "dossier_id" = #{dossier_id} and "delete_status" = 0
    </select>

    <select id="getUserPermissions" resultType="string">
        SELECT
          p."code"
        FROM
          "user" u
          LEFT JOIN "user_role" ur ON u."id" = ur."user_id"
          INNER JOIN "role" r ON ur."role_id" = r."id"
          LEFT JOIN "role_permission" rp ON r."id" = rp."role_id"
          INNER JOIN "permission" p ON rp."permission_id" = p."id"
        WHERE
          u."id" = #{userId}
            <if test="area_id != null">
                AND ur."area_id" = #{area_id}
            </if>
            <if test="place_id != null">
                AND ur."place_id" = #{place_id}
            </if>
          AND u."delete_status" = 0
          AND ur."delete_status" = 0
          AND r."delete_status" = 0
          AND rp."delete_status" = 0
          AND p."delete_status" = 0
    </select>

    <select id="getUserPermissionDetails" resultType="com.jkga.wyyjs.model.entity.PermissionEntity">
        SELECT
          p.*
        FROM
          "user" u
          LEFT JOIN "user_role" ur ON u."id" = ur."user_id"
          INNER JOIN "role" r ON ur."role_id" = r."id"
          LEFT JOIN "role_permission" rp ON r."id" = rp."role_id"
          INNER JOIN "permission" p ON rp."permission_id" = p."id"
        WHERE
          u."id" = #{userId} and ur."area_id" = #{area_id}
          AND u."delete_status" = 0
          AND ur."delete_status" = 0
          AND r."delete_status" = 0
          AND rp."delete_status" = 0
          AND p."delete_status" = 0
    </select>


</mapper>