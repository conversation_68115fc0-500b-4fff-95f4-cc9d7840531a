<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jkga.wyyjs.mapper.RolePermissionMapper">

    <resultMap id="RolePermissionResultMap" type="com.jkga.wyyjs.model.entity.RolePermissionEntity">
        <id     property="id"     column="id"     />
        <result property="roleId"   column="role_id"   />
        <result property="permissionId"  column="permission_id"   />
        <result property="deleteStatus"   column="delete_status"   />
        <result property="createTime"   column="create_time"   />
    </resultMap>

    <select id="getRolePermissionList" resultMap="RolePermissionResultMap">
        select * from "role_permission" where "role_id" = #{roleId} and "delete_status" = 0
    </select>

    <delete id="delAreaRolePermission">
        delete from "role_permission" where "role_id" = #{roleId}
    </delete>

    <insert id="batchInsertAreaRolePermission">
        insert into "role_permission"("id", "role_id", "permission_id", "delete_status", "create_time") VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.id}, #{item.roleId}, #{item.permissionId}, #{item.deleteStatus}, #{item.createTime})
        </foreach>
    </insert>

</mapper>