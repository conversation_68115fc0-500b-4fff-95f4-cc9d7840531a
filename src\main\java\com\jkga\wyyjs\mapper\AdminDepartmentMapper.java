package com.jkga.wyyjs.mapper;

import com.jkga.wyyjs.model.entity.AdminDepartmentEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 管理部门Mapper接口
 * 
 * @Author：clyde
 * @Date：2025/7/18
 */
@Mapper
public interface AdminDepartmentMapper {

    /**
     * 插入部门
     */
    boolean insertAdminDepartment(AdminDepartmentEntity adminDepartmentEntity);

    /**
     * 根据名称和条件查询部门
     */
    AdminDepartmentEntity getDepartmentByName(@Param("name") String name, 
                                             @Param("areaId") String areaId, 
                                             @Param("placeId") String placeId,
                                             @Param("type") Integer type);

    /**
     * 根据ID查询部门
     */
    AdminDepartmentEntity getDepartmentById(@Param("id") String id);

    /**
     * 更新部门名称
     */
    boolean updateDepartmentName(@Param("id") String id, @Param("name") String name);

    /**
     * 删除部门（逻辑删除）
     */
    boolean delDepartment(@Param("id") String id);

    /**
     * 获取部门列表
     */
    List<AdminDepartmentEntity> getDepartmentList(@Param("managerId") String managerId, 
                                                 @Param("type") Integer type);

}
