package com.jkga.wyyjs.aspect;

import cn.hutool.json.JSONObject;
import com.jkga.wyyjs.mapper.SysOperateLogMapper;
import com.jkga.wyyjs.model.entity.SysOperateLogEntity;
import com.jkga.wyyjs.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.LocalVariableTableParameterNameDiscoverer;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;

/**
 * @Author：clyde
 * @Date：2025/2/20 16:35
 */
@Slf4j
@Service
public class AnsyRecordOperateLog {

    @Autowired
    private SysOperateLogMapper sysOperateLogMapper;


    @Async("wyyjsExecutor")
    public void saveOperateInfo(ProceedingJoinPoint joinPoint,SysOperateLogEntity logEntity, Object response) {
        log_info("============进入切面");
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();

        RecordLog recordLogAnnotation = method.getAnnotation(RecordLog.class);
        if (recordLogAnnotation != null) {
            log_info("==========操作模型：" + recordLogAnnotation.optModel());
            log_info("==========操作说明：" + recordLogAnnotation.optDesc());
            logEntity.setOptModel(recordLogAnnotation.optModel());
            logEntity.setOptDesc(recordLogAnnotation.optDesc());
        }
        // 请求的类名方法名
        String className = joinPoint.getTarget().getClass().getName();
        String methodName = signature.getName();
        log_info("======类名方法名：" + className + "." + methodName);
        logEntity.setMethod(className + "." + methodName);
        // 请求方法参数值
        Object[] args = joinPoint.getArgs();
        // 请求方法参数名称
        LocalVariableTableParameterNameDiscoverer u = new LocalVariableTableParameterNameDiscoverer();
        String[] paramNames = u.getParameterNames(method);
        if (args != null && paramNames != null) {
            JSONObject params = new JSONObject();
            for (int i = 0; i < args.length; i++) {
                params.set(paramNames[i], args[i]);
            }
            //操作请求参数
            log_info("==========请求参数：" + params.toString());
            logEntity.setRequestData(params.toString());
        }


        if (response != null) {
            logEntity.setResponseData(response.toString());
        }

        // 保存系统操作日志
        sysOperateLogMapper.saveSysOperateLog(logEntity);

    }

//    public HttpServletRequest getRequest(){
//       return  ((ServletRequestAttributes) (RequestContextHolder.currentRequestAttributes())).getRequest();
//    }


    public void log_info(String data) {
//        log.info(data);
    }

}
