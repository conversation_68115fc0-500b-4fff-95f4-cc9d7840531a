package com.jkga.wyyjs.config;

import com.jkga.wyyjs.model.configuration.Mq1Properties;
import org.springframework.amqp.rabbit.connection.CachingConnectionFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * @Author：clyde
 * @Date：2025/1/13 11:04
 */
@Configuration
public class RabbitMQConnectionConfig {

    // 第一个连接工厂
    @Bean("primaryConnectionFactory")
    @Primary
    public CachingConnectionFactory primaryConnectionFactory(
            @Qualifier("primaryRabbitMQProperties") Mq1Properties primaryRabbitMQProperties) {
        CachingConnectionFactory connectionFactory = new CachingConnectionFactory();
        connectionFactory.setHost(primaryRabbitMQProperties.getHost());
        connectionFactory.setPort(primaryRabbitMQProperties.getPort());
        connectionFactory.setUsername(primaryRabbitMQProperties.getUsername());
        connectionFactory.setPassword(primaryRabbitMQProperties.getPassword());
        return connectionFactory;
    }

//    // 第二个连接工厂
//    @Bean("secondaryConnectionFactory")
//    public CachingConnectionFactory secondaryConnectionFactory(
//            @Qualifier("secondaryRabbitMQProperties") Mq2Properties secondaryRabbitMQProperties) {
//        CachingConnectionFactory connectionFactory = new CachingConnectionFactory();
//        connectionFactory.setHost(secondaryRabbitMQProperties.getHost());
//        connectionFactory.setPort(secondaryRabbitMQProperties.getPort());
//        connectionFactory.setUsername(secondaryRabbitMQProperties.getUsername());
//        connectionFactory.setPassword(secondaryRabbitMQProperties.getPassword());
//        return connectionFactory;
//    }


}
